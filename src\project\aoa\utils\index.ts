import { TimeType } from '/@aoa/api/types';
import dayjs, { Dayjs } from 'dayjs';
import { getFactoryId } from './factory';
import { isEmpty as _isEmpty, flatMap, omit } from 'lodash-es';
import { CallBack } from '/@aoa/utils/types';

import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');

const filterSenceData = (arr: any[]) => {
  arr = arr?.map((item: any) => {
    return (
      item?.data?.map((val: any) => {
        return val?.value || val;
      }) ||
      item?.value ||
      item
    );
  });
  arr = arr?.flat() || [];
  arr = arr.filter((item: any) => !isNaN(Number(item)));
  return arr;
};

// 此方式替代Math.max 防止堆栈溢出 > 10万
const safeMax = (arr) => {
  let max = arr[0];
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] > max) max = arr[i];
  }
  return max;
};

// 此方式替代Math.min 防止堆栈溢出 > 10万
const safeMin = (arr) => {
  let min = arr[0];
  for (const num of arr) {
    if (num < min) min = num;
  }
  return min;
};

// 获取场景资源多个指标数据的最大值
export const getMaxValue = (arr: any[], addRange = true, normal = 1) => {
  arr = filterSenceData(arr);
  if (arr?.length) {
    let max = safeMax(arr);
    if (addRange) {
      // max = max ? (max / 8 < 1 ? Math.ceil((max / 8) * 10) : Math.ceil(max / 8) * 10) : normal;
      max = max ? max * 1.1 : normal;
    }
    return max;
  }
  return normal;
};

// 获取场景资源多个指标数据的最小值
export const getMinValue = (arr: any[], addRange = true, normal = 0) => {
  arr = filterSenceData(arr);
  if (arr?.length) {
    let min = safeMin(arr);
    if (addRange) {
      // min = min ? Math.floor(min / 12) * 10 : normal;
      min = min && min < 1 ? min / 2 : min ? min / 1.1 : normal;
    }
    return min;
  }
  return normal;
};

/**
 * 判断数组是否为空
 * @param {Array} data 数组
 * @param {string | CallBack} callback 回调函数
 * @returns boolean
 */
export function isEmpty(data: Recordable[], callback?: string | CallBack) {
  const newData = data.map((item) => {
    let cb = (item) => item;

    if (typeof callback === 'string') {
      cb = (item) => item[callback];
    } else if (typeof callback === 'function') {
      cb = callback;
    }

    return cb(item);
  });

  const result = newData.every((item) => _isEmpty(item));

  return result;
}

/**
 * 生成随机数
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns number
 */
export function radomNumber(min, max) {
  return Number.parseInt(Math.floor(Math.random() * (max - min + 1) + min) + '');
}

/**
 * 禁用今天后面的日期
 * @param {string} current 日期
 * @returns boolean
 */
export function disabledAfterToday(current: Dayjs) {
  return current && current > dayjs().endOf('day');
}

function mathMultiply(arg1, arg2) {
  let m = 0;
  const s1 = arg1.toString();
  const s2 = arg2.toString();
  try {
    m += s1.split('.')[1].length; // 小数相乘，小数点后个数相加
  } catch (e) {}
  try {
    m += s2.split('.')[1].length;
  } catch (e) {}
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
}

/**
 * 获取数组中的最大值
 * @param {Array} arr 数组
 * @param {number} weight 砝码
 * @returns number
 */
export function getMaxValueArr(arr: number[][], weight = 0, fixed = 4) {
  const newArr = arr.map((item) => {
    return Math.max(...item);
  });

  return Number(mathMultiply(Math.max(...newArr), 1 + weight).toFixed(fixed));
}

/**
 * 获取数组中的最小值
 * @param {Array} arr 数组
 * @param {number} weight 砝码
 * @returns number
 */
export function geMinValueArr(arr: number[][], weight = 0, fixed = 4) {
  const newArr = arr.map((item) => {
    return Math.min(...item);
  });

  return Number(mathMultiply(Math.min(...newArr), 1 - weight).toFixed(fixed));
}

export const getChartsGrid = (uniqUnit: Recordable[], max, offset = 25) => {
  const maxLenth = (max || 2).toString().length - 1;
  const offsetLeft = (uniqUnit.length < 3 ? 0 : Math.ceil(uniqUnit.length / 2)) * offset;
  const offsetRight = (uniqUnit.length < 3 ? 0 : Math.floor(uniqUnit.length / 2)) * offset;

  const gridLeft = 5 + 2 * maxLenth + offsetLeft;
  const gridRight = 5 + 2 * maxLenth + offsetRight;
  return {
    gridLeft,
    gridRight,
  };
};

export const colorList = [
  '#009CF0',
  '#00D1C3',
  '#8CC6CE',
  '#1ACE32',
  '#DBD118',
  '#D518F6',
  '#E78FFF',
  '#6355FF',
];

export const chunkArray = (arr, chunkSize = 3) => {
  return arr.reduce((acc, _, i) => {
    if (i % chunkSize === 0) {
      acc.push(arr.slice(i, i + chunkSize));
    }
    return acc;
  }, []);
};

export const insetData = (data, timeType, comparison) => {
  const result = data;
  if (timeType !== TimeType.Month || !comparison) return result;

  if (data?.time.length) {
    const lastTime = data.time[data.time.length - 1];
    // 获取天
    const day = dayjs(lastTime).format('DD');
    const isDay = dayjs(lastTime).format('HH:mm:ss') === '00:00:00';
    if (Number(day) < 31) {
      const number = 31 - Number(day);
      if (isDay) {
        const timeData = Array.from({ length: number }).map((_, index) => {
          const itemDay = Number(day) + index + 1;
          return `${dayjs(lastTime).format('YYYY-MM')}-${
            itemDay < 10 ? '0' + itemDay : itemDay
          } 00:00:00`;
        });
        const dataData = Array.from({ length: number }).map(() => '');

        result.time = [...data.time, ...timeData];
        result.datas.forEach((item) => {
          item.data = [...item.data, ...dataData];
        });
      } else {
        const timeData = Array.from({ length: number })
          .map((_, index) => {
            const itemDay = Number(day) + index + 1;
            return Array.from({ length: 24 }).map((_, idx) => {
              return `${dayjs(lastTime).format('YYYY-MM')}-${
                itemDay < 10 ? '0' + itemDay : itemDay
              } ${idx < 10 ? '0' + idx : idx}:00:00`;
            });
          })
          .flat();
        const dataData = Array.from({ length: timeData.length }).map(() => '');
        result.time = [...data.time, ...timeData];
        result.datas.forEach((item) => {
          item.data = [...item.data, ...dataData];
        });
      }
    }
  }
  console.log('result', result);
  return result;
};

export const getParams = (
  indexCode: {
    resourceInterfaceId: string;
    groupCode: string;
    name: string;
    jsConvert?: boolean;
  },
  startDateTime: string | null = null,
  endDateTime: string | null = null,
  huanBiFlag = false,
  tongBiFlag = false,
) => {
  const factoryId = getFactoryId();
  const params = {
    startDateTime,
    endDateTime,
    indexCodes: '@￥Resource',
    tenantId: '@￥TenantId',
    factoryId,
    times:
      huanBiFlag || tongBiFlag
        ? [dayjs(startDateTime).valueOf(), dayjs(endDateTime).valueOf()]
        : undefined,
    huanBiFlag: huanBiFlag ?? undefined,
    tongBiFlag: tongBiFlag ?? undefined,
    dataType: huanBiFlag || tongBiFlag ? 2 : undefined,
    guding: huanBiFlag || tongBiFlag ? true : undefined,
  };
  const paramData = {
    ...indexCode,
    paramsData: JSON.stringify(params),
  };
  return paramData;
};

export const flattenTree = (tree) => {
  if (!tree || !tree.length) [];
  return flatMap(tree, (node) => {
    if (node.children) {
      return [omit(node, 'children'), ...flattenTree(node.children)];
    }
    return [node];
  });
};
export const mockTime = () => {
  return Array.from({ length: 10 }, (_, index) => `0${index}时`);
};

interface AlarmData {
  id: string;
  title: string;
  warnValue: string;
  limitValue: string;
  warnEventLevel: number;
  creationTime: string;
  indicatorCode: string;
}
export const getIndexCodeLevel = (indexCode: string, dataList: AlarmData[]): number => {
  const currentData = dataList.find((item) => item.indicatorCode === indexCode);
  if (!currentData) {
    return 0;
  }
  return currentData.warnEventLevel;
};
