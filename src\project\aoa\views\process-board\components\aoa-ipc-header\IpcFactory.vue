<template>
  <div :class="prefixCls">
    <div class="icon">
      <img :src="img" />
    </div>
    <div class="factory-select" v-if="menuList?.length">
      <Select
        v-model:value="moduleId"
        :bordered="false"
        placeholder="暂无水厂"
        :dropdownStyle="{
          padding: '4px',
        }"
        popupClassName="dropdown-ipc-factory-select"
        :dropdownMatchSelectWidth="false"
        @change="changeMenu"
      >
        <SelectOption v-for="item in menuList" :key="item.moduleId" :value="item.moduleId">
          {{ item.name }}
        </SelectOption>
        <template #suffixIcon>
          <Icon class="select-suffix-icon" icon="icon-park-outline:down" :size="20" />
        </template>
      </Select>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { Select, SelectOption } from 'ant-design-vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useUserStore } from '/@/store/modules/user';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { usePermission } from '/@/hooks/web/usePermission';
  import img from '/@aoa/assets/images/ipc_main_icon.png';
  import { flattenTree } from '/@/utils';

  const { prefixCls } = useDesign('aoa-ipc-factory');

  const permissionStore = usePermissionStore();
  const { refreshMenu } = usePermission();
  const userStore = useUserStore();
  const userInfo = computed(() => userStore.getUserInfo);
  const factoryInfoList = computed(() => userInfo.value.factoryInfoList || []);
  const moduleId = computed(() => permissionStore.getFirstMenuParams?.moduleId);

  const menuList = computed(() => {
    return permissionStore.getFirstMenuList || [];
  });

  const changeMenu = async (value) => {
    let redirect: string | undefined = '';
    const currentMenu = menuList.value.find((item) => item.moduleId === value);
    const flatAllMenuList = flattenTree(currentMenu?.children);
    if (currentMenu?.redirect) {
      const __redirect = flatAllMenuList.find((item) => item.path === redirect);
      if (__redirect) {
        redirect = currentMenu.redirect;
      } else {
        const currentRedirect = flatAllMenuList.find((item) => item.redirect)?.redirect;
        redirect = currentRedirect;
      }
    }

    await permissionStore.setFirstMenuParams({
      moduleId: value,
      redirect,
      title: currentMenu?.name,
      icon: currentMenu?.quickNavigationIcon,
    });

    const factory = factoryInfoList.value.find((item) => item.moduleId === value)?.factoryId;
    userStore.setFactoryId(factory);
    userStore.setGLobalSource({ factoryId: factory });

    await refreshMenu(true, true);
  };
</script>

<style lang="less" scoped>
  @import '/@aoa/assets/css/font.less';

  @prefix-cls: ~'@{namespace}-aoa-ipc-factory';

  .@{prefix-cls} {
    display: flex;
    align-items: center;

    .icon {
      width: 40px;
      height: 40px;

      img {
        width: 100%;
      }
    }

    .factory-select {
      :deep(.ant-select) {
        .ant-select-selector {
          font-size: 20px;
          font-family: Alimama ShuHeiTi;
          z-index: 1;
        }

        .ant-select-arrow {
          font-family: Alimama ShuHeiTi;
          color: #333;
          font-size: 24px;
          width: 24px;
          height: 24px;
          margin-top: -12px;
          inset-inline-end: 2px;
        }
      }
    }
  }
</style>
