import { FormSchema } from '/@/components/Form';
import { useUserStore } from '/@/store/modules/user';

const userInfo = useUserStore().getUserInfo;
// 手机验证码表单配置
export const getPhoneVerificationSchemas = (): FormSchema[] => [
  {
    field: 'phoneNumber',
    label: '手机号',
    component: 'Input',
    required: true,
    labelWidth: 100,
    componentProps: {
      placeholder: '请输入手机号',
      maxlength: 11,
      disabled: true,
      allowClear: false,
    },
    defaultValue: userInfo.phone,
    rules: [
      {
        required: true,
        message: '请输入手机号',
      },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号格式',
      },
    ],
  },
  {
    field: 'verificationCode',
    label: '验证码',
    labelWidth: 100,
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入验证码',
      maxlength: 4,
    },
    rules: [
      {
        required: true,
        message: '请输入验证码',
      },
      {
        pattern: /^\d{4}$/,
        message: '验证码必须是4位数字',
      },
    ],
    slot: 'verificationCodeSlot',
  },
];
