import { cloneDeep } from 'lodash-es';

/**
 * @param {Array} fixParams 固定参数
 * @param {Function} cb 回调函数
 */
export function updatePropData(fixParams, cb) {
  const data = fixParams[0];
  const emits = fixParams[1];
  const eventName = fixParams[2];
  const rawData = cloneDeep(data);
  cb(rawData);

  emits(eventName, rawData);
}

// 生产，生产下控套件添加指标使用
export function getAddExpressionData(i, initData) {
  return {
    ...initData,
    expression: `getV('${i.code}')`,
    // expression: ``,
    unit: i.unit,
    displayName: i.name,
    upperLimits: '',
    lowerLimits: '',
    // upperLimits: '10',
    // lowerLimits: '1',
    code: i.code,
    groupCode: i.groupCode,
    resourceInterfaceId: i.resourceInterfaceId,
  };
}
