<template>
  <BiCardBox title="进水" direction="right">
    <template #title-slot>
      <AiCard
        :questionTemplate="question"
        questionTitle="进水"
        type="thickness"
        :deepThinking="deepThinking"
      />
    </template>
    <template #content>
      <div class="water-content">
        <div
          :class="['water-item', index % 2 ? 'yesterday' : 'today']"
          v-for="(item, index) in data"
          :key="index"
        >
          <div class="cyclinder-wrap">
            <div class="inner">
              <div class="center" :style="{ height: getHeight(item.value, data) }">
                <img
                  class="assist1"
                  v-if="item.value && Number(item.value) > 0"
                  :src="index % 2 ? cylinder2_top : cylinder1_top"
                />
                <img
                  class="assist2"
                  v-if="item.value && Number(item.value) > 0"
                  :src="index % 2 ? cylinder2_center : cylinder1_center"
                />
                <div
                  :class="['info-box', { 'info-box-center': getHeight(item.value, data) === '0%' }]"
                >
                  <div class="value-unit">
                    <div class="value">{{
                      item.value !== '' && item.value !== null
                        ? formatNumber(Number(item.value), item.digit).num
                        : '-'
                    }}</div>
                    <span class="level">{{
                      formatNumber(Number(item.value), item.digit).level
                    }}</span>
                    <div class="unit">{{
                      item.value !== '' && item.value !== null ? item.unit : ''
                    }}</div>
                  </div>
                  <div class="name">{{ item.name }}</div>
                </div>
              </div>
            </div>
            <div class="bottom" v-if="item.value && Number(item.value) > 0">
              <img :src="index % 2 ? cylinder2_bottom : cylinder1_bottom" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </BiCardBox>
</template>

<script lang="ts" setup>
  import { computed, PropType } from 'vue';
  import { BiCardBox } from './BiCard';
  import type { DataList } from '../type';
  import { formatNumber } from '/@aoa/utils/number';
  import { AiCard } from '/@aoa/components/AiCard';

  import cylinder1_top from '/@aoa/views/BI/assets/images/cylinder1_top.png';
  import cylinder1_center from '/@aoa/views/BI/assets/images/cylinder1_center.png';
  import cylinder1_bottom from '/@aoa/views/BI/assets/images/cylinder1_bottom.png';
  import cylinder2_top from '/@aoa/views/BI/assets/images/cylinder2_top.png';
  import cylinder2_center from '/@aoa/views/BI/assets/images/cylinder2_center.png';
  import cylinder2_bottom from '/@aoa/views/BI/assets/images/cylinder2_bottom.png';

  const props = defineProps({
    data: {
      type: Array as PropType<DataList[]>,
      default: () => [],
    },
    deepThinking: {
      type: Boolean,
      default: false,
    },
    aiQuestionTemplate: {
      type: String,
      default: '',
    },
  });

  const getHeight = (val, data) => {
    if (
      !data ||
      !data.length ||
      data.every((item: any) => item.value === null || item.value === '')
    ) {
      return `0%`;
    }

    const max = Math.max(...data.map((item) => Number(item.value)));
    if (!val) {
      return `0%`;
    }
    if (Number(val) === max) {
      return `90%`;
    } else {
      const ratio = Number(val) / max;
      return ratio < 0.6 ? `60%` : `${ratio * 90}%`;
    }
  };

  const getQuestion = (data) => {
    if (!props.aiQuestionTemplate || !props.aiQuestionTemplate.includes('${data}')) {
      return props.aiQuestionTemplate;
    }
    const str = data
      .map((item, index) => {
        return `${index === 0 ? '生化池进水量' : item.name}${
          item.value !== '' && item.value !== null
            ? formatNumber(Number(item.value), item.digit).num
            : '-'
        }${
          item.value !== '' && item.value !== null
            ? formatNumber(Number(item.value), item.digit).level
            : ''
        }${item.unit}`;
      })
      .join('、');
    return props.aiQuestionTemplate.replace('${data}', str);
  };

  const question = computed(() => {
    const data = props.data;
    return getQuestion(data);
  });
</script>
<style lang="less" scoped>
  .water-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 170px;
    gap: 0 30px;

    .water-item {
      position: relative;
      width: calc(154px * 0.9);
      height: calc(157px * 0.9);
      background-image: url('/@aoa/views/BI/assets/images/cylinder1_main_bg.png');
      background-size: cover;
      transform-origin: right center;

      &.yesterday {
        background-image: url('/@aoa/views/BI/assets/images/cylinder2_main_bg.png');
        transform-origin: left center;
      }

      .cyclinder-wrap {
        position: absolute;
        left: calc((154px - 93px) / 2 * 0.9);
        width: calc(93px * 0.9);
        height: calc(100% - 19px * 0.9);
        top: 0;

        .info-box {
          position: relative;
          top: 20px;
          z-index: 100;

          &.info-box-center {
            top: calc(-50px * 0.9);
          }

          .value-unit {
            display: flex;
            align-items: flex-end;
            justify-content: center;
            line-height: 1;

            .value {
              font-family: Alimama ShuHeiTi;
              font-weight: 500;
              font-size: 18px;
              line-height: 1;
              text-shadow: 1px 1px 2px #04111e;
            }

            .level {
              font-family: Alimama ShuHeiTi;
              font-weight: 500;
              font-size: 16px;
              line-height: 18px;
              text-shadow: 1px 1px 2px #04111e;
            }

            .unit {
              padding-left: 4px;
              margin-bottom: 1px;
              font-size: 14px;
              text-shadow: 1px 1px 2px #06261f;
            }
          }

          .name {
            padding-top: calc(7px * 0.9);
            font-size: 14px;
            line-height: 1;
            text-align: center;
          }
        }

        .inner {
          position: absolute;
          width: 100%;
          height: calc(100% - 18px / 2 * 0.9 - 16px * 0.9);
          left: 0;
          bottom: calc(16px / 2 * 0.9);

          .center {
            position: absolute;
            width: 100%;
            height: 0;
            bottom: 0;
            left: 0;
            transition: height 0.3s;

            .assist1 {
              position: absolute;
              left: 0;
              top: 0;
              z-index: 1;
            }

            .assist2 {
              position: absolute;
              height: calc(100% - 16px / 2 * 0.9);
              width: 100%;
              left: 0;
              bottom: 0;
            }
          }
        }

        .bottom {
          position: absolute;
          width: 100%;
          height: calc(16px * 0.9);
          bottom: 0;
          left: 0;
          z-index: 2;

          img {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
          }
        }
      }
    }
  }
  @media screen and (min-width: 2000px) {
    .water-content {
      .px2vw(30);
      gap: 0 @vw;

      .water-item {
        position: relative;
        .width-prop(154);
        .height-prop(157);

        .cyclinder-wrap {
          position: absolute;
          .width-prop(30.5, left);
          .width-prop(93);
          .height-prop(138);

          .info-box {
            .height-prop(30, top);

            &.info-box-center {
              .height-prop(-30, top);
            }

            .value-unit {
              .value {
                .font-size(20);
              }

              .level {
                .font-size(18);
                line-height: 1;
              }

              .unit {
                .font-size(14);
              }
            }

            .name {
              .font-size(16);
            }
          }

          .inner {
            .px2vh(17);
            position: absolute;
            width: 100%;
            height: calc(100% - @vh);
            left: 0;
            .height-prop(8, bottom);

            .center {
              .assist2 {
                .px2vh(8);
                height: calc(100% - @vh);
              }
            }
          }

          .bottom {
            .height-prop(16);
          }
        }
      }
    }
  }
</style>
