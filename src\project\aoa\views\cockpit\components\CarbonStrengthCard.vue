<template>
  <CockpitCardBox title="碳强度">
    <template #title-slot>
      <div class="title-container">
        <div class="title-slot">
          <Tooltip placement="topLeft" overlayClassName="aoa3-tooltip">
            <template #title>
              碳强度指标，反映能源利用效率与低碳化水平，特指​“处理单位体积污水所直接或间接产生的温室气体排放量”，是评估污水处理厂环境绩效的核心参数。<br />
              计算公式：碳强度= 碳排放量 / 处理水量
            </template>
            <Icon icon="icon-park-solid:help" size="18" />
          </Tooltip>
        </div>
        <AiCard :questionTemplate="question" questionTitle="碳强度" :deepThinking="deepThinking" />
      </div>
    </template>
    <template #content>
      <div class="carbon-strength-content">
        <div class="chart-box" v-if="first">
          <div ref="chartRef" class="chart w-full h-full"></div>
        </div>
        <div class="empty-container w-full h-full" v-else>
          <Empty />
        </div>
      </div>
    </template>
  </CockpitCardBox>
</template>

<script lang="ts" setup>
  import { ref, PropType, watch, computed } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import dayjs from 'dayjs';
  import { CockpitCardBox, Empty } from './CockpitCard';
  import { useECharts } from '/@/hooks/web/useECharts';
  import type { ChartData } from '../type';
  import { getScaleValByClientWidth } from '../data';
  import { lineChartIcon2 } from '/@aoa/views/cockpit/data';
  import { AiCard } from '/@aoa/components/AiCard';

  const props = defineProps({
    data: {
      type: Array as PropType<ChartData[]>,
      default: () => [],
    },
    first: Boolean,
    deepThinking: {
      type: Boolean,
      default: false,
    },
    aiQuestionTemplate: {
      type: String,
      default: '',
    },
  });

  const chartRef = ref(null);
  const { setOptions } = useECharts(chartRef as any);

  const getXAxisData = (data: ChartData[]) => {
    if (data.length === 0) return [];
    return data[0].data?.map((item) => item.collectDateTime);
  };

  const getMax = () => {
    // console.log(data);
    // const dataArr = data
    //   .map((item) => item.data)
    //   .flat()
    //   .map((item) => item.value)
    //   .filter((item) => item !== null);
    // return dataArr.length <= 0 ? 1 : Math.max(...dataArr);
    return 0.5;
  };

  const getMin = () => {
    // console.log(data);
    // const dataArr = data
    //   .map((item) => item.data)
    //   .flat()
    //   .map((item) => item.value)
    //   .filter((item) => item !== null);
    // return Math.min(...dataArr);
    return 0;
  };

  const getValueFormatter = (value, digit = 2) => {
    return Number(value).toFixed(digit);
  };

  const getSeriesData = (data: ChartData[]) => {
    const setiseData = data.map((item) => {
      return {
        name: item.name,
        type: 'line',
        symbol: lineChartIcon2,
        symbolSize: 12,
        smooth: true,
        data: item.data?.map((item) => item.value),
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(252, 208, 86, 0.4)',
              },
              {
                offset: 1,
                color: 'rgba(252, 208, 86, 0)',
              },
            ],
          },
        },
        itemStyle: {
          color: 'rgba(252, 208, 86, 1)',
        },
      };
    });
    return setiseData.flat();
  };

  const setChart = () => {
    const { data } = props;

    let max = getMax();
    let min = getMin();

    const options = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#E1F3F1',
        borderColor: '#02695E',
        appendToBody: true,
        formatter: (params) => {
          return `<span style="font-size: 0.73vw; line-height: 1.5;">   
            ${Number(dayjs(params[0].axisValue).format('MM')) + '月'}
            </span></br>
          ${params
            .map((item, index) => {
              return `
            ${item.marker}&nbsp;<span style="font-size: 0.73vw;">${
                item.seriesName
              }</span>&nbsp;<span >${
                item.value !== '' && item.value !== null && item.value !== undefined
                  ? `<span style="font-weight: 600;font-size: 0.73vw;">${getValueFormatter(
                      item.value,
                      data[index].digit,
                    )} </span>&nbsp;<span style="font-weight: 600;font-size: 0.73vw;">${
                      data[index]?.unit
                    }</span>`
                  : '-'
              }</span>&nbsp;
            `;
            })
            .join('</br>')}
          `;
        },
      },
      legend: {
        show: false,
      },
      grid: {
        left: 8,
        right: 8,
        bottom: 0,
        top: getScaleValByClientWidth(35),
        containLabel: true,
        show: true,
        backgroundColor: 'rgba(2, 53, 32, 0.40)',
        borderWidth: 0,
      },
      xAxis: {
        type: 'category',
        data: getXAxisData(data),
        boundaryGap: false,
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          padding: [0, 10, 0, 0],
          formatter: (value) => {
            return Number(dayjs(value).format('MM')) + '月';
          },
        },
        axisTick: {
          show: false,
        },
        axisPointer: {
          show: true,
          type: 'line',
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            type: [5, 10],
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            shadowColor: 'rgba(178, 255, 241, 1)',
            shadowOffsetY: -2,
            shadowOffsetX: 0,
            shadowBlur: 5,
            width: 1,
          },
        },
      },
      yAxis: {
        type: 'value',
        name: data[0]?.name,
        max,
        min,
        interval: (max - min) / 5,
        nameTextStyle: {
          fontSize: getScaleValByClientWidth(13),
          color: '#fff',
          align: 'left',
          padding: [0, 0, 0, getScaleValByClientWidth(-25)],
        },
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          formatter: (value) => getValueFormatter(value, 1),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A1D0C4',
            type: [5, 10],
            offset: 5,
          },
        },
      },
      series: getSeriesData(data),
    };

    setOptions(options as any, false);
  };

  watch(
    () => props.data,
    () => {
      setChart();
    },
    { deep: true },
  );

  const getQuestion = (data) => {
    if (!props.aiQuestionTemplate || !props.aiQuestionTemplate.includes('${data}')) {
      return props.aiQuestionTemplate;
    }
    if (!data.length) {
      return '';
    }
    const str = data[0]?.data
      .map((item) => {
        return `${Number(dayjs(item.collectDateTime).format('MM'))}月${
          item.value !== null && item.value !== undefined
            ? Number(item.value).toFixed(data[0].digit ?? 2)
            : '-'
        }${data[0].unit}`;
      })
      .join('、');
    return props.aiQuestionTemplate.replace('${data}', str);
  };

  const question = computed(() => {
    const data = props.data;
    return getQuestion(data);
  });
</script>
<style lang="less" scoped>
  @media screen and (min-width: 1800px) {
    .carbon-strength-content {
      .px2vw(6);
      padding: @vw;
    }
  }

  .title-container {
    display: flex;
    align-items: center;
    .width-prop(5, margin-left);
  }

  .title-slot {
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    line-height: 1;

    &:hover {
      color: #fff;
    }
  }

  .right-box {
    padding-right: 8px;
    display: flex;
    align-items: center;

    :deep(.ant-picker) {
      .width-prop(91) !important;
      height: 28px;
      background: rgba(12, 80, 68, 0.56);
      border: 1px solid rgba(31, 195, 164, 0.72);
      padding: 0 8px;

      @media screen and (min-width: 2000px) {
        .px2vw(28);
        height: @vw;
      }

      .ant-picker-input {
        & > input {
          color: #fff;
          .font-size(13);
          .height-prop(28,line-height);
        }
      }

      .ant-picker-suffix {
        color: #fff;

        .anticon {
          .font-size(14);
        }
      }
    }
  }

  .carbon-strength-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .statistics-box {
      padding: 8px;
      display: flex;
      gap: 0 8px;
      justify-content: center;
      align-items: center;

      .statistics-item {
        padding: 0 4px;
        width: 50%;
        height: 36px;
        background: rgba(2, 53, 32, 0.4);
        border-radius: 4px;
        border: 1px solid rgba(70, 226, 196, 0.4);
        display: flex;
        align-items: center;
        justify-content: space-between;

        .name {
          font-size: 13px;
          line-height: 1;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .value-unit {
          display: flex;
          align-items: center;
          gap: 0 2px;

          .value {
            font-family: Alimama ShuHeiTi;
            font-weight: 500;
            font-size: 14px;
            line-height: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            flex: 1;

            .value-level {
              font-size: 13px;
            }
          }

          .unit {
            font-size: 13px;
            line-height: 1;
          }
        }
      }
    }

    .chart-box {
      // height: calc(100% - 48px);
      flex: 1;
      width: 100%;
    }
  }
</style>

<style lang="less">
  .ant-picker-dropdown.aoa3-picker-dropdown {
    .font-size(14);

    .ant-picker-header {
      & > button {
        .font-size(14);
        .px2vw(40);

        line-height: @vw;
      }
    }

    .ant-picker-super-prev-icon,
    .ant-picker-super-next-icon {
      .px2vw(7);
      .width-prop(7);

      height: @vw;

      &::before,
      &::after {
        .px2vw(7);
        .width-prop(7);

        height: @vw;
      }
    }

    .ant-picker-header-view {
      .px2vw(40);

      height: @vw;

      button {
        line-height: @vw;
      }
    }

    .ant-picker-month-panel {
      .width-prop(280);

      .ant-picker-content {
        .px2vw(264);

        height: @vw;
      }

      .ant-picker-cell {
        .px2vw(4);

        padding: @vw 0;

        &::before {
          .px2vw(24);

          height: @vw;
        }
      }

      .ant-picker-cell-inner {
        .width-prop(60);
        .px2vw(24);

        line-height: @vw;
        height: @vw;
      }
    }
  }
</style>
