@modal-prefix-cls: ~'@{namespace}-basic-modal';

.ant-modal-wrap {
  display: flex;
  align-items: center;
  justify-content: center;

  .ant-modal-content {
    background-color: @aoa3-join-from-bg;
  }

  &.MapModal {
    .ant-modal-body .scrollbar__view > div {
      padding-top: 0 !important;
    }
  }
  &:not(.@{modal-prefix-cls}) {
    .ant-modal {
      top: 0;

      &-body {
        padding: 0 !important;
        width: 100%;
        overflow: hidden;
      }

      &-content {
        padding: 0 24px;
      }

      &-close {
        top: 16px;
        right: 16px;
        width: 32px;
        height: 32px;
        color: rgb(102 102 102);
      }

      &-close-x {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        font-size: 20px;

        &:hover {
          background-color: @theme-aoa3-color-16p;
        }

        .anticon-close {
          margin-right: 0;
        }
      }

      &-confirm-body {
        &:not(:has(.ant-modal-confirm-title)) {
          padding-top: 24px;

          .ant-modal-confirm-content {
            margin-block-start: 0 !important;
          }
        }
      }

      .ant-modal-confirm-title {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding-right: 25px;
        line-height: 64px;
      }

      .anticon-info-circle,
      .anticon-exclamation-circle {
        color: #fc7c22 !important;
        margin-inline-end: 8px;
        font-size: 18px;
      }

      &-confirm-content {
        color: @text-color-bold !important;
        max-width: unset !important;
        margin-inline-start: 0 !important;
        margin-block-start: 4px !important;
      }

      &-confirm-btns {
        margin: 20px 0 24px;

        button {
          margin-inline-start: 16px !important;
          height: 32px;
          padding: 0 22px;
          word-spacing: -2px;

          &:focus-visible {
            margin-right: 3px;
          }
        }
      }
    }
  }

  .vben-basic-table-form-container {
    padding: 0;
  }

  .ant-form,
  .ant-table-wrapper {
    margin: 0;
    padding: 0;
    background-color: transparent !important;

    .ant-table-tbody td,
    .ant-table {
      background-color: transparent !important;
    }
  }

  &.aoa3 {
    .ant-modal-header {
      background-color: @aoa3-join-from-bg;
    }

    .ant-modal-content {
      background-color: @aoa3-join-from-bg;
    }

    .ant-input-number-disabled {
      background-color: @input-bg-disabled-aoa3;
      border-color: transparent;
      color: #333;
    }
  }
}
