<template>
  <div>
    <BasicModal
      :canFullscreen="false"
      @cancel="handleCancel"
      v-bind="$attrs"
      @register="registerModal"
      width="1200px"
      ref="modalRef"
    >
      <div class="container">
        <BasicForm @register="registerForm">
          <!-- 诊断类型 -->
          <template #diagnosis-type-slot="{ model, field }">
            <Select
              v-model:value="model[field]"
              :options="diagnosisTypeOptions"
              placeholder="请选择诊断类型"
              allow-clear
              @change="handleChangeDiagnoseType"
            />
          </template>
          <!-- 触发类型 -->
          <template #trigger-type-slot="{ model, field }">
            <Select
              v-model:value="model[field]"
              :options="triggerTypeOptions"
              placeholder="请选择触发类型"
              @change="handleChangeTriggerType"
              allow-clear
              :disabled="triggerTypeDisabled"
            />
          </template>
          <!-- 报警规则 -->
          <template #alarm-slot="{ model, field }">
            <div class="rule-contianer">
              <Select
                v-model:value="model[field]"
                :options="alarmRuleOptions"
                :field-names="{
                  label: 'title',
                  value: 'id',
                }"
                showSearch
                allowClear
                optionFilterProp="title"
                placeholder="请选择报警规则"
              />
              <Button
                v-if="model[field]"
                type="primary"
                ghost
                @click="handleOpenAlarmRuleDetailModal"
                >查看详情</Button
              >
            </div>
          </template>
          <!-- 规则类型 -->
          <template #rule-type-slot="{ model, field }">
            <RadioGroup
              v-model:value="model[field]"
              button-style="solid"
              @change="handleChangeRuleType"
            >
              <RadioButton :value="1">一般规则</RadioButton>
              <RadioButton :value="2">特殊规则</RadioButton>
            </RadioGroup>
          </template>
          <!-- 指标列表 -->
          <template #indicator-list-slot>
            <div class="rule-info-content-container">
              <div>
                <Input v-model:value="inputVal" placeholder="请输入资源标识/名称">
                  <template #prefix>
                    <Icon icon="icon-park-outline:search" color="#d9d9d9" />
                  </template>
                </Input>
                <div class="select-container">
                  <div>资源标识</div>
                  <div>资源名称</div>
                  <div class="checkAll-box"></div>
                </div>
                <div class="indicator-container">
                  <VScroll :itemHeight="40" :items="filterTagetList" :height="155">
                    <template #default="{ item }">
                      <div class="scroll-item">
                        <div>
                          <Tooltip>
                            <template #title>{{ item.code }}</template>
                            {{ item.code }}
                          </Tooltip>
                        </div>
                        <div>
                          <Tooltip>
                            <template #title>{{ item.name }}</template>
                            {{ item.name }}
                          </Tooltip>
                        </div>
                        <Checkbox
                          v-model:checked="item.checked"
                          @click="handleCheckIndicator(item)"
                        />
                      </div>
                    </template>
                  </VScroll>
                </div>
              </div>
              <div>
                <div class="checked-header-container">
                  <div>资源标识</div>
                  <div>操作</div>
                </div>
                <Draggable
                  group="form-draggable"
                  class="checked-container"
                  tag="div"
                  :component-data="{
                    tag: 'div',
                    type: 'transition-group',
                    name: 'list',
                  }"
                  ghostClass="moving"
                  :animation="180"
                  :list="checkedList"
                  handle=".th-drag"
                  item-key="key"
                >
                  <template #item="{ element: item }">
                    <div class="checked-item">
                      <div>
                        <Tooltip>
                          <template #title> {{ item.code }}</template>
                          {{ item.code }}
                        </Tooltip>
                      </div>
                      <div class="btns">
                        <div class="edit-btn" v-if="item.formula" @click="handleEditItem(item)"
                          >编辑</div
                        >
                        <div class="copy-btn" @click="handleCopyItem(item.code)">复制</div>
                        <div class="delete-btn" @click="handleDeleteItem(item)">删除</div>
                      </div>
                    </div>
                  </template>
                </Draggable>
                <div class="bottom-container">
                  <span class="clear-btn" @click="handleOpenVarModal" v-if="rule_type === 2"
                    >新增变量</span
                  >
                  <span class="clear-btn ml-4" @click="clearList">清空</span>
                </div>
              </div>
            </div>
          </template>

          <!-- 编辑规则，设置规则 -->
          <template #rule-setting-slot="{ model, field }">
            <RadioGroup
              v-model:value="model[field]"
              button-style="solid"
              @change="handleChangeRuleSetting"
            >
              <RadioButton :value="2">设置规则</RadioButton>
              <RadioButton :value="1">编辑规则</RadioButton>
            </RadioGroup>
          </template>

          <template #formula-slot="{ model, field }">
            <div class="trigger-condition-container">
              <!-- 一般规则触发条件 -->
              <div class="normal-rule" v-if="rule_type === 1">
                <Form ref="formRef1" :model="dynamicForm">
                  <Row
                    :gutter="[12, 16]"
                    v-for="(item, index) in dynamicForm.list"
                    :key="item.code"
                    :class="index > 0 ? 'my-4' : ''"
                  >
                    <Col :span="12">
                      <FormItem
                        label=""
                        :name="['list', index, 'code']"
                        :rules="{ required: true, message: '请选择指标', trigger: 'blur' }"
                      >
                        <Select
                          v-model:value="item.code"
                          placeholder="请选择指标"
                          allowClear
                          @change="
                            (value, options) =>
                              handleChangeDynamicFormCode(value, options, index, 1)
                          "
                        >
                          <SelectOption
                            v-for="i in checkedList"
                            :key="i.code"
                            :value="i.code"
                            :data="i"
                            >{{ i.code }}</SelectOption
                          >
                        </Select>
                      </FormItem>
                    </Col>
                    <Col :span="4">
                      <FormItem
                        label=""
                        :name="['list', index, 'action']"
                        :rules="{ required: true, message: '请选择比较符', trigger: 'blur' }"
                      >
                        <Select
                          v-model:value="item.action"
                          placeholder="请选择比较符"
                          allowClear
                          @select="(value, options) => handleChangeAction(value, options, index, 1)"
                        >
                          <SelectOption
                            v-for="i in ruleActionList"
                            :key="i.id"
                            :value="i.id"
                            :data="i"
                          >
                            {{ i.title }}
                          </SelectOption>
                        </Select>
                      </FormItem>
                    </Col>
                    <Col :span="4">
                      <FormItem
                        label=""
                        :name="['list', index, 'minValue']"
                        :rules="{ required: true, message: '请输入判断值', trigger: 'blur' }"
                      >
                        <InputNumber
                          v-model:value="item.minValue"
                          allowClear
                          placeholder="请输入判断值"
                        />
                      </FormItem>
                    </Col>
                    <Col :span="4" v-if="item.valueCount === 2">
                      <FormItem
                        label=""
                        :name="['list', index, 'maxValue']"
                        :rules="{ required: true, message: '请输入判断值', trigger: 'blur' }"
                      >
                        <InputNumber
                          v-model:value="item.maxValue"
                          allowClear
                          placeholder="请输入判断值"
                        />
                      </FormItem>
                    </Col>
                  </Row>
                </Form>
              </div>
              <!-- 特殊规则触发条件 -->
              <div class="special-rule" v-if="rule_type === 2">
                <!-- 设置规则 -->
                <div class="add-rule" v-if="rule_setting === 2">
                  <div class="btns">
                    <Button
                      type="primary"
                      :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
                      @click="handleAddRule(1)"
                      >数值</Button
                    >
                    <Button
                      type="primary"
                      :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
                      @click="handleAddRule(0)"
                      >指标</Button
                    >
                  </div>
                  <div class="content">
                    <Form ref="formRef2" :model="dynamicForm2">
                      <Row
                        :gutter="[12, 16]"
                        v-for="(item, index) in dynamicForm2.list"
                        :key="item.code"
                        :class="index > 0 ? 'my-4' : ''"
                      >
                        <Col :span="11">
                          <FormItem
                            label=""
                            :name="['list', index, 'code']"
                            :rules="{
                              required: true,
                              message: '请选择指标',
                              trigger: 'change',
                            }"
                          >
                            <Select
                              :options="checkedList"
                              :field-names="{
                                label: 'code',
                                value: 'code',
                              }"
                              allowClear
                              v-model:value="item.code"
                              @change="
                                (value, options) =>
                                  handleChangeDynamicFormCode(value, options, index, 2)
                              "
                              placeholder="请选择指标"
                            />
                          </FormItem>
                        </Col>
                        <Col :span="4">
                          <FormItem
                            label=""
                            :name="['list', index, 'action']"
                            :rules="{
                              required: true,
                              message: '请选择比较运算符',
                              trigger: 'change',
                            }"
                          >
                            <Select
                              v-model:value="item.action"
                              allowClear
                              placeholder="请选择比较运算符"
                              @select="
                                (value, options) => handleChangeAction(value, options, index, 2)
                              "
                            >
                              <SelectOption
                                v-for="i in ruleActionList"
                                :key="i.id"
                                :value="i.id"
                                :data="i"
                              >
                                {{ i.title }}
                              </SelectOption>
                            </Select>
                          </FormItem>
                        </Col>
                        <Col :span="4">
                          <FormItem
                            label=""
                            :name="['list', index, 'minValue']"
                            :rules="{
                              required: true,
                              message: item.type === 1 ? '请输入判断值' : '请选择请指标',
                              trigger: item.type === 1 ? 'blur' : 'change',
                            }"
                          >
                            <InputNumber
                              v-model:value="item.minValue"
                              allowClear
                              placeholder="请输入判断值"
                              v-if="item.type === 1"
                            />
                            <Select
                              :options="indicatorList"
                              :field-names="{
                                label: 'name',
                                value: 'code',
                              }"
                              v-model:value="item.minValue"
                              showSearch
                              allowClear
                              placeholder="请选择指标"
                              @change="
                                (value, options) => handleChangeMinvalue(value, options, index)
                              "
                              v-else
                            />
                          </FormItem>
                        </Col>
                        <Col :span="4" v-if="item.valueCount === 2">
                          <FormItem
                            label=""
                            :name="['list', index, 'maxValue']"
                            :rules="{
                              required: true,
                              message: item.type === 1 ? '请输入判断值' : '请选择请指标',
                              trigger: item.type === 1 ? 'blur' : 'change',
                            }"
                          >
                            <InputNumber
                              v-model:value="item.maxValue"
                              allowClear
                              placeholder="请输入判断值"
                              v-if="item.type === 1"
                            />
                            <Select
                              :options="indicatorList"
                              :field-names="{
                                label: 'name',
                                value: 'code',
                              }"
                              v-model:value="item.maxValue"
                              showSearch
                              allowClear
                              @change="
                                (value, options) => handleChangeMaxvalue(value, options, index)
                              "
                              placeholder="请选择指标名称"
                              v-else
                            />
                          </FormItem>
                        </Col>
                        <Col :span="1">
                          <Button
                            :icon="h(Icon, { icon: 'icon-park-outline:minus' })"
                            @click="handleReduce(index)"
                          />
                        </Col>
                      </Row>
                    </Form>
                  </div>
                </div>
                <!-- 编辑规则 -->
                <div class="json-rule" v-if="rule_setting === 1">
                  <!-- <JsonEditor
                    ref="jsConvertEditorRef"
                    v-model:modelValue="model[field]"
                    style="height: 150px; border: 1px solid #ced4da"
                    @update:modelValue="handleJsonEditorChange"
                    @init-instance="onInitInstance"
                  /> -->
                  <div>
                    <CodeEditor
                      placeholder="请输入自定义规则"
                      v-model:value="model[field]"
                      :row="4"
                      class="h-40"
                      mode="JSON"
                    />
                  </div>
                  <Button
                    type="primary"
                    class="mt-2"
                    ghost
                    @click="handleTest"
                    :loading="mockLoading"
                    >模拟测试</Button
                  >
                </div>
              </div>
            </div>
          </template>

          <template #suggestion-slot="{ model, field }">
            <div class="content-container">
              <Textarea
                v-model:value="model[field]"
                allowClear
                show-count
                :maxlength="200"
                placeholder="请输入诊断建议"
              />
              <!-- <Button
                :loading="false"
                :icon="h(Icon, { icon: 'icon-park-outline:doc-detail' })"
                type="primary"
                class="mt-2"
                >AI生成模板</Button
              > -->
            </div>
          </template>
          <template #content-slot="{ model, field }">
            <div class="content-container">
              <Textarea
                v-model:value="model[field]"
                allowClear
                show-count
                :maxlength="200"
                placeholder="请输入诊断内容"
              />
              <!-- <Button
                :loading="false"
                :icon="h(Icon, { icon: 'icon-park-outline:doc-detail' })"
                type="primary"
                class="mt-2"
                >AI生成模板</Button
              > -->
            </div>
          </template>
        </BasicForm>
      </div>
      <template #footer>
        <a-button @click="handleCancel" class="default-btn">取消</a-button>
        <a-button type="primary" @click="handleSubmit">保存</a-button>
      </template>
    </BasicModal>
    <AlarmRuleDetailModal @register="registerAlarmRuleDetailModal" />
    <VariateModal @success="variateSuccess" @register="registerVarModal" />
  </div>
</template>

<script lang="ts" setup name="EditModal">
  import { computed, ref, reactive, h } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import Draggable from 'vuedraggable';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import { CodeEditor } from '/@/components/CodeEditor';
  import AlarmRuleDetailModal from '/@aoa/views/alarm-management/rule-configuration/components/EditModal.vue';
  import VariateModal from './VariateModal.vue';
  import {
    Form,
    FormItem,
    Input,
    Checkbox,
    Select,
    SelectOption,
    RadioGroup,
    Row,
    Col,
    RadioButton,
    InputNumber,
    Textarea,
    Tooltip,
    Button,
  } from 'ant-design-vue';
  import { useCopyToClipboard } from '/@/hooks/web/useCopyToClipboard';
  import { VScroll } from '/@/components/VirtualScroll/index';
  import { schemas, diagnosisTypeOptions, triggerTypeOptions } from '../data';
  import { formulaToArr } from '../../util';
  import { getWarnEventByPage } from '/@aoa/api/event-center';
  import {
    addDiagnosisRule,
    updateDiagnosisRule,
    getListIndicatorAndVar,
    getDefaultRuleActionList,
    detailDiagnosisRule,
    mockTestDiagnosisRule,
  } from '/@aoa/api/diagnose';

  interface FormList {
    code: string | null;
    codeType: number;
    name: string;
    action: string | null;
    valueCount: number;
    minValue: string | null;
    minValueType: number | '';
    maxValue: string | null;
    maxValueType: number | '';
    type: number;
    formula: null | ''; // null： 指标  有值：变量
    varFormula: null | ''; // null： 指标  有值：变量
  }
  interface action {
    id: string;
    title: string;
    valueCount: number;
  }

  const emit = defineEmits(['success', 'register']);

  const isEdit = ref(false);
  const id = ref();
  const formRef1 = ref();
  const formRef2 = ref();
  const triggerTypeDisabled = ref(false);
  const rule_type = ref(1);
  const rule_setting = ref(2);

  const dynamicForm = reactive<{ list: FormList[] }>({
    list: [
      {
        code: null,
        codeType: 0,
        name: '',
        action: null,
        valueCount: 1,
        minValue: '',
        minValueType: 1,
        maxValue: '',
        maxValueType: 1,
        type: 1,
        formula: '',
        varFormula: '',
      },
    ],
  }); // 一般
  const dynamicForm2 = reactive<{ list: FormList[] }>({ list: [] }); // 特殊
  const indicatorListAll: any = ref([]); // 所有指标
  const indicatorList: any = ref([]); // 去除变量所有指标
  const ruleActionList = ref<action[]>([]);
  const alarmRuleOptions = ref([]);

  // 编辑器
  // const jsConvertEditorRef = ref<any>(null);
  const monacoInstance = ref(null);
  const jsonFnTemp = ref('');

  const { createMessage } = useMessage();
  const { clipboardRef, copiedRef } = useCopyToClipboard();

  // 生成模板
  const createFnTemp = () => {
    const fnTemp = `function event() {\r\n  var glData = ts.last('Smart.M580.GFJ.LCP2_DY', 'FACTORYID');\r\n  if (glData > 1) {\r\n    return true;\r\n  }\r\n  return false;\r\n}`;
    return fnTemp;
  };

  const [
    registerForm,
    { resetFields, updateSchema, setFieldsValue, getFieldsValue, validate, clearValidate },
  ] = useForm({
    labelWidth: 110,
    schemas: schemas,
    showActionButtonGroup: false,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ title: data.isEdit ? '编辑规则' : '新增规则' });
    isEdit.value = data.isEdit;
    id.value = data.id;
    await initData();
    if (data.isEdit) getDetail(data.id);
    if (!data.isEdit) {
      let fnTemp = createFnTemp();
      setFieldsValue({
        formula: fnTemp,
      });
    }
  });

  // 诊断类型change
  const handleChangeDiagnoseType = async (value) => {
    // 报警诊断：1   常规诊断： 2
    if (value === 1) await setFieldsValue({ triggerType: 1 });
    triggerTypeDisabled.value = value === 1;
    const formData = getFieldsValue();
    rule_setting.value = formData?.ruleSetting;
    setFieldsValue({ ruleType: formData?.ruleType || 1 });
    rule_type.value = formData?.ruleType || 1;
    updateSchema([
      { field: 'alarmId', ifShow: value === 1 }, //报警规则
      { field: 'ruleType', ifShow: value === 2 && formData?.triggerType === 1 }, //规则类型
      { field: 'frequency', ifShow: value === 2 && formData?.triggerType === 2 }, // 触发频率
      {
        field: 'triggerFrequency',
        ifShow: value === 2 && formData?.triggerType === 1,
      }, // 触发间隔
      { field: 'blank1', ifShow: value === 2 }, // 占位符-规则信息
      { field: 'indicatorList', ifShow: value === 2 }, // 指标列表
      {
        field: 'ruleSetting',
        ifShow: value === 2 && formData?.triggerType === 1 && formData?.ruleType === 2,
      }, // 规则设置
      { field: 'formula', ifShow: value === 2 && formData?.triggerType === 1 }, //触发条件
      { field: 'blank2', ifShow: value }, // 占位符-内容信息
      // // { field: 'aiStatus', ifShow: true }, //AI状态
      { field: 'exceptionReason', ifShow: value === 1 }, // 异常原因
      { field: 'suggestion', ifShow: value === 1 }, // 诊断建议
      { field: 'content', ifShow: value === 2 }, //诊断内容
    ]);
  };

  // 触发类型change
  const handleChangeTriggerType = (value) => {
    // 条件触发：1  定时触发： 2
    const formData = getFieldsValue();
    rule_setting.value = formData?.ruleSetting;
    clearValidate();
    updateSchema([
      { field: 'ruleType', ifShow: value === 1 },
      { field: 'frequency', ifShow: value === 2 },
      {
        field: 'triggerFrequency',
        ifShow: value === 1,
      }, // 触发间隔
      {
        field: 'ruleSetting',
        ifShow: value === 1 && rule_type.value === 2 && formData?.diagnosisType === 2,
      }, // 规则设置
      { field: 'formula', ifShow: value === 1 }, //触发条件
    ]);
  };

  const cacheCheckedList = ref<any[]>([]);

  const setIndicatorListChecked = () => {
    cacheCheckedList.value = checkedList.value;
    indicatorList.value.forEach((item: any) => {
      const find = checkedList.value.find((i) => i.code === item.code);
      if (find) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
  };
  // 规则类型change
  const handleChangeRuleType = (e: Event) => {
    const value = e?.target?.value;
    const formData = getFieldsValue();
    rule_type.value = value;
    rule_setting.value = formData?.ruleSetting;
    console.log('规则表单', formData);
    clearValidate();
    updateSchema([
      { field: 'ruleSetting', ifShow: value === 2 }, // 规则设置
    ]);
    if (value === 1) {
      checkedList.value = cacheCheckedList.value.filter((item) => !item.formula);
    } else {
      checkedList.value = cacheCheckedList.value;
    }
  };

  // 规则设置change
  const handleChangeRuleSetting = async (e: Event) => {
    const value = e?.target?.value;
    rule_setting.value = value;
    let fnTemp = jsonFnTemp.value || createFnTemp();
    setFieldsValue({
      formula: fnTemp,
    });
    clearValidate();
  };

  // 初始化数据
  const initData = async () => {
    setModalProps({ loading: true });
    // 获取报警规则
    await getWarnEventByPage({ current: 1, size: 1000 }).then((data) => {
      alarmRuleOptions.value = data.records;
    });
    // 获取指标列表
    const params = { ruleId: id.value };
    await getListIndicatorAndVar(params).then((data) => {
      indicatorListAll.value = data;
      indicatorList.value = data.filter((item) => !item.formula);
    });
    // 比较符
    await getDefaultRuleActionList().then((data) => {
      ruleActionList.value = data;
    });
    setModalProps({ loading: false });
  };

  const getCodes = (str) => {
    let codes = [];
    const list = formulaToArr(str);
    const indexItem = list.filter((_, index) => index === 0);
    const filterList = list.filter((_, index) => index !== 0);
    const dynamicFormList: any[] = [];
    filterList.map((item, index) => {
      const idx = Math.floor(index / 2);
      const indx = index % 2;
      if (indx === 0) dynamicFormList.push([]);
      dynamicFormList[idx][indx] = item;
    });

    dynamicFormList.map((item) => {
      if (item[1][0] === 0) {
        codes.push(item[1][1]);
      }
    });
    if (indexItem.length) {
      codes.push(indexItem[0][1]);
    }
    return [...new Set([...codes])];
  };

  const setCheckedList = () => {
    // 拿checkedList 是变量映射指标
    let varCodes: string[] = [];
    let indCodes: string[] = [];
    console.log('checkedList.value', checkedList.value);
    checkedList.value
      .filter((item) => item.formula)
      .map((item) => {
        varCodes.push(item.code);
        varCodes = [...new Set([...varCodes, ...item.allCodes])];
      });

    checkedList.value
      .filter((item) => !item.formula)
      .map((item) => {
        indCodes.push(item.code);
      });

    varCodes.map((item) => {
      const findItem = indicatorList.value.find((i) => i.code === item);
      const findAllItem = indicatorListAll.value.find((i) => i.code === item);
      if (findItem) findItem.checked = true;
      if (findAllItem && findAllItem.formula) {
        const codes = getCodes(findAllItem.formula);
        indCodes = [...new Set([...indCodes, ...codes])];
      }
    });
    // 拿表单使用的指标
    let useCodes: string[] = [];
    dynamicForm2.list.map((item) => {
      if (item.code) {
        useCodes.push(String(item.code));
      }
      if (item.type !== 1) {
        if (item.minValueType === 0 && item.minValue) {
          useCodes.push(item.minValue);
        }
        if (item.valueCount === 2 && item.maxValueType === 0 && item.maxValue) {
          useCodes.push(item.maxValue);
        }
      }
    });
    const allCodes = [...new Set([...varCodes, ...useCodes, ...indCodes])];
    checkedList.value = allCodes.map((code) => {
      const findAllItem = indicatorListAll.value.find((i) => i.code === String(code));
      const findItem = indicatorList.value.find((i) => i.code === String(code));
      const findCheckItem = checkedList.value.find((i) => i.code === String(code));
      if (findItem) findItem.checked = true;

      return {
        code: findCheckItem ? findCheckItem.code : findAllItem?.code,
        name: findCheckItem ? findCheckItem.name : findAllItem?.name,
        formula: findCheckItem ? findCheckItem.formula : findAllItem?.formula,
        allCodes: findCheckItem
          ? findCheckItem.allCodes
          : findAllItem?.formula
          ? getCodes(findAllItem.formula)
          : [],
        checked: true,
      };
    });
    setIndicatorListChecked();
  };

  // 更改指标
  const handleChangeDynamicFormCode = (_, option, index, type) => {
    if (type === 1) {
      dynamicForm.list[0].name = option.data.name;
      dynamicForm.list[0].codeType = option.data.formula ? 2 : 0;
      dynamicForm.list[0].varFormula = option.data.formula;
    } else if (type === 2) {
      dynamicForm2.list[index].name = option.name;
      dynamicForm2.list[index].codeType = option.formula ? 2 : 0;
      dynamicForm2.list[index].varFormula = option.formula;
    }
  };

  // 更改比较符
  const handleChangeAction = (_value: any, option: any, index, type) => {
    if (type === 1) {
      dynamicForm.list[0].valueCount = option.data.valueCount;
    } else if (type === 2) {
      dynamicForm2.list[index].valueCount = option.data.valueCount;
      setCheckedList();
    }
  };

  // 更改minValue 指标
  const handleChangeMinvalue = (_, option, index) => {
    dynamicForm2.list[index].minValueType = option.formula ? 2 : 0;
    setCheckedList();
  };

  // 更改maxValue 指标
  const handleChangeMaxvalue = (_, option, index) => {
    dynamicForm2.list[index].maxValueType = option.formula ? 2 : 0;
    setCheckedList();
  };

  // 动态表单2添加
  const handleAddRule = (type) => {
    if (!checkedList.value.length) {
      createMessage.warning('请先选择指标');
      return;
    }
    // 添加规则
    dynamicForm2.list.push({
      code: null,
      codeType: 0,
      name: '',
      action: null,
      valueCount: 1,
      minValue: null,
      minValueType: type,
      maxValue: null,
      maxValueType: type,
      type,
      formula: null,
      varFormula: null,
    });
  };

  // 动态表单2删除
  const handleReduce = (index) => {
    dynamicForm2.list.splice(index, 1);
    setCheckedList();
  };

  const modalRef = ref(null);
  const scrollBottom = () => {
    modalRef.value?.modalWrapperRef.scrollBottom();
  };

  // 提交
  const handleSubmit = async () => {
    try {
      const formData = getFieldsValue();
      let formula_value = '';
      let varAddReq: { varName: string; varCode: string | null; varFormula: string | null }[] = [];
      if (formData.diagnosisType === 2 && formData.triggerType === 1 && formData.ruleType === 1) {
        if (dynamicForm.list.length <= 0) {
          createMessage.warning('请选择触发条件');
          return;
        }
        await formRef1.value.validate();
        const formula_arr = dynamicForm.list.map((item) => {
          const actionLabel = ruleActionList.value.find((i) => i.id === item.action)?.title;
          return `['${item.codeType}','${item.code}','${item.name}'],['${
            item.action
          }','${actionLabel}'],['${item.minValueType}','${item.minValue}','${item.valueCount}'],['${
            item.maxValueType
          }',${item.maxValue ? `${item.maxValue}` : null},'${item.valueCount}']`;
        });
        formula_value = formula_arr.join(',');
        setFieldsValue({
          formula: formula_value,
        });
        varAddReq = dynamicForm.list
          .filter((item) => item.varFormula)
          .map((item) => {
            return {
              varName: item.name,
              varCode: item.code,
              varFormula: item.varFormula,
            };
          });
      } else if (
        formData.diagnosisType === 2 &&
        formData.triggerType === 1 &&
        formData.ruleType === 2 &&
        formData.ruleSetting === 2
      ) {
        if (dynamicForm2.list.length <= 0) {
          createMessage.warning('请选择触发条件');
          return;
        }
        await formRef2.value.validate();
        const formula_arr = dynamicForm2.list.map((item) => {
          const actionLabel = ruleActionList.value.find((i) => i.id === item.action)?.title;
          return `['${item.codeType}','${item.code}','${item.name}'],['${
            item.action
          }','${actionLabel}'],['${item.minValueType}','${item.minValue}','${item.valueCount}'],['${
            item.maxValueType
          }',${item.maxValue ? `${item.maxValue}` : null},'${item.valueCount}']`;
        });
        formula_value = formula_arr.join(',');
        console.log('formula_value2', formula_value);
        setFieldsValue({
          formula: formula_value,
        });
        varAddReq = dynamicForm2.list
          .filter((item) => item.varFormula)
          .map((item) => {
            return {
              varName: item.name,
              varCode: item.code,
              varFormula: item.varFormula,
            };
          });
      }
      const values = await validate();
      setModalProps({ confirmLoading: true });
      const params = {
        id: isEdit.value ? id.value : undefined,
        ...values,
        varAddReq,
        isEdit: values.ruleSetting === 1 && values.ruleType === 2,
        ruleType:
          values.diagnosisType === 1 || (values.diagnosisType === 2 && values.triggerType === 2)
            ? null
            : values.ruleType,
        frequency:
          values.diagnosisType === 1
            ? null
            : values.triggerType === 2
            ? values.frequency
            : values.triggerFrequency,
        formula: values.diagnosisType === 1 ? '' : values.formula,
      };
      console.log('提交参数', params);
      if (values.ruleSetting === 1 && values.diagnosisType === 2 && values.ruleType === 2) {
        const params = {
          formula: values.formula,
        };
        const data = await mockTestDiagnosisRule(params);
        if (data.code !== 0) {
          createMessage.error(data.msg);
          return;
        }
      }
      isEdit.value ? await updateDiagnosisRule(params) : await addDiagnosisRule(params);
      await handleCancel();
      createMessage.success('操作成功');
      emit('success');
    } catch (error: any) {
      console.log('error submit!!', error);
      const errorFields = error?.errorFields;
      if (errorFields && errorFields.length) {
        if (errorFields.every((item) => item.name[0] === 'content')) {
          scrollBottom();
        }
      }
    } finally {
      setModalProps({ confirmLoading: false });
    }
  };

  // 关闭
  const handleCancel = async () => {
    rule_type.value = 1;
    dynamicForm.list = [
      {
        code: null,
        codeType: 0,
        name: '',
        action: null,
        valueCount: 1,
        minValue: '',
        minValueType: 1,
        maxValue: '',
        maxValueType: 1,
        type: 1,
        formula: '',
        varFormula: '',
      },
    ];
    dynamicForm2.list = [];
    checkedList.value = [];
    cacheCheckedList.value = [];
    inputVal.value = '';
    rule_setting.value = 2;
    indicatorList.value = [];
    indicatorListAll.value = [];
    updateSchema([
      { field: 'alarmId', ifShow: false },
      { field: 'ruleType', ifShow: false },
      { field: 'frequency', ifShow: false },
      { field: 'blank1', ifShow: false },
      { field: 'indicatorList', ifShow: false },
      { field: 'ruleSetting', ifShow: false },
      { field: 'triggerFrequency', ifShow: false }, // 触发间隔
      { field: 'formula', ifShow: false },
      { field: 'blank2', ifShow: false },
      // { field: 'aiStatus', ifShow: true },
      { field: 'exceptionReason', ifShow: false },
      { field: 'suggestion', ifShow: false },
      { field: 'content', ifShow: false },
    ]);
    resetFields();
    setFieldsValue({
      ruleSetting: 2,
      ruleType: 1,
    });
    clearValidate();
    closeModal();
  };

  /**
   * @description: 规则信息
   */
  const inputVal = ref('');
  const checkedList = ref<any[]>([]);

  const filterTagetList = computed(() => {
    let result = indicatorList.value;
    if (inputVal.value.trim()) {
      const inputValArr = inputVal.value.split(' ').filter((item) => item);
      result = result.filter((item: any) => {
        return inputValArr.every((i) => {
          return (
            item.name.toLocaleLowerCase().includes(i.toLocaleLowerCase()) ||
            item.code.toLocaleLowerCase().includes(i.toLocaleLowerCase())
          );
        });
      });
    }
    return result;
  });

  // 选中指标
  const handleCheckIndicator = (item: any) => {
    item.checked = !item.checked;
    if (item.checked) {
      checkedList.value.push(JSON.parse(JSON.stringify(item)));
    } else {
      checkedList.value = checkedList.value.filter((i: any) => i.code !== item.code);
      dynamicForm2.list = dynamicForm2.list.filter((i) => i.code !== item.code);
      const findLastIndex = dynamicForm.list.findLastIndex((i) => i.code !== item.code);
      if (findLastIndex !== -1) {
        dynamicForm.list[0].code = null;
        dynamicForm.list[0].codeType = 0;
      }
    }
    setIndicatorListChecked();
  };

  // 复制指标
  const handleCopyItem = async (value) => {
    clipboardRef.value = value;
    if (copiedRef.value) {
      createMessage.success('复制成功！');
    }
  };

  // 删除指标
  const handleDeleteItem = (item: any) => {
    const findItem = indicatorList.value.find((i: any) => i.code === item.code);
    if (findItem) {
      indicatorList.value.find((i: any) => i.code === item.code).checked = false;
      checkedList.value = checkedList.value.filter((i: any) => i.code !== item.code);
    } else {
      const findIndex = checkedList.value.findIndex(
        (i: any) => i.code === item.code && i.name && i.formula,
      );
      checkedList.value.splice(findIndex, 1);
    }
    setIndicatorListChecked();
    const findIndex2 = dynamicForm2.list.findIndex((i) => i.code === item.code);
    if (dynamicForm.list[0].code === item.code) {
      dynamicForm.list[0].code = null;
    }

    if (findIndex2 !== -1) {
      dynamicForm2.list = dynamicForm2.list.filter((i: any) => i.code !== item.code);
    }
  };

  // 清空指标
  const clearList = () => {
    indicatorList.value.forEach((item: any) => {
      item.checked = false;
    });
    checkedList.value = [];
    cacheCheckedList.value = [];
    dynamicForm.list = [
      {
        code: null,
        codeType: 0,
        name: '',
        action: null,
        valueCount: 1,
        minValue: '',
        minValueType: 1,
        maxValue: '',
        maxValueType: 1,
        type: 1,
        formula: '',
        varFormula: '',
      },
    ];
    dynamicForm2.list = [];
  };

  // 通过详情设置checkedList
  const setCheckListByDetail = (list) => {
    let indCodes: string[] = [];
    let varCodes: string[] = [];
    list.map((item) => {
      if (item[0][0] == 2) {
        varCodes.push(item[0][1]);
      }
      if (item[0][0] == 0) {
        indCodes.push(item[0][1]);
      }
      if (item[2][0] != 1) {
        indCodes.push(item[2][1]);
      }

      if (item[2][2] == 2 && item[3][0] != 1) {
        indCodes.push(item[3][1]);
      }
    });
    varCodes = [...new Set([...varCodes])];

    varCodes.map((item) => {
      const findItemFormula = indicatorListAll.value.find((i) => i.code === String(item))?.formula;
      if (findItemFormula) {
        const codes = getCodes(findItemFormula);
        indCodes = [...new Set([...indCodes, ...codes])];
      }
    });
    const allCodes = [...new Set([...indCodes, ...varCodes])];
    checkedList.value = allCodes.map((item) => {
      const findItem = indicatorListAll.value.find((i) => i.code === String(item));
      const findI = indicatorList.value.find((i) => i.code === String(item));
      if (findI) findI.checked = true;
      return {
        code: findItem?.code,
        name: findItem?.name,
        formula: findItem?.formula,
        checked: true,
        allCodes: findItem?.formula ? getCodes(findItem.formula) : [],
      };
    });
    setIndicatorListChecked();
  };

  // 正则匹配
  const setMatches = (content) => {
    const pattern = /\$\$(.*?)\$\$/g;
    const matches = content?.matchAll(pattern);
    const extractedValues = Array.from(matches, (match) => match[1]);
    checkedList.value = extractedValues.map((item) => {
      const finditem = indicatorList.value.find((i) => i.code === String(item));
      return {
        code: finditem?.code,
        name: finditem?.name,
        formula: finditem?.formula,
        checked: true,
        allCodes: finditem?.formula ? getCodes(finditem.formula) : [],
      };
    });
    setIndicatorListChecked();
  };

  // 获取详情
  const getDetail = async (id) => {
    setModalProps({ loading: true });
    const res = await detailDiagnosisRule(id);
    console.log('详情', res);
    updateSchema([
      { field: 'alarmId', ifShow: res.diagnosisType === 1 }, //报警规则
      { field: 'ruleType', ifShow: res.diagnosisType === 2 && res?.triggerType === 1 }, //规则类型
      {
        field: 'triggerFrequency',
        ifShow: res.diagnosisType === 2 && res?.triggerType === 1,
      }, // 触发间隔
      { field: 'frequency', ifShow: res.diagnosisType === 2 && res?.triggerType === 2 }, // 触发频率
      { field: 'blank1', ifShow: res.diagnosisType === 2 }, // 占位符-规则信息
      { field: 'indicatorList', ifShow: res.diagnosisType === 2 }, // 指标列表
      {
        field: 'ruleSetting',
        ifShow: res.diagnosisType === 2 && res?.triggerType === 1 && res?.ruleType === 2,
      }, // 规则设置
      { field: 'formula', ifShow: res.diagnosisType === 2 && res?.triggerType === 1 }, //触发条件
      { field: 'blank2', ifShow: res.diagnosisType }, // 占位符-内容信息
      // // { field: 'aiStatus', ifShow: true }, //AI状态
      { field: 'exceptionReason', ifShow: res.diagnosisType === 1 }, // 异常原因
      { field: 'suggestion', ifShow: res.diagnosisType === 1 }, // 诊断建议
      { field: 'content', ifShow: res.diagnosisType === 2 }, //诊断内容
    ]);
    setFieldsValue({ ...res, ruleSetting: res.isEdit ? 1 : 2, triggerFrequency: res.frequency });
    triggerTypeDisabled.value = res.diagnosisType === 1;
    rule_type.value = res.ruleType;
    rule_setting.value = res.isEdit ? 1 : 2;
    if (rule_setting.value === 2) {
      setFieldsValue({
        formula: '',
      });
    }

    // 获取表格列配置
    if (
      (res.diagnosisType === 2 && res.triggerType === 1 && res.ruleType === 1) ||
      (res.diagnosisType === 2 && res.triggerType === 1 && res.ruleType === 2 && !res.isEdit)
    ) {
      const list = formulaToArr(res.formula);
      const dynamicFormList: any[] = [];
      list.map((item, index) => {
        const idx = Math.floor(index / 4);
        const indx = index % 4;
        if (indx === 0) dynamicFormList.push([]);
        dynamicFormList[idx][indx] = item;
      });
      console.log('公式', dynamicFormList);
      setCheckListByDetail(dynamicFormList);

      if (res.ruleType === 1) {
        // 一般规则
        dynamicForm.list = dynamicFormList.map((item) => {
          const code = item[0][1];
          const formula = indicatorListAll.value.find((i) => i.code === String(code))?.formula;
          return {
            code: item[0][1],
            codeType: item[0][0],
            name: item[0][2],
            action: item[1][0],
            minValue: item[2][1],
            minValueType: item[2][0],
            maxValue: item[3][1],
            maxValueType: item[3][0],
            valueCount: item[2][2],
            type: item[2][0] === 1 ? 1 : 0,
            formula: formula,
            varFormula: formula,
          };
        });
      } else {
        // 特殊规则
        dynamicForm2.list = dynamicFormList.map((item) => {
          const code = item[0][1];
          const formula = indicatorListAll.value.find((i) => i.code === String(code))?.formula;
          return {
            code: item[0][1],
            codeType: item[0][0],
            name: item[0][2],
            action: item[1][0],
            minValue: item[2][1],
            minValueType: item[2][0],
            maxValue: item[2][2] === 2 ? item[3][1] : null,
            maxValueType: item[3][0],
            valueCount: item[2][2],
            type: item[2][0] === 1 ? 1 : 0,
            formula: formula,
            varFormula: formula,
          };
        });
      }
    }
    // 定时触发
    if (res.diagnosisType === 2 && res.triggerType === 2) {
      const content = res?.content;
      if (content) setMatches(content);
    }

    // 编辑器
    if (res.isEdit && res.diagnosisType === 2 && res.ruleType === 2) {
      let fnTemp = res.formula ? res.formula : createFnTemp();
      jsonFnTemp.value = fnTemp;
      setFieldsValue({
        formula: fnTemp,
      });
      const content = res?.content;
      if (content) setMatches(content);
    } else {
      let fnTemp = createFnTemp();
      setFieldsValue({
        formula: fnTemp,
      });
    }

    clearValidate();
    setModalProps({ loading: false });
  };

  // 报警规则详情
  const [registerAlarmRuleDetailModal, { openModal: openAlarmRuleDetailModal }] = useModal();
  const handleOpenAlarmRuleDetailModal = () => {
    const alarmId = getFieldsValue()?.alarmId;
    const data = alarmRuleOptions.value.find((item: any) => item.id === alarmId);
    openAlarmRuleDetailModal(true, {
      isEdit: true,
      record: data,
      disabled: true,
    });
  };

  // 变量modal
  const [registerVarModal, { openModal: openVarModal }] = useModal();
  // 新增变量
  const handleOpenVarModal = () => {
    openVarModal(true, {
      indicatorListAll: indicatorListAll.value,
      checkedList: checkedList.value,
      isEdit: false,
    });
  };

  // 编辑变量
  const handleEditItem = (item) => {
    openVarModal(true, {
      indicatorListAll: indicatorListAll.value,
      checkedList: checkedList.value,
      isEdit: true,
      data: item,
    });
  };

  // 新增变量成功回调
  const variateSuccess = (data) => {
    const varCodes = data.allCodes;
    const checkedListCodesRaw = checkedList.value.map((item) => item.code);
    const checkedListCodes = [...new Set([...checkedListCodesRaw, ...varCodes])];
    checkedList.value = checkedListCodes.map((code) => {
      const findItem = indicatorList.value.find((item) => item.code === code);
      const findCheckItem = checkedList.value.find((item) => item.code === code);
      if (findItem) findItem.checked = true;

      return {
        code: code,
        name: code === data.code ? data.name : findCheckItem?.name || findItem?.name,
        formula: code === data.code ? data.formula : findCheckItem?.formula || findItem?.formula,
        checked: true,
        allCodes: data.code === code ? varCodes : findCheckItem?.allCodes || [],
      };
    });
    if (data.isEdit) {
      dynamicForm.list = dynamicForm.list.map((item) => {
        return {
          ...item,
          varFormula: item.code === data.code ? data.formula : item.varFormula,
        };
      });
      dynamicForm2.list = dynamicForm2.list.map((item) => {
        return {
          ...item,
          varFormula: item.code === data.code ? data.formula : item.varFormula,
        };
      });
    } else {
      checkedList.value.push({
        code: data.code,
        name: data.name,
        formula: data.formula,
        allCodes: varCodes,
      });
    }
    setIndicatorListChecked();
  };

  const handleJsonEditorChange = (value) => {
    console.log(value);
  };

  const mockLoading = ref(false);

  // 模拟测试
  const handleTest = async () => {
    const formula = getFieldsValue()?.formula;
    if (!formula) {
      createMessage.warning('请输入触发条件！');
      return;
    }
    try {
      mockLoading.value = true;
      const params = {
        formula,
      };
      const data = await mockTestDiagnosisRule(params);
      data.code === 0 ? createMessage.success('模拟测试成功！') : createMessage.error(data.msg);
    } finally {
      mockLoading.value = false;
    }
  };

  const onInitInstance = (monaco) => {
    monacoInstance.value = monaco;
    // getFnInfo();
  };

  // 获取事件函数相关信息
  // const getFnInfo = async () => {
  //   // const res = await getEventFnInfo();
  //   const res = [];
  //   if (monacoInstance.value) {
  //     // 注册自定义提示
  //     monacoInstance.value?.languages.registerCompletionItemProvider('javascript', {
  //       triggerCharacters: ['t', 'ts', 'ts.'],
  //       provideCompletionItems: (model, position) => {
  //         const wordUntilPosition = model.getWordUntilPosition(position);
  //         const provideCompletionItems = res.map((item, index) => ({
  //           label: item.invokeChineseExample,
  //           kind: monacoInstance.value?.languages.CompletionItemKind.Function,
  //           detail: item.methodNote,
  //           documentation: item.paramNote,
  //           insertText: item.invokeExample,
  //           range: {
  //             startLineNumber: position.lineNumber,
  //             endLineNumber: position.lineNumber,
  //             startColumn: wordUntilPosition.startColumn,
  //             endColumn: wordUntilPosition.endColumn - 1,
  //           },
  //           sortText: String(index + 1),
  //         }));

  //         return {
  //           suggestions: provideCompletionItems,
  //         };
  //       },
  //     });
  //   }
  // };
</script>

<style scoped lang="less">
  .easy-rule,
  .custom-rule {
    :deep(.ant-form-item) {
      margin-bottom: 0;
    }
  }

  .container {
    .rule-contianer {
      display: flex;
      align-items: flex-start;
      gap: 0 16px;
    }

    .rule-info-content-container {
      display: flex;
      // border: 1px solid #e9e9e9;
      border: 1px solid @aoa3-join-from-border;
      border-radius: 4px;
      height: 260px;
      color: #333;

      ::-webkit-scrollbar {
        height: 0 !important;
        width: 0 !important;
        background: transparent;
      }

      & > div {
        width: 50%;
        padding: 12px;

        &:first-child {
          // width: calc(50% - 50px);
        }

        &:last-child {
          // width: calc(50% + 50px);
          // border-left: 1px solid #e9e9e9;
          border-left: 1px solid @aoa3-join-from-border;
          position: relative;
        }
      }

      .ant-input-affix-wrapper {
        padding: 4px 11px;

        .ant-input-prefix {
          img {
            width: 14px;
          }
        }
      }

      .select-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 12px;
        font-weight: 500;
        color: #333;
        gap: 0 12px;
        padding: 5px 12px;
        background-color: @aoa3-table-header-bg;

        & > div {
          flex-shrink: 0;

          &:nth-child(1) {
            width: 260px;
          }

          &:nth-child(2) {
            flex: 1;
          }
        }

        .checkAll-box {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 52px;

          span {
            margin-right: 8px;
          }

          img {
            cursor: pointer;
          }
        }
      }

      .indicator-container {
        // margin-top: 17px;

        .scroll-item {
          display: flex;
          justify-content: space-between;
          gap: 0 12px;
          padding: 5px 12px;

          &:hover {
            background-color: @table3-row-hover-bg;
          }

          & > * {
            flex-shrink: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          & > div {
            &:nth-child(1) {
              width: 260px;
            }

            &:nth-child(2) {
              flex: 1;
            }
          }

          .ant-checkbox-wrapper {
            width: 52px;
            justify-content: flex-end;
          }
        }
      }

      .checked-header-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 500;
        line-height: 1;
        // margin-bottom: 12px;
        color: #333;
        padding: 9px 12px;
        background-color: @aoa3-table-header-bg;

        & > div {
          // &:nth-child(1) {
          //   width: 180px;
          // }

          &:nth-child(2) {
            width: 116px;
          }
        }
      }

      .checked-container {
        width: 100%;
        height: 173px;
        overflow-y: auto;

        .checked-item {
          padding: 0 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 32px;
          width: 100%;

          &:hover {
            background-color: @aoa3-table-row-hover-bg;
          }

          .btns {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0 16px;
          }

          & > * {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          & > div {
            // &:nth-child(1) {
            //   width: 180px;
            // }

            &:nth-child(2) {
              width: 116px;
            }
          }

          .edit-item {
            cursor: text;
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;

            .name {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              position: relative;
              display: inline-block;
              line-height: 1;
              flex: 1;
            }

            .icon {
              display: none;
            }

            &:hover {
              color: @theme-color;

              .icon {
                display: block;
              }
            }
          }

          .edit-btn {
            cursor: pointer;
            color: @aoa3-join-active-text;
          }

          .copy-btn {
            cursor: pointer;
            color: @aoa3-join-active-text;
          }

          .delete-btn {
            cursor: pointer;
            color: @aoa3-join-active-text;
          }
        }
      }

      .bottom-container {
        position: absolute;
        bottom: 0;
        height: 35px;
        border-top: 1px solid @aoa3-join-from-border;
        left: 0;
        width: 100%;
        line-height: 35px;
        padding: 0 12px;
        display: flex;
        justify-content: flex-end;

        .tips {
          color: #999999;
        }

        .clear-btn {
          color: @aoa3-join-active-text;
          cursor: pointer;
        }
      }
    }

    .trigger-condition-container {
      // .normal-rule {
      // }

      .special-rule {
        .add-rule {
          .btns {
            display: flex;
            align-items: center;
            gap: 0 16px;
          }

          .content {
            margin-top: 16px;
          }
        }

        // .json-rule {
        // }
      }
    }
  }
</style>
