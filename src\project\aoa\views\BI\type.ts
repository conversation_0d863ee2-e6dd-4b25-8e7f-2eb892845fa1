export interface Options {
  label: string;
  value: string;
}

export interface ChartData {
  name: string;
  value: number | null;
  valueHB?: number | null;
  valueTB?: number | null;
  name1?: string;
  value1?: number | null;
  digit: number;
  unit: string;
  indexCode: string;
  collectDateTime: string;
}

export interface LineChartData {
  data: ChartData[];
  first: boolean;
}

export interface DataList {
  value: string | null;
  unit: string;
  name: string;
  indexCode: string;
  digit: number;
}

export interface DataListSelectData {
  data: DataList[];
  value: string;
  options: Options[];
}

export interface AlarmData {
  id: string;
  title: string;
  warnValue: string;
  limitValue: string;
  warnEventLevel: number;
  creationTime: string;
  indicatorCode: string;
}
