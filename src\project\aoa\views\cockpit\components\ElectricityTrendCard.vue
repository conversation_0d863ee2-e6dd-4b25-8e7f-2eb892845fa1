<template>
  <CockpitCardBox title="电费">
    <template #title-slot>
      <AiCard :questionTemplate="question" questionTitle="电费" :deepThinking="deepThinking" />
    </template>
    <template #content>
      <div class="electricity-content w-full h-full" v-if="first">
        <div ref="chartRef" class="chart w-full h-full"></div>
      </div>
      <div class="empty-container w-full h-full" v-else>
        <Empty />
      </div>
    </template>
  </CockpitCardBox>
</template>

<script lang="ts" setup>
  import { ref, PropType, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { CockpitCardBox, Empty } from './CockpitCard';
  import { getScaleValByClientWidth } from '../data';
  import type { BarChartData } from '../type';
  import { AiCard } from '/@aoa/components/AiCard';

  const props = defineProps({
    data: {
      type: Array as PropType<BarChartData[]>,
      default: () => [],
    },
    first: Boolean,
    deepThinking: {
      type: Boolean,
      default: false,
    },
    aiQuestionTemplate: {
      type: String,
      default: '',
    },
  });

  const chartRef = ref(null);
  const { setOptions } = useECharts(chartRef as any);

  const colorList = [
    ['rgba(11, 167, 137, 0.72)', 'rgba(117, 255, 229, 0.72)', 'rgba(151, 252, 233, 1)'],
    ['rgba(97, 185, 197, 0.72)', 'rgba(213, 250, 255, 0.72)', 'rgba(204, 249, 255, 1)'],
  ];
  const itemColorList = ['rgba(42, 208, 144, 1)', 'rgba(97, 185, 197, 1)'];

  // const dataList = ['', '-环比'];
  const dataList = [''];
  const allSelectIndex = ref<number | null>(null);

  const getLendData = (data) => {
    if (!data) return [];
    return dataList.map((_, index) => (index > 0 ? data.name1 : data.name));
  };

  const getXAxisData = (data: BarChartData[]) => {
    return data.map((item) => item.collectDateTime);
  };

  const getMax = () => {
    // console.log(data);
    // const dataArr = data.filter((item) => item !== null);
    // return dataArr.length <= 0 ? 1 : Math.max(...dataArr) * 1.1;
    return 130;
  };

  const getMin = () => {
    // console.log(data);
    // const dataArr = data.flat().filter((item) => item !== null);
    // return Math.min(...dataArr);
    return 0;
  };

  const getValueFormatter = (value, digit = 2) => {
    return Number(value).toFixed(digit);
  };

  const getYAxisData = (data: BarChartData[]) => {
    // const mergeDatas = data
    //   .map((item) => {
    //     return [item.value, item.value1];
    //   })
    //   .flat()
    //   .filter((i) => i !== null && i !== undefined);
    const max = getMax();
    const min = getMin();

    const result = {
      type: 'value',
      name: data[0].unit ? `电费(${data[0].unit})` : '',
      max,
      min,
      interval: (max - min) / 5,
      nameTextStyle: {
        fontSize: getScaleValByClientWidth(13),
        color: '#fff',
        align: 'left',
        padding: [0, 0, 0, getScaleValByClientWidth(-30)],
      },
      axisLabel: {
        show: true,
        color: '#fff',
        fontSize: getScaleValByClientWidth(13),
        formatter: (value) => getValueFormatter(value, 0),
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(210, 255, 247, 0.40)',
          type: [5, 10],
          offset: 5,
        },
      },
    };

    return [result];
  };

  const getSeriesData = (data: BarChartData[], pictorialBarIndex) => {
    const setiseData = dataList.map((_, index) => {
      let symbolStr: undefined | string = undefined;
      data.map((i) => {
        if (pictorialBarIndex === null) {
          if (index % 2 && i.value1) {
            symbolStr = undefined;
          } else if (index % 2 === 0 && i.value) {
            symbolStr = undefined;
          } else {
            symbolStr = 'none';
          }
        } else {
          if (pictorialBarIndex === index) {
            if (index % 2 && i.value1) {
              symbolStr = undefined;
            } else if (index % 2 === 0 && i.value) {
              symbolStr = undefined;
            } else {
              symbolStr = 'none';
            }
          } else {
            symbolStr = 'none';
          }
        }
      });
      return [
        {
          name: 'pictorialBar',
          type: 'pictorialBar',
          legendHoverLink: false,
          symbolSize: ['100%', 4],
          symbolOffset: [0, -3],
          barMaxWidth: 16,
          symbolPosition: 'end',
          barGap: pictorialBarIndex === null ? '10%' : '-100%',
          z: 12,
          data: data.map((i) => ({
            value: index % 2 ? i.value1 : i.value,
            symbol: symbolStr,
            itemStyle: {
              color: colorList[index % 2][2],
            },
          })),
          tooltip: {
            show: false,
          },
        },
        {
          name: index % 2 ? data[0].name1 : data[0].name,
          type: 'bar',
          barMaxWidth: 16,
          yAxisIndex: 0,
          itemStyle: {
            color: itemColorList[index % 2],
          },
          data: data.map((i) => ({
            value: index % 2 ? i.value1 : i.value,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: colorList[index % 2][0],
                  },
                  {
                    offset: 0.5,
                    color: colorList[index % 2][1],
                  },
                  {
                    offset: 1,
                    color: colorList[index % 2][0],
                  },
                ],
              },
            },
          })),
        },
      ];
    });

    return setiseData.flat();
  };

  const setChart = () => {
    const { data } = props;

    const options = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#E1F3F1',
        borderColor: '#02695E',
        appendToBody: true,
        formatter: (params) => {
          return `<span style="font-size: 0.73vw; line-height: 1.5;">${
            Number(dayjs(params[0].axisValue).format('MM')) + '月'
          }</span></br>
          ${params
            .map((item, index) => {
              return `
            ${item.marker}&nbsp;<span style="font-size: 0.73vw;line-height: 1.5;">${
                item.seriesName
              }</span>&nbsp;<span >${
                item.value !== '' && item.value !== null && item.value !== undefined
                  ? `<span style="font-weight: 600;font-size: 0.73vw;line-height: 1.5;">${getValueFormatter(
                      item.value,
                      data[index].digit,
                    )} </span>&nbsp;<span style="font-weight: 600;font-size: 0.73vw;line-height: 1.5;">${
                      data[index]?.unit
                    }</span>`
                  : '-'
              }</span>&nbsp;
            `;
            })
            .join('</br>')}
          `;
        },
        // valueFormatter: (value) => {
        //   return value !== '' && value !== null && value !== undefined
        //     ? `${getValueFormatter(value, data[0].digit)} ${data[0]?.unit}`
        //     : '-';
        // },
      },
      legend: {
        show: false,
        data: getLendData(data[0]),
        top: 0,
        right: 0,
        itemWidth: getScaleValByClientWidth(8),
        itemHeight: getScaleValByClientWidth(8),
        itemGap: 8,
        icon: 'circle',
        textStyle: {
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          lineHeight: getScaleValByClientWidth(13 * 1.25),
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
        },
      },
      grid: {
        left: 8,
        right: 8,
        bottom: 0,
        top: getScaleValByClientWidth(35),
        containLabel: true,
        show: true,
        backgroundColor: 'rgba(2, 53, 32, 0.40)',
        borderWidth: 0,
      },
      xAxis: [
        {
          type: 'category',
          data: getXAxisData(data),
          boundaryGap: true,
          axisLabel: {
            show: true,
            color: '#fff',
            fontSize: getScaleValByClientWidth(13),
            padding: [0, 5, 0, 0],
            formatter: (value) => {
              return Number(dayjs(value).format('MM')) + '月';
            },
          },
          axisTick: {
            show: false,
          },
          axisPointer: {
            show: true,
            type: 'line',
            lineStyle: {
              color: 'rgba(178, 255, 241, 1)',
              type: [5, 10],
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(178, 255, 241, 1)',
              shadowColor: 'rgba(178, 255, 241, 1)',
              shadowOffsetY: -2,
              shadowOffsetX: 0,
              shadowBlur: 5,
              width: 1,
            },
          },
        },
      ],
      yAxis: getYAxisData(data),
      series: getSeriesData(data, allSelectIndex.value),
    };

    setOptions(options as any, false);

    // const chartInstance = getInstance();
    // if (chartInstance) {
    //   chartInstance.off('legendselectchanged');
    //   chartInstance.on('legendselectchanged', (params: any) => {
    //     const hasSelected = Object.values(params.selected).some(Boolean);
    //     const hasAllSelected = Object.values(params.selected).every(Boolean);
    //     if (hasAllSelected) {
    //       allSelectIndex.value = null;
    //     } else {
    //       if (hasSelected) {
    //         const key = Object.values(params.selected);
    //         allSelectIndex.value = key.findIndex((i) => i);
    //       } else {
    //         allSelectIndex.value = -1;
    //       }
    //     }
    //     const options = chartInstance.getOption();
    //     setOptions({
    //       ...options,
    //       series: getSeriesData(data, allSelectIndex.value),
    //     });
    //   });
    // }
  };

  watch(
    () => props.data,
    () => {
      setChart();
    },
    { deep: true },
  );

  const getQuestion = (data) => {
    if (!props.aiQuestionTemplate || !props.aiQuestionTemplate.includes('${data}')) {
      return props.aiQuestionTemplate;
    }
    const str = data
      .map((item) => {
        return `${Number(dayjs(item.collectDateTime).format('MM'))}月${
          item.value !== null && item.value !== undefined
            ? Number(item.value).toFixed(item.digit ?? 2)
            : '-'
        }${item.unit}`;
      })
      .join('、');
    return props.aiQuestionTemplate.replace('${data}', str);
  };

  const question = computed(() => {
    const data = props.data;
    return getQuestion(data);
  });
</script>
<style lang="less" scoped>
  @media screen and (min-width: 1800px) {
    .electricity-content {
      .px2vw(6);
      padding: @vw;
    }
  }
</style>
