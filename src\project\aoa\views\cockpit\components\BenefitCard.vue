<template>
  <CockpitCardBox title="同比经济效益">
    <template #title-slot>
      <AiCard
        :questionTemplate="question"
        questionTitle="同比经济效益"
        :deepThinking="deepThinking"
      />
    </template>
    <template #content>
      <div class="benefit-content w-full h-full">
        <!-- <div class="all-container"></div> -->
        <div class="chart-box">
          <div ref="chartRef" class="chart w-full h-full"></div>
          <div class="all-box">
            <div class="all-value">
              <div class="value">
                <span class="value-num">{{
                  props.all !== null ? `${Number(props.all).toFixed(0)}` : '-'
                }}</span>
                <span class="value-level">{{
                  props.all !== null ? `${legendData[0]?.unit ?? '万'}` : ''
                }}</span>
              </div>
            </div>
            <div class="all-name">总收益</div>
          </div>
        </div>
        <div class="legend-box">
          <div class="item" v-for="(item, index) in legendData" :key="index">
            <div class="legend-header">
              <div class="icon"></div>
              <div class="name">{{ item.name }}</div>
            </div>
            <div class="value">
              {{ item.value !== null ? `${Number(item.value).toFixed(2)}${item.unit}` : '-' }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </CockpitCardBox>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  // import { formatNumber } from '/@aoa/utils/number';
  import { CockpitCardBox } from './CockpitCard';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { AiCard } from '/@aoa/components/AiCard';
  import { getScaleValByClientWidth } from '../data';
  import circleGraphic from '../assets/images/icon_1.png';

  const props = defineProps({
    data: {
      type: Array as any,
      default: () => [],
    },
    all: {
      type: Number,
      default: null,
    },
    first: Boolean,
    deepThinking: {
      type: Boolean,
      default: false,
    },
    aiQuestionTemplate: {
      type: String,
      default: '',
    },
  });

  const chartRef = ref(null);
  const { setOptions } = useECharts(chartRef as any);
  const colorList = ['rgba(31, 195, 164, 1)', 'rgba(253, 182, 75, 1)'];

  const legendData = ref<{ name: string; value: number; unit: string }[]>([]);
  const getLegendData = (data) => {
    legendData.value = data.map((item) => ({
      name: item.name,
      value: item.value,
      unit: item.unit,
    }));
  };

  const getSeriesData = (data) => {
    return {
      type: 'pie',
      radius: [getScaleValByClientWidth(50), getScaleValByClientWidth(65)],
      center: ['50%', '49%'],
      hoverAnimation: false,
      clockWise: false,
      itemStyle: {
        color: (params) => {
          return colorList[params.dataIndex];
        },
      },
      label: {
        alignTo: 'edge',
        formatter: '{name|{d}%}',
        minMargin: 5,
        edgeDistance: 10,
        lineHeight: 15,
        rich: {
          name: {
            fontSize: getScaleValByClientWidth(16),
            color: '#fff',
          },
        },
      },
      labelLine: {
        length: 10,
        length2: 15,
        maxSurfaceAngle: 80,
        lineStyle: {
          color: '#fff',
        },
      },
      // labelLayout: function (params) {
      //   // const isLeft = params.labelRect.x < myChart.getWidth() / 2;
      //   const isLeft = true;
      //   const points = params.labelLinePoints;
      //   // Update the end point.
      //   points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width;
      //   return {
      //     labelLinePoints: points,
      //   };
      // },
      data: data.map((item) => ({
        value: item.value,
        name: item.name,
      })),
    };
  };

  const setChart = () => {
    const { data } = props;
    getLegendData(data);
    // if (!series || !series[0] || !series[0].data[0]?.totalvalue) {
    //   return;
    // }
    // const isW = hasMax([{ data: [series[0].data[0]?.totalvalue || 0] }]);
    // const titleText = formatterNum(series[0].data[0]?.totalvalue || 0);
    // function formatterNum(value) {
    //   const val = isW ? `${roundFun(value / 10000, 1)}万` : value;
    //   return val;
    // }

    const option = {
      legend: {
        show: false,
      },
      graphic: {
        elements: [
          {
            type: 'image',
            style: {
              image: circleGraphic,
              width: getScaleValByClientWidth(168),
              height: getScaleValByClientWidth(168),
            },
            left: 'center',
            top: 'center',
          },
        ],
      },

      series: getSeriesData(data),
    };

    setOptions(option as any);
  };

  watch(
    () => props.first,
    () => {
      setChart();
    },
    { deep: true },
  );

  const getQuestion = (data) => {
    if (!props.aiQuestionTemplate || !props.aiQuestionTemplate.includes('${data}')) {
      return props.aiQuestionTemplate;
    }
    const str = data
      .map((item, index) => {
        return `${index % 2 ? '预计' : '同比'}${item.name}${item.value}${item.unit}`;
      })
      .join('、');
    return props.aiQuestionTemplate.replace('${data}', str);
  };

  const question = computed(() => {
    const data = props.data;
    return getQuestion(data);
  });
</script>
<style lang="less" scoped>
  .benefit-content {
    display: flex;
    flex-direction: column;

    .chart-box {
      position: relative;
      flex: 1;
      width: 100%;

      .all-box {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-content: center;
        text-align: center;
        z-index: 1;

        .all-name {
          .px2vw(4);
          padding-top: @vw;
          .font-size(16);
          line-height: 1;
        }

        .all-value {
          font-family: Alimama ShuHeiTi;
          .font-size(24);
          line-height: 1;
          display: flex;
          justify-content: center;
          align-items: center;

          .value-level {
            .font-size(22);
          }
        }
      }
    }
  }

  .legend-box {
    padding-bottom: 8px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    .px2vw(50);
    gap: 0 @vw;

    .item {
      .legend-header {
        display: flex;
        align-items: center;

        .icon {
          width: 11px;
          height: 11px;
          border: 1px solid #fff;
          background: rgba(31, 195, 164, 1);
        }

        .name {
          padding-left: 8px;
          .font-size(14);
          color: #ebebeb;
          line-height: 1;
        }
      }

      &:last-child {
        .legend-header {
          .icon {
            background: rgba(253, 182, 75, 1);
          }
        }
      }

      .value {
        padding-top: 8px;
        padding-left: 19px;
        .font-size(16);
        color: #fff;
        line-height: 1;
        font-weight: 600;
      }
    }
  }

  @media screen and (min-width: 1800px) {
    .benefit-content {
      .px2vw(6);
      padding: @vw;
    }
  }
</style>
