<template>
  <div class="ellipsis-tooltip">
    <div ref="textRef" class="ellipsis-text">
      <Tooltip :title="text" v-if="isOverflow" :placement="placement">
        {{ text }}
      </Tooltip>
      <span v-else>{{ text }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import { Tooltip } from 'ant-design-vue';

  defineProps({
    text: {
      type: String,
      required: true,
    },
    placement: {
      type: String as any,
    },
  });

  const textRef = ref(null);
  const isOverflow = ref(false);

  const checkOverflow = () => {
    const element = textRef.value;
    if (element) {
      isOverflow.value = element?.scrollWidth > element?.clientWidth;
    }
  };

  const resizeObserver = new ResizeObserver(checkOverflow);

  onMounted(() => {
    const element = textRef.value;
    if (element) {
      resizeObserver.observe(element);
      checkOverflow();
    }
  });

  onBeforeUnmount(() => {
    if (textRef.value) {
      resizeObserver.unobserve(textRef.value);
    }
  });
</script>

<style scoped lang="less">
  .ellipsis-text {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
