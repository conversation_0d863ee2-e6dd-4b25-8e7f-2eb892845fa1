<template>
  <div :class="prefixCls">
    <IPCFactory />
    <IPCMenu />
    <SettingDrawer />
  </div>
</template>

<script lang="ts" setup>
  import SettingDrawer from './SettingDrawer.vue';
  import IPCMenu from './IpcMenu.vue';
  import IPCFactory from './IpcFactory.vue';
  import { useDesign } from '/@/hooks/web/useDesign';

  const { prefixCls } = useDesign('aoa-ipc-header');
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-aoa-ipc-header';
  @ipcPrefixCls: ~'@{namespace}-aoa-ipc-header-bottom';

  .@{ipcPrefixCls} {

    display: flex;

    justify-content: space-between;
    align-items: center;

  }
  .@{prefix-cls}{
    padding: 24px 24px 16px 24px;
    height: 80px;
    gap: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #e1f3f1;
  }
</style>
