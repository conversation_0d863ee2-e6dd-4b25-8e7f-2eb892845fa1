<template>
  <div class="editor-header">
    <div class="left">
      <img :src="arrowLeft" class="icon" @click="goBack" v-show="false" />
      <span class="device">{{ flowName }}</span>
    </div>
    <div class="center editor-header-center">
      <Menu />
    </div>
    <div class="right">
      <div class="item" v-for="item in actions" :key="item.key" @click="dispatchFunc(item.action)">
        <img :src="item.icon" alt="" class="icon" />
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useProcess } from '/@process-editor/hooks/useProcess';
  import { openWindow } from '/@/utils';
  import save from '/@process-editor/assets/images/save.png';
  import arrowLeft from '/@process-editor/assets/images/arrow-left.png';
  import view from '/@process-editor/assets/images/preview.png';
  import {
    getFactoryId,
    saveFlowDataDetail,
    preprocessSaveKitDataFn,
  } from '/@process-editor/utils';
  import { saveShowModelDetailApi } from '/@process-editor/api/index';
  import { MODEL_INFO, UNSAVED_KITS, SCALE_SYNC } from '/@process-editor/constant/process';
  import Menu from './Menu.vue';

  declare const meta2d;

  const route = useRoute();
  const { createMessage } = useMessage();

  const { getEditorLocalStorage } = useProcess();

  const flowName = computed(() => route.query.flowName || '');

  const actions = [
    { key: 'save', icon: save, name: '保存', action: 'saveFile' },
    { key: 'view', icon: view, name: '预览', action: 'preview' },
    // { key: 'back', icon: back, name: '返回', action: 'goBack' },
  ];

  const actionFunc = { saveFile, preview, goBack };

  function dispatchFunc(act) {
    actionFunc[act]();
  }

  async function saveFile() {
    if (validateScaleSync()) {
      await autoSave();
      createMessage.success('保存成功');
    }
  }

  /**
   * 保存画布中所有的套件
   */
  async function autoSave() {
    async function saveShowModelDetail() {
      const unsavedKits = getEditorLocalStorage(UNSAVED_KITS);
      console.log('unsavedKits', unsavedKits);

      if (unsavedKits?.length) {
        const modelInfo = getEditorLocalStorage(MODEL_INFO);

        async function saveEditKit() {
          if (modelInfo.id) {
            await saveShowModelDetailApi({
              detailModelId: modelInfo.id,
              kits: preprocessSaveKitDataFn(unsavedKits),
            });
          }
        }

        await saveEditKit();
      }
    }

    const modelInfo = getEditorLocalStorage(MODEL_INFO);

    const flowId = route.query.flowId || '';

    let res = await saveFlowDataDetail(
      { flowId, version: modelInfo.flowVersion || route.query.version || '' },
      {
        paramsProcessFn: (params) => {
          let dataJson = params.dataJson;
          const optionsData = meta2d.getOptions();
          dataJson.refreshTime = optionsData.refreshTime || 1000 * 10;
        },
      },
    );

    await saveShowModelDetail();

    return res;
  }

  function validateScaleSync() {
    const scaleSync = getEditorLocalStorage(SCALE_SYNC);
    if (!scaleSync) {
      createMessage.info('缩放后请更新套件，否则预览时可能会出现数据异常');
    }

    return scaleSync;
  }

  async function preview() {
    if (validateScaleSync()) {
      // const { flowId } = await autoSave();
      // 先停止动画，避免数据波动
      // meta2d.stopAnimate();
      // 跳转到预览页面
      const flowId = route.query.flowId || '';
      openWindow('/#/process-preview', {
        param: {
          flowId,
          factoryId: getFactoryId(),
        },
        target: '_blank',
      });
    }
  }

  function goBack() {
    window.history.back();
  }
</script>

<style lang="less" scoped>
  .editor-header {
    padding: 0 16px;
    height: 56px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #cccccc;
    background-color: #fff;

    .left {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;

      .icon {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }

      .device {
        flex-shrink: 0;
      }
    }

    .center {
      height: 56px;
      flex: 1;
      display: flex;
      justify-content: center;
      overflow: hidden;

      :deep(.ant-menu) {
        color: #666666;
      }
    }

    .right {
      display: flex;
      gap: 16px;

      .item {
        width: 52px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: @theme-color;
        cursor: pointer;

        .icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
</style>
