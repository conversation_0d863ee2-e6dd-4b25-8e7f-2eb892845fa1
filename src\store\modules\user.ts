import type { UserInfo } from '/#/store';
import type { ErrorMessageMode } from '/#/axios';
import { defineStore } from 'pinia';
import { store } from '/@/store';
import { RoleEnum } from '/@/enums/roleEnum';
import { PageEnum } from '/@/enums/pageEnum';
import {
  ROLES_KEY,
  TOKEN_KEY,
  USER_INFO_KEY,
  USER_DESIGN_KEY,
  FACTORY_KEY,
  UPDATE_GLOBAL_RESOURCE_PARAM,
} from '/@/enums/cacheEnum';
import { getAuthCache, setAuthCache } from '/@/utils/auth';
import { LoginParams } from '/@/api/sys/model/userModel';
import { getCountMsgApi } from '/@/api/sys/msg';
import { doLogout, getUserInfo, loginApi } from '/@/api/sys/user';
import { getParamKeyApi } from '/@/api/admin/param';
import { ParamsKeyEnum } from '/@/enums/appEnum';

import { useI18n } from '/@/hooks/web/useI18n';
import { useMessage } from '/@/hooks/web/useMessage';
import { useDomain } from '/@/locales/useDomain';
import { initAppConfigStore } from '/@/logics/initAppConfig';

import { router } from '/@/router';
import { usePermissionStore } from '/@/store/modules/permission';
import { RouteRecordRaw } from 'vue-router';
import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';
import { isArray } from '/@/utils/is';
import { h } from 'vue';
import { useFlowStoreWithout } from '/@/store/modules/flow';
import type { Menu } from '/@/router/types';
import { createSessionStorage } from '/@/utils/cache';
import { METER_BAR } from '/@metering/enums/cacheEnum';
import { createLocalStorage } from '/@/utils/cache';
import { SINGLE_LOGIN } from '/@/enums/cacheEnum';
import { useEventBus } from '@vueuse/core';

const ss = createSessionStorage();

interface UserState {
  userInfo: Nullable<UserInfo>;
  token?: string;
  roleList: RoleEnum[];
  sessionTimeout?: boolean;
  lastUpdateTime: number;
  msgCount: number;
  specialUser: boolean;
  isFullFooterWidth: boolean;
  showDesign: boolean;
  isSystemModalOpenCount: number;
  isSystemModalHandler: boolean;
}

export const useUserStore = defineStore({
  id: 'app-user',
  state: (): UserState => ({
    // user info
    userInfo: null,
    // token
    token: undefined,
    // roleList
    roleList: [],
    // Whether the login expired
    sessionTimeout: false,
    // Last fetch time
    lastUpdateTime: 0,
    // 特殊用户,用来判断是否嵌入其他系统
    specialUser: false,
    msgCount: 0,
    // BasicDetail 组件footer是否宽度为100%
    isFullFooterWidth: false,
    // 是否显示设计模型
    showDesign: false,
    // 加药当前系统弹窗数量
    isSystemModalOpenCount: 0,
    // 加药系统弹窗是否处于交互状态
    isSystemModalHandler: false,
  }),
  getters: {
    getGlobalSourceMap() {
      return getAuthCache<Indexable>(UPDATE_GLOBAL_RESOURCE_PARAM) || {};
    },
    getUserInfo(): UserInfo {
      return this.userInfo || getAuthCache<UserInfo>(USER_INFO_KEY) || {};
    },
    getToken(): string {
      return this.token || getAuthCache<string>(TOKEN_KEY);
    },
    getRoleList(): string[] {
      return this.roleList.length > 0 ? this.roleList : getAuthCache<string[]>(ROLES_KEY);
    },
    getSessionTimeout(): boolean {
      return !!this.sessionTimeout;
    },
    getLastUpdateTime(): number {
      return this.lastUpdateTime;
    },
    getSpecialUser(): boolean {
      return this.specialUser;
    },
    getMsgCount(): number {
      return this.msgCount;
    },
    getIsFullFooterWidth(): boolean {
      return this.isFullFooterWidth;
    },
    getShowDesign(): boolean {
      return this.showDesign || getAuthCache<boolean>(USER_DESIGN_KEY);
    },
    getCurrentFactoryId(): string {
      return getAuthCache(FACTORY_KEY) || this.userInfo?.bindSourceUniqueId || '';
    },
  },
  actions: {
    setToken(info: string | undefined) {
      this.token = info ? info : ''; // for null or undefined value
      setAuthCache(TOKEN_KEY, info);
    },
    setRoleList(roleList: RoleEnum[]) {
      this.roleList = roleList;
      setAuthCache(ROLES_KEY, roleList);
    },
    setGLobalSource(info: any = {}) {
      const data = Object.assign(this.getGlobalSourceMap, info);
      setAuthCache(UPDATE_GLOBAL_RESOURCE_PARAM, data);
    },
    setUserInfo(info: UserInfo | null) {
      this.userInfo = info;
      this.lastUpdateTime = new Date().getTime();
      setAuthCache(USER_INFO_KEY, info);
      this.setGLobalSource(info);
      this.checkFactoryId();
    },
    setSessionTimeout(flag: boolean) {
      this.sessionTimeout = flag;
    },
    setSpecialUser(flag: boolean) {
      this.specialUser = flag;
    },
    setIsFullFooterWidth(flag: boolean) {
      this.isFullFooterWidth = flag;
    },
    setShowDesign(flag: boolean) {
      this.showDesign = flag;
      setAuthCache(USER_DESIGN_KEY, flag);
    },
    resetState() {
      this.userInfo = null;
      this.token = '';
      this.roleList = [];
      this.sessionTimeout = false;
    },
    setIsSystemModalOpenCount(n: number) {
      this.isSystemModalOpenCount = n;
    },
    setIsSystemModalHandler(flag: boolean) {
      this.isSystemModalHandler = flag;
    },
    setFactoryId(factoryId: string) {
      const ls = createLocalStorage();
      ls.set(FACTORY_KEY, factoryId);
      if (this.userInfo) {
        this.userInfo.bindSourceUniqueId = factoryId;
      }
      this.setGLobalSource({ factoryId });
    },
    checkFactoryId() {
      // 检查并切换水厂
      const ls = createLocalStorage();
      const factoryId = ls.get(FACTORY_KEY);
      const factoryInfoList = this.userInfo?.factoryInfoList || [];
      if (!factoryInfoList.length && factoryId) {
        this.setFactoryId(factoryId);
        useEventBus(FACTORY_KEY).emit(factoryId);
      }
    },
    /**
     * @description: login
     */
    async login(
      params: LoginParams & {
        goHome?: boolean;
        mode?: ErrorMessageMode;
        singleLogin?: boolean;
        toPageInfo?: Indexable;
        params?: Indexable;
      },
    ): Promise<UserInfo | null> {
      const { goHome = true, mode, singleLogin, toPageInfo, ...loginParams } = params;

      const data = await loginApi(loginParams, mode);

      const { token_type, access_token } = data;
      if (!access_token) throw data;
      const token = token_type + ' ' + access_token;
      this.setToken(token);

      const { updateProjectSetting } = useDomain();
      updateProjectSetting();

      return await this.afterLoginAction(goHome, singleLogin, toPageInfo);
    },
    async afterLoginAction(
      goHome?: boolean,
      singleLogin = false,
      pageInfo?: Indexable,
    ): Promise<UserInfo | null> {
      if (!this.getToken) return null;
      const ls = createLocalStorage();
      // get user info
      const userInfo = await this.getUserInfoAction();
      const designCode = (await getParamKeyApi(ParamsKeyEnum.DESIGN)) || '';
      const roles = this.getRoleList;
      //   是否显示设计模型
      const showDesign = roles.some((role) => designCode.includes(role));

      this.setShowDesign(showDesign);
      const sessionTimeout = this.sessionTimeout;
      if (sessionTimeout) {
        this.setSessionTimeout(false);
      } else {
        const permissionStore = usePermissionStore();

        let activeMenuParam: Indexable | null = null;
        if (singleLogin) {
          ls.set(SINGLE_LOGIN, true);
          activeMenuParam = {
            moduleId: pageInfo?.meta!.moduleId,
          };
        }

        permissionStore.setFirstMenuParams(activeMenuParam);
        if (!permissionStore.isDynamicAddedRoute) {
          const routes = await permissionStore.buildRoutesAction(false, false, singleLogin);
          routes.forEach((route) => {
            router.addRoute(route as unknown as RouteRecordRaw);
          });
          router.addRoute(PAGE_NOT_FOUND_ROUTE as unknown as RouteRecordRaw);
          permissionStore.setDynamicAddedRoute(true);
          permissionStore.patchHomeAffix();
        }

        try {
          if (goHome) {
            const paths = router
              .getRoutes()
              .map((r) => r.path)
              .filter((p) => !!p);

            const url = this.loginGoHome(permissionStore, paths);
            router.replace(url);
          }
        } catch (e) {
          throw e;
        }
      }
      return userInfo;
    },
    loginGoHome(permissionStore, paths: string[]) {
      // const { createMessage } = useMessage();
      const list: Menu[] = permissionStore.getFirstMenuList;
      let url = permissionStore.getFirstMenuParams?.redirect;

      if (url) return url;
      if (permissionStore.getFirstMenuParams?.redirect)
        for (const { children, redirect, path } of list) {
          url = redirect || path;

          if (paths.includes(url)) {
            return url;
          } else if (children) {
            url = this.loginGoHome(children, paths);
            if (url) return url;
          }
        }
      return url;
    },
    async getUserInfoAction(): Promise<UserInfo | null> {
      if (!this.getToken) return null;
      const userInfo = await getUserInfo();
      const { roles = [] } = userInfo;
      if (isArray(roles)) {
        // const roleList = roles.map((item) => item.value) as RoleEnum[];
        this.setRoleList(roles as RoleEnum[]);
      } else {
        userInfo.roles = [];
        this.setRoleList([]);
      }
      this.setUserInfo(userInfo.sysUser);
      return userInfo.sysUser;
    },
    /**
     * @description: logout
     */
    async logout(goLogin = false, type?: string) {
      try {
        await doLogout();
        const { getDomain, getProjectCode } = useDomain();
        const ls = createLocalStorage();
        const permissionStore = usePermissionStore();
        this.setUserInfo(null);
        this.setToken(undefined);
        ls.set(SINGLE_LOGIN, false);
        // changeTenantId(undefined);
        this.setSessionTimeout(false);
        permissionStore.setFirstMenuParams(null);
        this.setFactoryId('');
        ls.set(FACTORY_KEY, null);
        ls.remove(FACTORY_KEY);
        setAuthCache(FACTORY_KEY, null);
        this.setGLobalSource(null);
        if (goLogin) {
          let query = `?domain=${getDomain.value ?? getProjectCode.value ?? 'aoa'}`;
          if (type !== 'confirm' && router.currentRoute.value.path !== '/login') {
            query += `&redirect=${router.currentRoute.value.path}`;
          }
          await router.push(PageEnum.BASE_LOGIN + query);
        }

        /** 重置项目配置 */
        initAppConfigStore();
        ss.remove(METER_BAR);
      } catch (e) {
        console.log('注销Token失败');
        throw e;
      }
    },

    /**
     * @description: Confirm before logging out
     */
    confirmLoginOut(dialogClass?: string, maskStyle?: any) {
      const { createConfirm } = useMessage();
      const { t } = useI18n();
      createConfirm({
        iconType: 'warning',
        wrapClassName: dialogClass,
        maskStyle,
        title: () => h('span', t('sys.app.logoutTip')),
        content: () => h('span', t('sys.app.logoutMessage')),
        onOk: async () => {
          const flowStore = useFlowStoreWithout();

          if (flowStore.getFlowReleaseParams && !flowStore.isUnlock) {
            await flowStore.releaseFlowProcess(flowStore.getFlowReleaseParams);
          }

          const arr =
            flowStore.getFlowAttachFiles?.filter((i) => i.new)?.map((i) => i.realfileName) ?? [];
          if (arr.length) {
            await flowStore.delAttachFilesRoute(arr, this.getUserInfo.userId as string);
          }
          await this.logout(true, 'confirm');
        },
      });
    },
    async resetProjectMsgCount() {
      const ret = await getCountMsgApi();
      this.msgCount = ret;
      return ret;
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
