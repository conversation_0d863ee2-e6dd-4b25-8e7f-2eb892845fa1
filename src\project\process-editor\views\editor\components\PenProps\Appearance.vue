<template>
  <div class="appearanceProps">
    <Form :form-list="showMap" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" />
  </div>
</template>

<script setup>
  import { computed, onMounted, reactive, ref, toRaw } from 'vue';
  import { cloneDeep } from 'lodash-es';
  import Form from '../Form.vue';
  import { appearanceProps } from '/@process-editor/views/editor/data/defaultsConfig.js';
  import { mergeProps } from '/@process-editor/views/editor/data/utils.js';
  import { deepClone } from 'hlxb-meta2d-core';
  import { uploadApi } from '/@process-editor/api/index';
  import { addResourcePrefix } from '/@process-editor/utils/index';

  // 记录是否有选中多个图元
  const multiPen = ref(false);
  const defaultConfig = deepClone(appearanceProps); //深拷贝保存默认配置
  let m = reactive(appearanceProps); // 响应式数据源
  let activePen = {};

  // 更新属性方法
  function updateFunc(prop) {
    return (value) => {
      if (multiPen.value) {
        for (let i of activePen) {
          meta2d.setValue(
            {
              id: i.id,
              [prop]: value,
            },
            { render: false },
          );
        }
        meta2d.render();
      } else {
        meta2d.setValue({
          id: activePen.id,
          [prop]: value,
        });
      }
    };
  }

  // function updateFuncImgType() {
  //   return (value) => {
  //     if (multiPen.value) {
  //       for (let i of activePen) {
  //         meta2d.setValue(
  //           {
  //             id: i.id,
  //             image: value,
  //             width: value === i.Detail2DPicUrl ? i.Detail2DPicWidth : i.ResourcePicWidth,
  //             height: value === i.Detail2DPicUrl ? i.Detail2DPicHeight : i.ResourcePicHeight,
  //           },
  //           { render: false },
  //         );
  //       }
  //       meta2d.render();
  //     } else {
  //       meta2d.setValue({
  //         id: activePen.id,
  //         image: value,
  //         width:
  //           value === activePen.Detail2DPicUrl
  //             ? activePen.Detail2DPicWidth
  //             : activePen.ResourcePicWidth,
  //         height:
  //           value === activePen.Detail2DPicUrl
  //             ? activePen.Detail2DPicHeight
  //             : activePen.ResourcePicHeight,
  //       });
  //     }
  //   };
  // }

  onMounted(() => {
    meta2d.on('active', (args) => {
      // console.log("appearance", args);
      // 只修改一个
      if (args.length >= 1) {
        multiPen.value = args.length > 1;
        if (multiPen.value) {
          // 批量修改
          activePen = reactive(args);
          // 以最后一个图元信息为主
          for (let i of activePen) {
            mergeProps(m, i);
          }
        } else {
          // 修改一个
          activePen = reactive(args[0]);
          mergeProps(m, defaultConfig);
          mergeProps(m, activePen);
          const penRect = meta2d.getPenRect(toRaw(activePen));
          Object.assign(m, penRect);
        }
      }
    });
    // 更新数据  合并多个事件
    meta2d.on('update', () => {
      meta2d.emit('editPen');
    });
    meta2d.on('resizePens', () => {
      meta2d.emit('editPen');
    });
    meta2d.on('rotatePens', () => {
      meta2d.emit('editPen');
    });
    meta2d.on('valueUpdate', () => {
      meta2d.emit('editPen');
    });
    meta2d.on('editPen', () => {
      if (multiPen.value) {
        // 若有多个图元，则设置以最后一个图元为主
        for (let i of activePen) {
          mergeProps(m, i);
        }
      } else {
        mergeProps(m, activePen);
      }
    });
  });

  const map = [
    {
      title: '文字',
      multiShow: true,
      children: [
        {
          title: '文字内容',
          type: 'input',
          // option: {
          //   type: "textarea",
          // },
          prop: 'text',
          bindProp: m,
          event: 'change',
          func: updateFunc('text'),
        },
        {
          title: '字体名',
          type: 'select',
          multiShow: true,
          prop: 'fontFamily',
          option: {
            placeholder: '请选择字体',
            list: [
              {
                label: '宋体',
                value: '宋体',
              },
              {
                label: '黑体',
                value: '黑体',
              },
              {
                label: 'YouSheBiaoTiHei',
                value: 'YouSheBiaoTiHei',
              },
              {
                label: 'D-DIN-PRO',
                value: 'D-DIN-PRO',
              },
            ],
          },
          bindProp: m,
          event: 'change',
          func: updateFunc('fontFamily'),
        },
        {
          title: '字体大小',
          type: 'number',
          multiShow: true,
          prop: 'fontSize',
          bindProp: m,
          event: 'change',
          func: updateFunc('fontSize'),
        },
        {
          title: '字体颜色',
          type: 'color',
          multiShow: true,
          prop: 'textColor',
          bindProp: m,
          event: 'change',
          func: updateFunc('textColor'),
        },
        {
          title: '浮动字体颜色',
          type: 'color',
          multiShow: true,
          prop: 'hoverTextColor',
          bindProp: m,
          event: 'change',
          func: updateFunc('hoverTextColor'),
        },
        {
          title: '选中字体颜色',
          type: 'color',
          multiShow: true,
          prop: 'activeTextColor',
          bindProp: m,
          event: 'change',
          func: updateFunc('activeTextColor'),
        },
        {
          title: '文字背景颜色',
          type: 'color',
          multiShow: true,
          prop: 'textBackground',
          bindProp: m,
          event: 'change',
          func: updateFunc('textBackground'),
        },
        {
          title: '水平对齐',
          type: 'select',
          multiShow: true,
          prop: 'textAlign',
          option: {
            placeholder: '请选择对齐方式',
            list: [
              {
                label: '左对齐',
                value: 'left',
              },
              {
                label: '居中对齐',
                value: 'center',
              },
              {
                label: '右对齐',
                value: 'right',
              },
            ],
          },
          bindProp: m,
          event: 'change',
          func: updateFunc('textAlign'),
        },
        {
          title: '垂直对齐',
          type: 'select',
          multiShow: true,
          prop: 'textBaseline',
          option: {
            placeholder: '请选择对齐方式',
            list: [
              {
                label: '顶部对齐',
                value: 'top',
              },
              {
                label: '居中对齐',
                value: 'center',
              },
              {
                label: '底部对齐',
                value: 'bottom',
              },
            ],
          },
          bindProp: m,
          event: 'change',
          func: updateFunc('textBaseline'),
        },
        {
          title: '行高',
          type: 'number',
          multiShow: true,
          option: {
            step: 0.1,
          },
          prop: 'lineHeight',
          bindProp: m,
          event: 'change',
          func: updateFunc('lineHeight'),
        },
        {
          title: '换行',
          type: 'select',
          multiShow: true,
          prop: 'whiteSpace',
          option: {
            placeholder: '请选择换行方式',
            list: [
              {
                label: '默认',
                value: 'nowrap',
              },
              {
                label: '不换行',
                value: 'nowrap',
              },
              {
                label: '回车换行',
                value: 'pre-line',
              },
              {
                label: '永远换行',
                value: 'break-all',
              },
            ],
          },
          bindProp: m,
          event: 'change',
          func: updateFunc('whiteSpace'),
        },
        {
          title: '文字宽度',
          type: 'number',
          multiShow: true,
          option: {
            min: 0,
          },
          prop: 'textWidth',
          bindProp: m,
          event: 'change',
          func: updateFunc('textWidth'),
        },
        {
          title: '文字高度',
          type: 'number',
          multiShow: true,
          option: {
            min: 0,
          },
          prop: 'textHeight',
          bindProp: m,
          event: 'change',
          func: updateFunc('textHeight'),
        },
        {
          title: '超出省略',
          type: 'switch',
          prop: 'ellipsis',
          bindProp: m,
          event: 'change',
          func: updateFunc('ellipsis'),
        },
        {
          title: '隐藏文字',
          type: 'switch',
          prop: 'hiddenText',
          bindProp: m,
          event: 'change',
          func: updateFunc('hiddenText'),
        },
      ],
    },
    {
      title: '位置与大小',
      multiShow: false,
      children: [
        {
          title: 'x',
          type: 'number',
          prop: 'x',
          option: {
            placeholder: 'px',
          },
          bindProp: m,
          event: 'change',
          func(value) {
            meta2d.setValue({
              id: activePen.id,
              x: value,
            });
            meta2d.setPenRect(
              toRaw(activePen),
              {
                x: value,
                y: activePen.y,
                width: activePen.width,
                height: activePen.height,
              },
              false,
            );
            mergeProps(m, activePen);
            const penRect = meta2d.getPenRect(toRaw(activePen));
            Object.assign(m, penRect);
            meta2d.canvas.calcActiveRect();
            meta2d.render();
          },
        },
        {
          title: 'y',
          type: 'number',
          prop: 'y',
          option: {
            placeholder: 'px',
          },
          bindProp: m,
          event: 'change',
          func(value) {
            meta2d.setValue({
              id: activePen.id,
              y: value,
            });
            meta2d.setPenRect(
              toRaw(activePen),
              {
                x: activePen.x,
                y: value,
                width: activePen.width,
                height: activePen.height,
              },
              false,
            );
            mergeProps(m, activePen);
            const penRect = meta2d.getPenRect(toRaw(activePen));
            Object.assign(m, penRect);
            meta2d.canvas.calcActiveRect();
            meta2d.render();
          },
        },
        {
          title: '宽度',
          type: 'number',
          prop: 'width',
          bindProp: m,
          option: {
            min: 0,
          },
          event: 'change',
          func(value) {
            if (activePen.ratio) {
              meta2d.setValue({
                id: activePen.id,
                width: value,
                height: (value / activePen.width) * activePen.height,
              });
            } else {
              meta2d.setValue({
                id: activePen.id,
                width: value,
              });
            }
            mergeProps(m, activePen);
            const penRect = meta2d.getPenRect(toRaw(activePen));
            Object.assign(m, penRect);
            meta2d.canvas.calcActiveRect();
            meta2d.render();
          },
        },
        {
          title: '高度',
          type: 'number',
          prop: 'height',
          bindProp: m,
          event: 'change',
          func(value) {
            if (activePen.ratio) {
              meta2d.setValue({
                id: activePen.id,
                height: value,
                width: (value / activePen.height) * activePen.width,
              });
            } else {
              meta2d.setValue({
                id: activePen.id,
                height: value,
              });
            }
            mergeProps(m, activePen);
            const penRect = meta2d.getPenRect(toRaw(activePen));
            Object.assign(m, penRect);
            meta2d.canvas.calcActiveRect();
            meta2d.render();
          },
        },
        {
          title: '锁定宽高比',
          type: 'switch',
          prop: 'ratio',
          bindProp: m,
          event: 'change',
          func(value) {
            meta2d.setValue({
              id: activePen.id,
              ratio: value,
            });
            activePen.ratio = value;
            meta2d.render();
            mergeProps(m, activePen);
          },
        },
        {
          title: '圆角',
          type: 'number',
          prop: 'borderRadius',
          bindProp: m,
          event: 'change',
          option: {
            placeholder: '<1为比例',
            min: 0,
          },
          func: updateFunc('borderRadius'),
        },
        {
          title: '旋转',
          type: 'number',
          prop: 'rotate',
          bindProp: m,
          event: 'change',
          option: {
            placeholder: '角度',
          },
          func: updateFunc('rotate'),
        },
        {
          title: '内边距上',
          type: 'number',
          prop: 'paddingTop',
          bindProp: m,
          event: 'change',
          option: {
            placeholder: 'px',
          },
          func: updateFunc('paddingTop'),
        },
        {
          title: '内边距下',
          type: 'number',
          prop: 'paddingBottom',
          bindProp: m,
          event: 'change',
          option: {
            placeholder: 'px',
          },
          func: updateFunc('paddingBottom'),
        },
        {
          title: '内边距左',
          type: 'number',
          prop: 'paddingLeft',
          bindProp: m,
          event: 'change',
          option: {
            placeholder: 'px',
          },
          func: updateFunc('paddingLeft'),
        },
        {
          title: '内边距右',
          type: 'number',
          prop: 'paddingRight',
          bindProp: m,
          event: 'change',
          option: {
            placeholder: 'px',
          },
          func: updateFunc('paddingRight'),
        },
        {
          title: '进度',
          type: 'number',
          prop: 'progress',
          bindProp: m,
          event: 'change',
          option: {
            placeholder: 'px',
            min: 0,
            step: 0.1,
            max: 1,
          },
          func: updateFunc('progress'),
        },
        {
          title: '垂直进度',
          type: 'switch',
          prop: 'verticalProgress',
          bindProp: m,
          event: 'change',
          func: updateFunc('verticalProgress'),
        },
        {
          title: '水平翻转',
          type: 'switch',
          prop: 'flipX',
          bindProp: m,
          event: 'change',
          func: updateFunc('flipX'),
        },
        {
          title: '垂直翻转',
          type: 'switch',
          prop: 'flipY',
          bindProp: m,
          event: 'change',
          func: updateFunc('flipY'),
        },
      ],
    },
    {
      title: '样式',
      multiShow: true,
      children: [
        {
          title: '线条样式',
          type: 'select',
          multiShow: true,
          prop: 'dash',
          option: {
            placeholder: '线条样式',
            list: [
              {
                label: '直线',
                template:
                  '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" style="height: 20px;width: 80px;">\n' +
                  '                  <g fill="none" stroke="black" stroke-width="1">\n' +
                  '                    <path d="M0 9 l85 0"></path>\n' +
                  '                  </g>\n' +
                  '                </svg>',
                value: 0,
              },
              {
                label: '虚线',
                template:
                  '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" style="height: 20px;width: 80px;">\n' +
                  '                  <g fill="none" stroke="black" stroke-width="1">\n' +
                  '                    <path stroke-dasharray="5 5" d="M0 9 l85 0"></path>\n' +
                  '                  </g>\n' +
                  '                </svg>',
                value: 1,
              },
              {
                label: '点横线',
                template:
                  '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" style="height: 20px;width: 80px;">\n' +
                  '                  <g fill="none" stroke="black" stroke-width="1">\n' +
                  '                    <path stroke-dasharray="10 10 2 10" d="M0 9 l85 0"></path>\n' +
                  '                  </g>\n' +
                  '                </svg>',
                value: 2,
              },
            ],
          },
          bindProp: m,
          event: 'change',
          func(value) {
            const dash = [
              [0, 0],
              [5, 5],
              [10, 10, 2, 10],
            ];
            if (multiPen.value) {
              for (let i of activePen) {
                meta2d.setValue(
                  {
                    id: i.id,
                    lineDash: dash[value],
                  },
                  { render: false },
                );
              }
              meta2d.render();
            } else {
              activePen.dash = value;
              meta2d.setValue({
                id: activePen.id,
                lineDash: dash[value],
              });
            }
          },
        },
        {
          title: '连接样式',
          type: 'select',
          multiShow: true,
          option: {
            placeholder: '连接样式',
            list: [
              {
                label: '默认',
                value: 'miter',
              },
              {
                label: '圆形',
                value: 'round',
              },
              {
                label: '斜角',
                value: 'bevel',
              },
            ],
          },
          prop: 'lineJoin',
          bindProp: m,
          event: 'change',
          func: updateFunc('lineJoin'),
        },
        {
          title: '末端样式',
          type: 'select',
          multiShow: true,
          option: {
            placeholder: '末端样式',
            list: [
              {
                label: '默认',
                value: 'butt',
              },
              {
                label: '圆形',
                value: 'round',
              },
              {
                label: '方形',
                value: 'square',
              },
            ],
          },
          prop: 'lineCap',
          bindProp: m,
          event: 'change',
          func: updateFunc('lineCap'),
        },
        {
          title: '边框及线条颜色',
          type: 'color',
          multiShow: true,
          prop: 'color',
          bindProp: m,
          event: 'change',
          func: updateFunc('color'),
        },
        {
          title: '边框浮动颜色',
          type: 'color',
          multiShow: true,
          prop: 'hoverColor',
          bindProp: m,
          event: 'change',
          func: updateFunc('hoverColor'),
        },
        {
          title: '边框选中颜色',
          type: 'color',
          multiShow: true,
          prop: 'activeColor',
          bindProp: m,
          event: 'change',
          func: updateFunc('activeColor'),
        },
        {
          title: '线条宽度',
          type: 'number',
          multiShow: true,
          prop: 'lineWidth',
          bindProp: m,
          event: 'change',
          func: updateFunc('lineWidth'),
        },
        {
          title: '线条边框宽度',
          type: 'number',
          multiShow: true,
          prop: 'borderWidth',
          bindProp: m,
          event: 'change',
          func: updateFunc('borderWidth'),
        },
        {
          title: '线条边框颜色',
          type: 'color',
          multiShow: true,
          prop: 'borderColor',
          bindProp: m,
          event: 'change',
          func: updateFunc('borderColor'),
        },
        {
          title: '背景颜色',
          type: 'color',
          multiShow: true,
          prop: 'background',
          bindProp: m,
          event: 'change',
          func: updateFunc('background'),
        },
        {
          title: '浮动背景颜色',
          type: 'color',
          multiShow: true,
          prop: 'hoverBackground',
          bindProp: m,
          event: 'change',
          func: updateFunc('hoverBackground'),
        },
        {
          title: '选中背景颜色',
          type: 'color',
          multiShow: true,
          prop: 'activeBackground',
          bindProp: m,
          event: 'change',
          func: updateFunc('activeBackground'),
        },
        {
          title: '透明度',
          type: 'number',
          multiShow: true,
          prop: 'globalAlpha',
          bindProp: m,
          option: {
            min: 0,
            step: 0.1,
            max: 1,
          },
          event: 'change',
          func: updateFunc('globalAlpha'),
        },
        {
          title: '锚点颜色',
          type: 'color',
          prop: 'anchorColor',
          bindProp: m,
          event: 'change',
          func: updateFunc('anchorColor'),
        },
        {
          title: '锚点半径',
          type: 'number',
          prop: 'anchorRadius',
          bindProp: m,
          option: {
            min: 0,
            step: 1,
            max: 10,
          },
          event: 'change',
          func: updateFunc('anchorRadius'),
        },
        {
          title: '阴影颜色',
          type: 'color',
          prop: 'shadowColor',
          bindProp: m,
          event: 'change',
          func: updateFunc('shadowColor'),
        },
        {
          title: '阴影模糊',
          type: 'number',
          prop: 'shadowBlur',
          bindProp: m,
          option: {
            min: 0,
            step: 1,
            max: Infinity,
          },
          event: 'change',
          func: updateFunc('shadowBlur'),
        },
        {
          title: '阴影x偏移',
          type: 'number',
          prop: 'shadowOffsetX',
          bindProp: m,
          event: 'change',
          func: updateFunc('shadowOffsetX'),
        },
        {
          title: '阴影y偏移',
          type: 'number',
          prop: 'shadowOffsetY',
          bindProp: m,
          event: 'change',
          func: updateFunc('shadowOffsetY'),
        },
        {
          title: '文字阴影',
          type: 'switch',
          prop: 'textHasShadow',
          bindProp: m,
          event: 'change',
          func: updateFunc('textHasShadow'),
        },
        {
          title: '是否显示',
          type: 'switch',
          prop: 'visible',
          bindProp: m,
          event: 'change',
          func: updateFunc('visible'),
        },
      ],
    },
    {
      title: '图片',
      multiShow: false,
      children: [
        // {
        //   title: '图片类型',
        //   type: 'select',
        //   prop: 'imageType',
        //   option: {
        //     placeholder: '请选择图片源',
        //     list: [],
        //   },
        //   bindProp: m,
        //   event: 'change',
        //   func: updateFuncImgType(),
        // },
        {
          title: '图片',
          type: 'file',
          prop: 'image',
          bindProp: m, // 绑定的属性
          event: 'change',
          option: {
            accept: 'image/*',
            api: uploadApi,
          },
          func([file]) {
            const isGif = (file?.url || '').endsWith('.gif');
            const data = {
              id: activePen.id,
              image: addResourcePrefix(file?.url || ''),
            };
            if (isGif) {
              data.name = 'gif';
            }
            m.image = data.image;
            meta2d.setValue(data);
          },
        },
        {
          title: '宽度',
          type: 'number',
          prop: 'iconWidth',
          option: {
            placeholder: '自适应',
          },
          bindProp: m,
          event: 'change',
          func: updateFunc('iconWidth'),
        },
        {
          title: '高度',
          type: 'number',
          prop: 'iconHeight',
          option: {
            placeholder: '自适应',
          },
          bindProp: m,
          event: 'change',
          func: updateFunc('iconHeight'),
        },
        {
          title: '保持比例',
          type: 'switch',
          prop: 'imageRatio',
          bindProp: m,
          event: 'change',
          func: updateFunc('imageRatio'),
        },
        {
          title: '水平偏移',
          type: 'number',
          prop: 'iconLeft',
          bindProp: m,
          event: 'change',
          func: updateFunc('iconLeft'),
        },
        {
          title: '垂直偏移',
          type: 'number',
          prop: 'iconTop',
          bindProp: m,
          event: 'change',
          func: updateFunc('iconTop'),
        },
      ],
    },
    {
      title: '禁止',
      multiShow: false,
      children: [
        {
          title: '禁止输入',
          type: 'switch',
          prop: 'disableInput',
          bindProp: m,
          event: 'change',
          func: updateFunc('disableInput'),
        },
        {
          title: '禁止旋转',
          type: 'switch',
          prop: 'disableRotate',
          bindProp: m,
          event: 'change',
          func: updateFunc('disableRotate'),
        },
        {
          title: '禁止缩放',
          type: 'switch',
          prop: 'disableSize',
          bindProp: m,
          event: 'change',
          func: updateFunc('disableSize'),
        },
        {
          title: '禁止锚点',
          type: 'switch',
          prop: 'disableAnchor',
          bindProp: m,
          event: 'change',
          func: updateFunc('disableAnchor'),
        },
      ],
    },
    {
      title: '扩展',
      multiShow: false,
      children: [
        {
          title: '权限code',
          type: 'input',
          option: {
            placeholder: '权限code，多个逗号分隔',
          },
          prop: 'permissionCodes',
          bindProp: m,
          event: 'change',
          func: updateFunc('permissionCodes'),
        },
      ],
    },
  ];

  // 计算展示字段列表
  let showMap = computed(() => {
    const rawData = cloneDeep(map);
    let data = [];

    if (multiPen.value) {
      data = rawData.filter((i) => {
        i.multiShow ? (i.children = i.children.filter((item) => item.multiShow)) : '';
        return i.multiShow;
      });
    } else {
      data = cloneDeep(map);
    }

    return data;
  });
</script>

<style scoped></style>
