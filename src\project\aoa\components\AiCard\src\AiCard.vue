<template>
  <div
    :class="['ai-card', { thickness: type !== '' }]"
    @click="openAiModal"
    v-if="questionTemplate"
  >
    <div class="icon"></div>
    <div class="title">AI分析</div>
  </div>
  <AiModal
    @register="registerModal"
    :aiQuestion="questionTemplate"
    :aiQuestionTitle="questionTitle"
    :modalType="type"
    :deepThinking="deepThinking"
  />
</template>

<script lang="ts" setup>
  import { useModal } from '/@/components/Modal';
  import AiModal from './AiModal.vue';

  defineProps({
    questionTemplate: {
      type: String,
      default: '',
    },
    questionTitle: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    deepThinking: {
      type: Boolean,
      default: false,
    },
  });

  const [registerModal, { openModal }] = useModal();

  const openAiModal = () => {
    openModal(true, {
      record: {},
      isUpdate: false,
    });
  };
</script>

<style lang="less" scoped>
  .ai-card {
    .px2vw(8);
    padding: 0 @vw;
    .width-prop(28, height);
    .width-prop(12, margin-left);
    background: rgba(12, 80, 68, 0.88);
    border-radius: 4px;
    border: 1px solid rgba(31, 195, 164, 0.72);
    display: flex;
    align-items: center;
    cursor: pointer;

    .icon {
      .width-prop(18, height);
      .width-prop(18);
      background-image: url('/@aoa/views/cockpit/assets/images/ai.png');
      background-size: cover;
    }

    .title {
      .width-prop(4, padding-left);
      font-family: PingFang SC;
      font-weight: 400;
      .font-size(15);
      .width-prop(28, line-height);
    }

    &.thickness {
      // background: #258775;
      // box-shadow: inset -1px -1px 2px 0px #041a16, inset 1px 1px 2px 0px #bbfff2;
      // border: none;

      background: linear-gradient(131deg, #1684f9 1%, #183fbe 100%);
      box-shadow: inset -1px -1px 2px 0px #041a16, inset 1px 1px 2px 0px #afcfff;
      border: none;
    }
  }
</style>
