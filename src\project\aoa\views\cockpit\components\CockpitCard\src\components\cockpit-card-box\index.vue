<template>
  <div class="box-container">
    <div class="container-header" :style="`background-image: url(${icon})`">
      <div class="container-header-left">
        <div class="title-text">
          {{ title }}
          <slot name="title-slot"></slot>
        </div>
      </div>
      <slot name="header-right"></slot>
    </div>
    <div class="container-content">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import icon from '/@aoa/views/BI/assets/images/card_title_icon.png';

  defineProps({
    title: String,
  });
</script>

<style lang="less" scoped>
  .box-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 4px;
    border: 1px solid rgba(158, 197, 191, 1);
    background: linear-gradient(to bottom, rgba(1, 88, 72, 0.29), rgba(2, 88, 73, 0.72));

    .container-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      background-repeat: no-repeat;
      background-position: left bottom;
      background-size: 100% 100%;

      .container-header-left {
        .title-text {
          font-family: <PERSON><PERSON><PERSON>Ti;
          color: #fff;
          padding-left: 42px;
          font-weight: 500;
          font-size: 16px;
          line-height: 1;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          display: flex;
          align-items: center;
        }
      }
    }

    .container-content {
      height: calc(100% - 40px);
      overflow-y: overlay;
      overflow-x: hidden;
    }
  }

  @media screen and (min-width: 2000px) {
    .box-container {
      .container-header {
        .height-prop(48, height);

        .container-header-left {
          .title-text {
            .width-prop(44, padding-left);
            .font-size(18);
          }
        }
      }

      .container-content {
        .px2vh(48);
        height: calc(100% - @vh);
      }
    }
  }
</style>
