<template>
  <div class="cockpit-page">
    <div class="page-decorate">
      <div class="page-decorate-left"> </div>
      <div class="page-decorate-right"> </div>
      <div class="page-decorate-bottom"> </div>
    </div>
    <div class="page-head">
      <div class="head-left">
        <div class="back-box" @click="back"><img :src="back_icon" /></div>
        <ADateTime :theme="theme" />
      </div>
      <div class="title">精确曝气综合看板</div>
      <div class="info" v-if="showWeather">
        <AWeater :theme="theme" />
      </div>
    </div>
    <div class="page-content">
      <div class="water-in-out" style="display: none"><WaterInOutCard /></div>
      <div class="content-inner">
        <div class="content-row content-row-left">
          <div
            class="row-item"
            style="height: 34.25%"
            :data-resource-code="indexCodeList[7].groupCode"
          >
            <!-- 经济效益 -->
            <BenefitCard
              :data="benefitData.data"
              :all="benefitData.all"
              :first="benefitData.first"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(1)"
            />
          </div>
          <div
            class="row-item"
            style="height: 31.91%"
            :data-resource-code="indexCodeList[1].groupCode"
          >
            <!-- 吨水单耗 -->
            <WaterCard
              :data="waterData.data"
              :waterController="waterController"
              :first="waterData.first"
              :groupCode="indexCodeList[1].groupCode"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(2)"
            />
          </div>
          <div
            class="row-item"
            style="height: 30.21%"
            :data-resource-code="indexCodeList[8].groupCode"
          >
            <!-- 电费 -->
            <ElectricityTrendCard
              :data="electricityData.data"
              :first="electricityData.first"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(3)"
            />
          </div>
        </div>
        <div class="content-row content-row-middle">
          <div class="in-water" :data-resource-code="indexCodeList[5].groupCode">
            <!-- 进水流量 -->
            <BottomCard :data="windFlowData" :groupCode="indexCodeList[5].groupCode" />
          </div>
        </div>
        <div class="content-row content-row-right">
          <div class="row-item" data-resource-code="ZHGLJSC_TZHJD" style="height: 34.65%">
            <!-- 碳中和目标 -->
            <CarbonCard
              :processData="carbonProcessData"
              :data="carbonEmissionsData.data"
              :first="carbonEmissionsData.first"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(4)"
            />
          </div>
          <div
            class="row-item"
            :data-resource-code="indexCodeList[6].groupCode"
            style="height: 30.94%"
          >
            <!-- 碳强度分析 -->
            <CarbonStrengthCard
              :data="carbonStrengthData.data"
              :first="carbonStrengthData.first"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(5)"
            />
          </div>
          <div class="row-item" :data-resource-code="indexCodeList[10]" style="height: 31.18%">
            <!-- 碳收益 -->
            <CarbonProfitCard
              :data="carbonProfitData.data"
              :first="carbonProfitData.first"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(6)"
            />
          </div>
        </div>
      </div>
    </div>
    <template v-for="(item, index) in tipsItemSmallData" :key="index">
      <tipsItemSmall
        v-if="index > 0"
        :data="item.data"
        :style="`position: absolute; left: ${item.position?.[0]}; top: ${item.position?.[1]};z-index: 1;`"
        :theme="theme"
        :classSet="true"
      />
    </template>
    <template v-for="(img, index) in tipsImgFirst" :key="index">
      <div :style="formatStyle(img)"> {{ img.name }}：{{ img.value }}{{ img.unit }} </div>
    </template>
    <template v-for="(img, index) in tipsImgLast" :key="index">
      <div :style="formatStyle(img)"> {{ img.name }}：{{ img.value }}{{ img.unit }}</div>
    </template>
    <bgVideo @imgSize="imgSize" :classSet="true" />
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { CSSProperties, ref, reactive, onUnmounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { indexCodeList, centerParams } from './data';
  import {
    mockElectricityData,
    mockCarbonStrengthData,
    mockCarbonProfitData,
    mockWaterFlowData,
    mockWaterData,
    mockCarbonEmissionsData,
    mockBenefitData,
  } from './data';
  import { getSenceGroupTree } from '/@zhcz/api/scenes-group';
  import type { AerationData, LineChartData, DataList } from './type';
  import { ADateTime } from '/@aoa/components/ADateTime';
  import { AWeater } from '/@aoa/components/AWeater';
  // import tipsItem from './components/tipsItem.vue';
  import tipsItemSmall from './components/tipsItemSmall.vue';
  import hight_d_bg from './assets/images/hight_d_bg.png';
  import back_icon from './assets/images/back_icon.png';
  import { useIntervalFn } from '@vueuse/core';
  import { callResourceFunction } from '/@/api/config-center/scenes-group';
  import { getParams } from '/@aoa/utils';
  import { getParamKeyApi } from '/@/api/admin/param';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import { ParamsKeyEnum } from '/@aoa/enums';

  import BenefitCard from './components/BenefitCard.vue';
  import ElectricityTrendCard from './components/ElectricityTrendCard.vue';
  import WaterCard from './components/WaterCard.vue';
  import CarbonCard from './components/CarbonCard.vue';
  import CarbonStrengthCard from './components/CarbonStrengthCard.vue';
  import CarbonProfitCard from './components/CarbonProfitCard.vue';
  import BottomCard from './components/BottomCard.vue';
  import bgVideo from './components/bgVideo.vue';
  import WaterInOutCard from './components/WaterInOutCard.vue';

  const theme = ref<'light' | 'dark'>('dark');
  const router = useRouter();
  const indexList = ref<any[]>([]);
  const innerWidth = window.innerWidth;
  const baseWidth = 1920;
  const baseHeight = 1080;
  const imgRect = ref({ left: 0, top: 0, width: baseWidth, height: baseHeight });

  const tipsItemSmallData = ref<any>([
    {
      data: {
        title: '生化池',
        data: [
          // {
          //   name: '日均耗电大概25,144kWh',
          //   // value: '0.8',
          //   // unit: 'mg/L',
          // },
          // {
          //   name: '溶解氧',
          //   value: '0.5',
          //   unit: 'mg/L',
          // },
        ],
        config: {
          theme: '',
        },
      },
      position:
        innerWidth < 1500
          ? ['49.54%', '43.2%']
          : innerWidth < 1926 && innerWidth > 1500
          ? ['43.54%', '36.2%']
          : innerWidth > 1926 && innerWidth < 2566
          ? ['49.54%', '45.2%']
          : ['49.54%', '43.2%'],
    },
    {
      data: {
        title: '鼓风机房',
        data: [
          {
            name: '日均耗电大概',
            value: '25,144',
            unit: 'kWh',
          },
          // {
          //   name: '溶解氧',
          //   value: '0.5',
          //   unit: 'mg/L',
          // },
        ],
        config: {
          theme: '',
        },
      },
      position:
        innerWidth < 1500
          ? ['63.756%', '10.648%']
          : innerWidth < 1926
          ? ['64.756%', '18.648%']
          : innerWidth < 2566
          ? ['60.756%', '15.648%']
          : ['60.756%', '17.648%'],
    },
  ]);
  const tipsImgFirst = ref<any>([
    {
      src: hight_d_bg,
      position: ['22.083', '42.222'], // 全屏 1080
      // position:
      //   innerWidth < 1500
      //     ? ['15.083', '45.722']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['25.083', '40.722']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['25.083', '41.722']
      //     : ['25.083', '43.722'],
      width: '153.85',
      height: '36.57',
      name: 'COD',
      value: '-',
      unit: 'mg/L',
      rotate: 330.8,
    },
    {
      src: hight_d_bg,
      position: ['30.1666', '34.577'],
      // position:
      //   innerWidth < 1500
      //     ? ['25.1666', '36.777']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['32.1666', '32.777']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['31.1666', '35.777']
      //     : ['32.1666', '35.777'],
      width: '153.85',
      height: '36.57',
      name: 'TN',
      value: '-',
      unit: 'mg/L',
      rotate: 330.8,
    },
    {
      src: hight_d_bg,
      position: ['39.858', '24.729'],
      // position:
      //   innerWidth < 1500
      //     ? ['36.458', '27.729']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['39.458', '24.729']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['39.458', '27.729']
      //     : ['39.458', '28.729'],
      width: '153.85',
      height: '36.57',
      name: '氨氮',
      value: '-',
      unit: 'mg/L',
      rotate: 330.8,
    },
    {
      src: hight_d_bg,
      position: ['47.629', '17.492'],
      // position:
      //   innerWidth < 1500
      //     ? ['46.229', '19.492']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['46.229', '17.492']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['46.229', '20.492']
      //     : ['46.229', '22.492'],
      width: '153.85',
      height: '36.57',
      name: 'TP',
      value: '-',
      unit: 'mg/L',
      rotate: 330.8,
    },
  ]);
  const tipsImgLast = ref<any>([
    {
      src: hight_d_bg,
      position: ['56.837', '72.169'],
      // position:
      //   innerWidth < 1500
      //     ? ['53.837', '76.169']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['56.837', '72.169']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['58.837', '74.169']
      //     : ['55.837', '76.169'],
      width: '153.85',
      height: '36.57',
      name: 'COD',
      value: '-',
      unit: 'mg/L',
      rotate: 322.29,
    },
    {
      src: hight_d_bg,
      position: ['64.234', '62.163'],
      // position:
      //   innerWidth < 1500
      //     ? ['62.234', '66.163']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['63.234', '62.163']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['65.234', '65.163']
      //     : ['62.234', '67.163'],
      width: '153.85',
      height: '36.57',
      name: '氨氮',
      value: '-',
      unit: 'mg/L',
      rotate: 322.29,
    },
    {
      src: hight_d_bg,
      position: ['71.074', '52.692'],
      // position:
      //   innerWidth < 1500
      //     ? ['70.974', '56.692']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['68.974', '52.692']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['72.974', '54.692']
      //     : ['68.974', '57.692'],
      width: '153.85',
      height: '36.57',
      name: 'PH',
      value: '-',
      unit: '',
      rotate: 324.29,
    },
  ]);
  const imgSize = (img: any) => {
    imgRect.value = { ...img };
  };
  const formatStyle = (img: any): CSSProperties => {
    const scale = imgRect.value.width / baseWidth;
    const left = imgRect.value.left + Number(img?.position[0]) * 0.01 * 1920 * scale + 'px';
    const top = imgRect.value.top + Number(img?.position[1]) * 0.01 * 1080 * scale + 'px';
    const width = Number(img.width) * scale + 'px';
    const height = Number(img.height) * scale + 'px';
    return {
      background: 'url(' + img.src + ') no-repeat',
      backgroundSize: '100% 100%',
      position: 'absolute',
      left,
      top,
      width,
      height,
      lineHeight: height,
      transform: `rotate(${img.rotate}deg)`,
      fontFamily: 'PingFang SC',
      fontWeight: 600,
      fontSize: 14 * scale + 'px',
      color: '#FFFFFF',
      textAlign: 'center',
      textShadow: '0px 1px 2px rgba(0, 0, 0, 0.88)',
      zIndex: 1,
    };
  };

  // 是否开启天气组件
  const showWeather = ref(false);

  const getShowWeather = async () => {
    const data = await getParamKeyApi(ParamsKeyEnum.HasWeater);
    showWeather.value = data === '1';
  };

  getShowWeather();

  // 是否开启深度思考
  const deepThinking = ref(false);
  const getDeepThinking = async () => {
    const data = await getParamKeyApi(ParamsKeyEnum.DeepThinking);
    deepThinking.value = data === '1';
  };

  getDeepThinking();

  const AiQuestionTemplate = ref<{ label: string; value: string; index: number }[]>([]);

  // 获取AI提问模板
  const getAIQuestionWords = async () => {
    const res = await getDictTypeListApi({
      type: ParamsKeyEnum.AI_QUESTION_WORDS,
    });
    if (res && res.length) {
      AiQuestionTemplate.value = res.map((item) => ({
        label: item.label,
        value: item.value,
        index: item.sortOrder,
      }));
    }
  };
  getAIQuestionWords();

  // 获取对应AI提问模板
  const getCurrentAiQuestionTemplate = (index) => {
    return AiQuestionTemplate.value.find((item) => item.index === index)?.value;
  };

  /** 经济效益 */
  const benefitData = ref({
    all: null,
    data: mockBenefitData,
    first: false,
  });

  const getBenefitData = async () => {
    const params = getParams(
      indexCodeList[7],
      '1970-01-01 00:00:00',
      dayjs().format('YYYY-MM-DD 23:59:59'),
    );
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item.value !== null && item.value !== '' ? Number(item.value) : null,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
          maxVal: item.maxVal,
          minVal: item.minVal,
        };
      });
      benefitData.value.data = data;
      if (data.every((i) => i.value === null || i.value === '')) {
        benefitData.value.all = null;
      } else {
        const all = data.reduce((acc, cur) => acc + Number(cur.value), 0);
        benefitData.value.all = all;
      }
    } else {
      benefitData.value.data = mockBenefitData;
      benefitData.value.all = null;
    }
    benefitData.value.first = true;
  };

  const electricityData = reactive({
    data: mockElectricityData,
    first: false,
  });

  const getElectricityData = async () => {
    // 当前电费
    const startDateTime = dayjs().startOf('year').format('YYYY-MM-DD 00:00:00');
    const endDateTime = dayjs().endOf('year').format('YYYY-MM-DD 23:59:59');
    const params = getParams(indexCodeList[8], startDateTime, endDateTime);
    // 传统电费
    // const _startDateTime = dayjs().startOf('year').format('2024-MM-DD 00:00:00');
    // const _endDateTime = dayjs().endOf('year').format('2024-MM-DD 23:59:59');
    // const _params = getParams(indexCodeList[9], _startDateTime, _endDateTime);

    // const [res, _res] = await Promise.all([
    //   callResourceFunction(params),
    //   callResourceFunction(_params),
    // ]);

    const [res] = await Promise.all([callResourceFunction(params)]);
    if (res && res.length) {
      electricityData.data = res.map((item) => {
        return {
          name: item.indexName,
          // name1: _res.length ? _res[index].indexName : '',
          value: item.value,
          // value1: _res.length ? _res[index].value : null,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
          collectDateTime: item.collectDateTime,
        };
      });
    } else {
      electricityData.data = mockElectricityData;
    }

    electricityData.first = true;
  };

  /** 水处理能效 */
  const waterData = reactive<LineChartData>({
    data: mockWaterData,
    first: false,
  });

  const waterController = ref<number | null>(null);

  // 获取色带
  const getWaterController = async () => {
    const res = await getDictTypeListApi({
      type: ParamsKeyEnum.WATER_CONTROLLER,
    });
    if (res && res.length) {
      waterController.value = Number(res[0]?.value);
    }
  };

  const getWaterData = async () => {
    const startDateTime = dayjs().startOf('year').format('YYYY-MM-DD 00:00:00');
    const endDateTime = dayjs().endOf('year').format('YYYY-MM-DD 23:59:59');
    const params = getParams(indexCodeList[1], startDateTime, endDateTime);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      waterData.data = res.map((item) => {
        return {
          name: item.indexName,
          data: item.data,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          maxVal: item.maxVal,
          minVal: item.minVal,
          indexCode: item.indexCode,
        };
      });
    } else {
      waterData.data = mockWaterData;
    }
    waterData.first = true;
  };

  /** 碳中和目标-进度 */
  const carbonProcessData = ref<AerationData>({
    value: null,
    maxVal: null,
    minVal: null,
    name: '',
    unit: '',
    indexCode: '',
    digit: 0,
  });

  const getCarbonProcessData = async () => {
    const startDate = dayjs().startOf('year').format('YYYY-MM-DD 00:00:00');
    const endDate = dayjs().endOf('year').format('YYYY-MM-DD 23:59:59');
    const params = getParams(indexCodeList[2], startDate, endDate);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      carbonProcessData.value.value = res[0]?.value;
      carbonProcessData.value.maxVal = res[0]?.maxVal;
      carbonProcessData.value.name = res[0]?.indexName;
      carbonProcessData.value.unit = res[0]?.unitName;
      carbonProcessData.value.indexCode = res[0]?.indexCode;
      carbonProcessData.value.digit = res[0]?.digit;
    } else {
      carbonProcessData.value.value = null;
      carbonProcessData.value.maxVal = null;
      carbonProcessData.value.name = '';
      carbonProcessData.value.unit = '';
      carbonProcessData.value.indexCode = '';
      carbonProcessData.value.digit = 0;
    }
  };

  /** 碳中和目标-碳排放量 */
  const carbonEmissionsData = reactive<LineChartData>({
    data: [],
    first: false,
  });

  const getCarbonEmissionsData = async () => {
    const startDateTime = dayjs().startOf('year').format('YYYY-MM-DD 00:00:00');
    const endDateTime = dayjs().endOf('year').format('YYYY-MM-DD 23:59:59');
    const params = getParams(indexCodeList[3], startDateTime, endDateTime);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      carbonEmissionsData.data = res.map((item) => {
        return {
          name: item.indexName,
          data: item.data,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          maxVal: item.maxVal,
          minVal: item.minVal,
          indexCode: item.indexCode,
        };
      });
    } else {
      carbonEmissionsData.data = mockCarbonEmissionsData;
    }
    carbonEmissionsData.first = true;
  };

  /** 碳强度分析-统计 */

  const carbonStrengthData = reactive<LineChartData>({
    data: mockCarbonStrengthData(),
    first: false,
  });

  const getCarbonStrengthData = async () => {
    const params = getParams(
      indexCodeList[6],
      dayjs().startOf('year').format('YYYY-MM-DD 00:00:00'),
      dayjs().endOf('year').format('YYYY-MM-DD 23:59:59'),
    );
    const res = await callResourceFunction(params);
    if (res && res.length) {
      carbonStrengthData.data = res.map((item) => {
        return {
          name: item.indexName,
          data: item.data,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          maxVal: item.maxVal,
          minVal: item.minVal,
          indexCode: item.indexCode,
        };
      });
    } else {
      carbonStrengthData.data = mockCarbonStrengthData();
    }
    carbonStrengthData.first = true;
  };

  // 碳收益
  const carbonProfitData = reactive<LineChartData>({
    data: mockCarbonProfitData,
    first: false,
  });

  const getCarbonProfitData = async () => {
    const startDateTime = dayjs().startOf('year').format('YYYY-MM-DD 00:00:00');
    const endDateTime = dayjs().endOf('year').format('YYYY-MM-DD 23:59:59');
    const params = getParams(indexCodeList[10], startDateTime, endDateTime);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      carbonProfitData.data = res.map((item) => {
        return {
          name: item.indexName,
          data: item.data,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          maxVal: item.maxVal,
          minVal: item.minVal,
          indexCode: item.indexCode,
        };
      });
    } else {
      carbonProfitData.data = mockCarbonProfitData;
    }
    carbonProfitData.first = true;
  };

  /** 中心流量 */
  const windFlowData = ref<DataList[]>(mockWaterFlowData);
  const getWaterFlowData = async () => {
    const params = getParams(
      indexCodeList[5],
      '1970-01-01 00:00:00',
      dayjs().format('YYYY-MM-DD 23:59:59'),
    );
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item.value,
          digit: item.digit ?? 0,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      windFlowData.value = data;
    } else {
      windFlowData.value = mockWaterFlowData;
    }
  };

  const getResourceData = async () => {
    const data = await getSenceGroupTree();
    // 工艺监控
    indexList.value =
      data
        .find((item) => item.senceCode === 'JQBQ_V3')
        ?.children?.find((item) => item.groupCode === 'ZHGLJSC')?.children || [];
  };

  getResourceData();

  const getData = () => {
    getBenefitData();
    getElectricityData();
    getWaterController();
    getWaterData();
    getCarbonProcessData();
    getCarbonEmissionsData();
    getCarbonStrengthData();
    getCarbonProfitData();
    getWaterFlowData();
  };

  // 进水水质
  const getJSSZData = async () => {
    const params = getParams(centerParams[1]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const formatRes = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
        // name: 'COD',
        // value: '-',
        // unit: 'mg/L',
      });
      tipsImgLast.value = tipsImgLast.value.map((item, index) => {
        return { ...item, ...formatRes[index] };
      });
    } else {
      // tipsImgFirst.value = [
      //   {
      //     src: hight_d_bg,
      //     position: ['22.083%', '34.722%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'COD',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 330.8,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['29.1666%', '27.777%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'TN',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 330.8,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['36.458%', '19.629%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: '氨氮',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 330.8,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['43.229%', '12.592%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'TP',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 330.8,
      //   },
      // ];
    }
  };
  // 出水水质
  const getCSSZData = async () => {
    const params = getParams(centerParams[2]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const formatRes = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      tipsImgFirst.value = tipsImgFirst.value.map((item, index) => {
        return { ...item, ...formatRes[index] };
      });
    } else {
      // tipsImgLast.value = [
      //   {
      //     src: hight_d_bg,
      //     position: ['58.837%', '81.169%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'COD',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 324.29,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['65.234%', '72.163%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: '氨氮',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 324.29,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['71.974%', '62.692%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'PH',
      //     value: '-',
      //     unit: '',
      //     rotate: 324.29,
      //   },
      // ];
    }
  };
  // 鼓风机房
  const getGFJFData = async () => {
    const params = getParams(centerParams[3]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const formatRes = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      tipsItemSmallData.value[1].data.data = formatRes;
    } else {
      tipsItemSmallData.value[1].data.data = [
        {
          name: '日均耗电大概',
          value: '25144',
          unit: 'kWh',
        },
        // {
        //   name: '溶解氧',
        //   value: '0.5',
        //   unit: 'mg/L',
        // },
      ];
    }
  };
  // 返回
  const back = () => {
    const path = router.currentRoute.value.meta.backRoutePath;
    if (path) {
      router.push(path);
    } else {
      router.go(-1);
    }
  };

  const getAllData = () => {
    getCSSZData();
    getJSSZData();
    getGFJFData();
  };
  const getPollData = () => {
    getBenefitData();
    getElectricityData();
    getCarbonProcessData();
    getCarbonEmissionsData();
    getCarbonStrengthData();
    getCarbonProfitData();
    getWaterFlowData();
    getAllData();
  };

  getData();
  getAllData();
  const { pause } = useIntervalFn(getPollData, 10 * 1000);

  onUnmounted(() => {
    pause();
  });
</script>
<style lang="less" scoped>
  @import '/@aoa/assets/css/font.less';

  .cockpit-page {
    position: relative;
    width: 100%;
    height: 100%;
    // background: url('../BI/assets/images/bi_bim.png') center center no-repeat;
    background-size: cover;
    overflow: hidden;
    color: #fff;

    .page-decorate {
      .page-decorate-left,
      .page-decorate-right {
        position: absolute;
        width: 20px;
        top: 20px;
        height: calc(100% - 52px);
        background-size: 100% 100%;
        z-index: 99;
      }

      .page-decorate-left {
        left: 0;
        background-image: url('/@aoa/views/BI/assets/images/bi_left_bg.png');
      }

      .page-decorate-right {
        right: 0;
        background-image: url('/@aoa/views/BI/assets/images/bi_right_bg.png');
      }

      .page-decorate-bottom {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        height: 18px;
        background-size: 100% 100%;
        background-image: url('/@aoa/views/BI/assets/images/bi_footer.png');
        z-index: 99;
      }
    }

    .page-head {
      position: relative;
      padding: 14px 28px 0;
      width: 100%;
      height: 72px;
      background: url('/@aoa/views/BI/assets/images/title.png') center top no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 99;

      .head-left {
        .px2vw(24);
        display: flex;
        align-items: center;
        gap: 0 @vw;

        .back-box {
          .width-prop(40);
          .width-prop(40, height);
          border-radius: 50%;
          cursor: pointer;

          img {
            width: 100%;
          }
        }
      }

      .title {
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);
        font-family: 'Alimama ShuHeiTi';
        letter-spacing: 4px;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        line-height: 60px;
      }

      .info {
        display: flex;
        width: max-content;
      }
    }

    .page-content {
      position: relative;
      padding: 10px 28px 6px;
      width: 100%;
      height: calc(100% - 72px - 18px);

      .water-in-out {
        position: absolute;
        left: calc(28px + 292px + 24px);
        top: 10px;
        z-index: 100;
      }

      .content-inner {
        display: flex;
        justify-content: space-between;
        gap: 12px;
        height: 100%;

        .content-row {
          height: 100%;
          display: flex;
          justify-content: space-between;
          gap: 12px;

          .row-item {
            position: relative;
          }

          &-left,
          &-right {
            width: 292px;
            display: flex;
            z-index: 2;
            flex-direction: column;
          }

          &-middle {
            position: relative;
            flex: 1;
            z-index: 2;
            padding: 0 12px;
          }
        }
      }
    }
  }

  @media screen and (min-width: 1800px) {
    .cockpit-page {
      .page-content {
        .water-in-out {
          .px2vw(372);
          left: calc(28px + @vw + 24px);
        }

        .content-inner {
          .content-row {
            &-left,
            &-right {
              .width-prop(372);
            }
          }
        }
      }
    }
  }

  @media screen and (min-width: 2000px) {
    .cockpit-page {
      .page-decorate {
        .page-decorate-left,
        .page-decorate-right {
          .width-prop(24);
          .px2vh(64);
          height: calc(100% - @vh);
          .height-prop(24, top);
        }

        .page-decorate-bottom {
          .height-prop(24, height);
        }
      }

      .page-head {
        .px2vw(28);
        .px2vh(14);
        padding: @vh @vw 0;
        .height-prop(94, height);

        .title {
          .font-size(28);
          .height-prop(86, line-height);
        }
      }

      .page-content {
        .px2vw(28);
        .px2vh(12);
        padding: @vh @vw;
        height: calc(100% - (94 / @design-height) * 100vh - (24 / @design-height) * 100vh);

        .water-in-out {
          .px2vw(424);
          left: @vw;
          .width-prop(10, top);
        }
      }
    }
  }
</style>
