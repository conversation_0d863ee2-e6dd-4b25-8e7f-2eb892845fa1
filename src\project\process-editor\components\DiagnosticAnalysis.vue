<template>
  <div
    :class="['diagnostic-analysis-wrapper', { 'ani-analysis': open }]"
    ref="target"
    v-if="open"
    :style="getBindStyle"
    v-loading="loading"
    :placement="placement"
    @mouseleave="handleMouseLeave"
  >
    <div class="list-wrapper">
      <div class="title">诊断分析报告</div>
      <div class="content-wrapper">
        <template v-if="status === 2">
          <div class="content analysis">
            <div class="label">诊断内容：</div>
            <div class="value">
              {{ content.inspectContent }}
            </div>
          </div>
        </template>
        <template v-if="status === 1">
          <div class="content reason">
            <div class="label">异常原因：</div>
            <div class="value">
              {{ content.exceptionReason }}
            </div>
          </div>
          <div class="content suggestion">
            <div class="label">诊断建议：</div>
            <div class="value">
              {{ content.inspectAdvice }}
            </div>
          </div>
        </template>
      </div>
      <div class="footer"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue';
  import { onClickOutside, useMouseInElement } from '@vueuse/core';
  import { getDiagnosisRecordByIndicatorCode } from '../api/index';
  // 枚举变量引用需优化
  import { expressionTypeEnum } from '../views/editor/components/PenProps/kit/kit.data';

  const props = defineProps({
    open: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      default: () => ({}),
      required: true,
    },
    position: {
      type: Object,
      default: () => ({
        left: 0,
        top: 0,
      }),
      required: true,
    },
  });

  defineEmits(['update:open']);

  const open = ref(false);

  // const open = computed({
  //   get() {
  //     return props.open;
  //   },
  //   set(value) {
  //     emit('update:open', value);
  //   },
  // });

  const target = ref<HTMLElement | null>(null);
  onClickOutside(target, () => {
    open.value = false;
  });
  const { isOutside } = useMouseInElement(target);
  watch(isOutside, (val) => {
    if (val) {
      setTimeout(() => (open.value = false), 200);
    }
  });

  const NORMAL = 'normal';
  const status = computed(() => {
    return props.data.scriptResult === NORMAL ? 2 : 1;
  });

  // 气泡弹框宽度
  const targetWidth = computed(() => {
    return target.value?.clientWidth || 350;
  });

  // 动态高度状态
  const dynamicHeight = ref(250);

  // 气泡弹框高度度 - 动态计算实际高度
  const targetHeight = computed(() => {
    return dynamicHeight.value;
  });

  // 更新动态高度
  const updateHeight = async () => {
    await nextTick();
    if (target.value) {
      const actualHeight = target.value.clientHeight;
      if (actualHeight > 0) {
        dynamicHeight.value = Math.min(actualHeight, 300); // 最大高度限制为300px
      }
    }
  };

  function handleMouseLeave() {
    // open.value = false;
  }

  const _position_ = computed(() => {
    const { position } = props;
    const parentElement = target.value?.parentElement; //父级元素
    const targetWidthHalf = targetWidth.value / 2; //气泡弹框宽度的一半
    const targetHeightHalf = targetHeight.value / 2; //气泡弹框高度的一半
    const penWidthHalf = props.data.width / 2; //画笔宽度的一半
    const penHeightHalf = props.data.height / 2; //画笔高度的一半

    // 默认上
    let top = position.top - targetHeight.value - 8;
    let left = position.left - targetWidthHalf;

    if (top < 0) {
      // 下
      top = position.top + props.data.height + 16;
      left = position.left - targetWidthHalf;
    }

    if (left < 0) {
      // 右
      left = position.left + penWidthHalf + 8;
      top = position.top - targetHeightHalf + penHeightHalf;
    }

    const parentElementWidth = parentElement?.clientWidth || 0;

    if (position.left + targetWidth.value > parentElementWidth) {
      // 左
      left = position.left - penWidthHalf - targetWidth.value;
      top = position.top - targetHeightHalf + penHeightHalf;
    }

    return {
      top: `${top}px`,
      left: `${left}px`,
    };
  });

  const getBindStyle = computed(() => {
    return {
      ..._position_.value,
      // 不再设置固定高度，让内容自动撑开
    };
  });

  // 气泡弹框位置
  const placement = computed(() => {
    const top = Number(_position_.value.top.split('px')[0]);
    const left = Number(_position_.value.left.split('px')[0]);
    const { position } = props;
    const targetWidthHalf = targetWidth.value / 2; //气泡弹框宽度的一半
    const targetHeightHalf = targetHeight.value / 2; //气泡弹框高度的一半
    const penWidthHalf = props.data.width / 2; //画笔宽度的一半
    const penHeightHalf = props.data.height / 2; //画笔高度的一半

    if (top === position.top + props.data.height + 16 && left === position.left - targetWidthHalf) {
      return 'bottom';
    } else if (
      left == position.left + penWidthHalf + 8 &&
      top === position.top - targetHeightHalf + penHeightHalf
    ) {
      return 'right';
    } else if (
      left === position.left - penWidthHalf - targetWidth.value &&
      top === position.top - targetHeightHalf + penHeightHalf
    ) {
      return 'left';
    }

    return 'top';
  });

  const queryParams = computed(() => {
    const params = {
      // startDateTime: dayjs().startOf('days').format('YYYY-MM-DD HH:mm:ss'),
      // endDateTime: dayjs().endOf('days').format('YYYY-MM-DD HH:mm:ss'),
      indicatorCode: props.data.code || '',
      // type: status.value, // 1：报警；2：常规
      diagnosisType: props.data.scriptType === expressionTypeEnum.rule ? 1 : 2,
    };

    return params;
  });

  const loading = ref(false);
  const content = ref<Recordable>({
    exceptionReason: '',
    inspectAdvice: '',
    inspectContent: '',
  });
  // let index = 0;
  async function getData() {
    try {
      loading.value = true;
      const data = await getDiagnosisRecordByIndicatorCode(queryParams.value);
      content.value.exceptionReason = data?.exceptionReason || '';
      content.value.inspectAdvice = data?.inspectAdvice || '';
      content.value.inspectContent = data?.inspectContent || '';
      if (
        (status.value === 2 && content.value.inspectContent) ||
        (status.value === 1 && content.value.inspectAdvice && content.value.exceptionReason)
      ) {
        open.value = true;
        // 内容更新后重新计算高度
        await updateHeight();
      } else {
        open.value = false;
      }
    } catch (error) {
      open.value = false;
      console.log('error', error);
    } finally {
      loading.value = false;
    }
  }

  watch([() => props.open, () => props.position], (val) => {
    if (val[0]) {
      getData();
    } else {
      open.value = false;
    }
  });
</script>

<style lang="less" scoped>
  .diagnostic-analysis-wrapper {
    --background-color: #025849ff;
    --border-color: #06866b66;
    position: absolute;
    z-index: 100;
    width: 350px;
    border-radius: 4px;
    opacity: 0;

    :deep(.full-loading) {
      background: transparent;
    }

    &.ani-analysis {
      animation: fadeIn 0.3s forwards;
    }

    &[placement='top'] {
      &::before {
        // 倒三角边框
        content: '';
        position: absolute;
        bottom: -13px;
        left: 50%;
        transform: translate(-50%, -1px);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 13px solid var(--border-color);
      }

      &::after {
        // 倒三角填充
        content: '';
        position: absolute;
        bottom: -12px;
        left: 50%;
        transform: translate(-50%, -1px);
        width: 0;
        height: 0;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-top: 12px solid var(--background-color);
      }
    }

    &[placement='bottom'] {
      &::before {
        // 倒三角边框
        content: '';
        position: absolute;
        top: -13px;
        left: 50%;
        transform: translate(-50%, 1px);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 13px solid var(--border-color);
      }

      &::after {
        // 倒三角填充
        content: '';
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translate(-50%, 1px);
        width: 0;
        height: 0;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-bottom: 12px solid var(--background-color);
      }
    }

    &[placement='left'] {
      &::before {
        // 倒三角边框
        content: '';
        position: absolute;
        right: -13px;
        top: 50%;
        transform: translate(-1px, -50%);
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
        border-left: 13px solid var(--border-color);
      }

      &::after {
        // 倒三角填充
        content: '';
        position: absolute;
        right: -12px;
        top: 50%;
        transform: translate(-1px, -50%);
        width: 0;
        height: 0;
        border-top: 7px solid transparent;
        border-bottom: 7px solid transparent;
        border-left: 12px solid var(--background-color);
      }
    }

    &[placement='right'] {
      &::before {
        // 倒三角边框
        content: '';
        position: absolute;
        left: -13px;
        top: 50%;
        transform: translate(1px, -50%);
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
        border-right: 13px solid var(--border-color);
      }

      &::after {
        // 倒三角填充
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        transform: translate(1px, -50%);
        width: 0;
        height: 0;
        border-top: 7px solid transparent;
        border-bottom: 7px solid transparent;
        border-right: 12px solid var(--background-color);
      }
    }

    .list-wrapper {
      width: 100%;
      height: 100%;
      background-color: var(--background-color);
      border: 1px solid var(--border-color);
      border-radius: 4px;

      .title {
        font-weight: 600;
        font-size: 16px;
        height: 48px;
        line-height: 1;
        padding-top: 16px;
        color: #ffffffe0;
        padding-left: 16px;
        background-size: 100% 100%;
      }

      .content-wrapper {
        overflow: auto;
        max-height: 250px;
        padding: 0px 16px 16px;
        background-size: 100% 100%;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none; /* 对于Chrome, Safari, Edge */
        }
      }

      .footer {
        height: 0px;
        background-size: 100% 100%;
      }

      .content {
        padding: 12px;
        width: 100%;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.12);
        font-size: 14px;
        color: #ffffffe0;
        background-color: #06866b66;
        box-shadow: inset 1px 2px 4px 0px #000e0b, inset 0px -1px 4px 0px #b2fff1;

        &:not(:last-child) {
          margin-bottom: 12px;
        }

        .label {
          margin-bottom: 4px;
          font-weight: 600;
        }

        .value {
          font-weight: 400;
          text-align: justify;
          white-space: pre-wrap;
        }
      }
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
</style>
