<template>
  <div :class="prefixCls">
    <swiper ref="swiperRef" slidesPerView="auto" @slide-change="handleSideChange">
      <swiper-slide v-for="(menu, index) in menusList" :key="index">
        <div
          :key="index"
          :class="['menu-item', { ['menu-item_actived']: menu.path === currentPath }]"
          @click="switchTab(menu, index)"
        >
          <div>{{ menu.name }}</div>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { useUserStore } from '/@/store/modules/user';
  import { useAoaStore } from '/@/store/modules/aoa';
  import { isNotEmpty } from '/@/utils/is';
  import { AOA_MENU_ACTIVE_INDEX } from '/@/enums/aoaEnum';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { useGo } from '/@/hooks/web/usePage';
  import { useRouter } from 'vue-router';
  import { flattenTree } from '/@/utils';

  const { prefixCls } = useDesign('aoa-ipc-menu');

  const router = useRouter();

  const currentPath = computed(() => router.currentRoute.value.path);

  const swiperRef = ref();
  const permissionStore = usePermissionStore();
  const userStore = useUserStore();
  const userInfo = computed(() => userStore.getUserInfo);
  const factoryInfoList = computed(() => userInfo.value.factoryInfoList || []);
  const factoryId = computed(() => userStore.getCurrentFactoryId);

  // const menusList = ref([]);

  // const getMenuList = () => {
  //   const firstMenuList = permissionStore.getFirstMenuList;
  //   const moduleId = factoryInfoList.value.find(
  //     (item) => item.factoryId === factoryId.value,
  //   )?.moduleId;

  //   console.log('getMenuList', firstMenuList, moduleId);
  //   if (!moduleId) {
  //     menusList.value = [];
  //     return;
  //   }

  //   const currentFirstMenuList = firstMenuList.filter((item) => item.moduleId === moduleId);
  //   const flatAllMenuList = flattenTree(currentFirstMenuList);
  //   if (currentFirstMenuList.length <= 0) {
  //     menusList.value = [];
  //     return;
  //   }
  //   menusList.value = flatAllMenuList.filter((item) => item.realPath) || [];

  //   router.push(menusList.value[0]?.path);
  // };

  const menusList = computed(() => {
    const firstMenuLists = permissionStore.getFirstMenuList;
    if (!firstMenuLists.length) {
      return [];
    }
    // 当前菜单
    const currentModuleId = factoryInfoList.value.find(
      (item) => item.factoryId === factoryId.value,
    )?.moduleId;
    const currentFirstMenuList = firstMenuLists.find((item) => item.moduleId === currentModuleId);

    const flatAllMenuList = flattenTree(currentFirstMenuList?.children);
    return !currentFirstMenuList ? [] : flatAllMenuList.filter((item) => item.realPath) || [];
  });

  const currentIndex = ref(0);
  function handleSideChange() {
    currentIndex.value = swiperRef.value.$el.swiper.activeIndex;
  }

  const token = computed(() => userStore.getToken);
  watch(
    () => token.value,
    (newVal) => {
      if (!newVal) {
        aoaStore.setAoaIpcMenuActiveIndex(0);
      }
    },
  );

  const aoaStore = useAoaStore();
  function getActiveIndex() {
    const lsActiveIndex = localStorage.getItem(AOA_MENU_ACTIVE_INDEX);
    return isNotEmpty(lsActiveIndex) ? Number(lsActiveIndex) : aoaStore.getAoaIpcMenuActiveIndex;
  }
  const activedIndex = ref(getActiveIndex());

  const go = useGo();
  async function switchTab(menu, index) {
    if (index === activedIndex.value) return;
    aoaStore.setAoaIpcMenuActiveIndex(index);
    activedIndex.value = getActiveIndex();
    go(menu.path);
  }

  // watch(
  //   () => factoryId.value,
  //   () => {
  //     getMenuList();
  //   },
  //   { deep: true },
  // );
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-aoa-ipc-menu';

  .@{prefix-cls} {
    display: flex;
    align-items: center;
    flex: 1;
    margin-left: 100px;

    ::v-deep(.swiper) {
      .swiper-wrapper {
        display: flex;
      }

      .swiper-slide {
        width: auto;
      }
    }

    .menu-item {
      min-width: 112px;
      padding: 0 24px;
      height: 40px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      text-align: center;
      line-height: 40px;
      cursor: pointer;

      &_actived {
        background: #18957d;
        box-shadow: inset -2px -2px 2px 0px #102c29, inset 2px 2px 2px 0px #bcebe2;
        border-radius: 4px;
        color: #fff;
      }
    }
  }
</style>
