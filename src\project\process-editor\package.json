{"name": "hlxb-process-editor", "version": "1.0.0", "description": "邮件系统", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:lint-staged": "lint-staged", "prepare": "husky install", "preinstall": "only-allow pnpm", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/"}, "repository": {"type": "git", "url": "https://codeup.aliyun.com/6422518b604a7332f58626d6/hlxb-frontend/hlxb-mail.git"}, "author": "", "license": "ISC", "devDependencies": {"@commitlint/cli": "^16.3.0", "@commitlint/config-conventional": "^16.2.4", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "cz-git": "^1.10.1", "eslint": "^8.51.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^8.7.1", "husky": "^7.0.4", "lint-staged": "^12.3.7", "only-allow": "^1.2.1", "postcss": "^8.4.31", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.8", "rimraf": "^6.0.1", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^7.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "typescript": "4.9.5", "vue-eslint-parser": "^9.4.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}