import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getPlatformInfoPage } from '/@zhcz/api/config-center/scenes-group';
import { getDictTypeListApi } from '/@/api/admin/dict';
import { DICT } from '/@zhcz/enums/sceneResource';

export const columns: BasicColumn[] = [
  {
    title: '',
    dataIndex: 'sort',
    key: 'sort',
    align: 'left',
    width: 92,
  },
  {
    title: '资源标识',
    dataIndex: 'sourceUniqueKey',
  },
  {
    title: '展示名称',
    dataIndex: 'displayName',
  },
  {
    title: '展示单位',
    dataIndex: 'unitName',
    format: (text) => text || '-',
    ifShow: true,
  },
  {
    title: '预览图',
    dataIndex: 'src',
    ifShow: false,
  },
  // {
  //   title: '标签',
  //   dataIndex: 'tag',
  //   format: (text) => text || '-',
  // },
  // {
  //   title: '开启上下限',
  //   dataIndex: 'openLimit',
  //   width: 100,
  // },
  // {
  //   title: '上限',
  //   dataIndex: 'maxVal',
  //   format: (text) => text || '-',
  //   width: 100,
  // },
  // {
  //   title: '下限',
  //   dataIndex: 'minVal',
  //   format: (text) => text || '-',
  //   width: 100,
  // },
  // {
  //   title: '小数点位数',
  //   dataIndex: 'digit',
  //   width: 100,
  // },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'searchText',
    label: '资源名称',
    component: 'Input',
    labelWidth: 68,
    colProps: { span: 7 },
  },
];

export const scenesSchemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'Id',
    show: false,
  },
  {
    field: 'platformId',
    component: 'ApiSelect',
    label: '平台',
    required: true,
    colProps: { span: 24 },
    componentProps: {
      api: getPlatformInfoPage,
      params: {
        current: 1,
        size: 999,
      },
      resultField: 'records',
      labelField: 'displayName',
      valueField: 'id',
    },
  },
  {
    field: 'senceName',
    component: 'Input',
    label: '场景名称',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'senceCode',
    component: 'Input',
    label: '场景编码',
    colProps: { span: 24 },
    defaultValue: '',
    required: true,
  },
  {
    field: 'sort',
    component: 'InputNumber',
    label: '场景排序',
    colProps: { span: 24 },
    componentProps: {
      min: 0,
    },
    defaultValue: 0,
  },
  {
    field: 'requestHandle',
    component: 'Input',
    label: '权限标识',
    colProps: { span: 24 },
    componentProps: {
      rows: 4,
    },
  },
];

export const groupSchemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'Id',
    show: false,
  },
  {
    field: 'parentName',
    component: 'Input',
    label: '父级场景',
    colProps: { span: 24 },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'groupName',
    component: 'Input',
    label: '分组名称',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'groupCode',
    component: 'Input',
    label: '分组编码',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'defaultInterfaceId',
    component: 'Input',
    label: '默认接口',
    colProps: { span: 24 },
    slot: 'interface-slot',
    required: true,
  },
  // {
  //   field: 'name',
  //   component: 'Input',
  //   label: '分组CODE',
  //   colProps: { span: 12 },
  //   required: true,
  // },
  {
    field: 'sort',
    component: 'InputNumber',
    label: '分组排序',
    colProps: { span: 24 },
    componentProps: {
      min: 0,
    },
    defaultValue: 0,
  },
  {
    field: 'requestHandle',
    component: 'Input',
    label: '权限标识',
    colProps: { span: 24 },
    componentProps: {
      rows: 4,
    },
  },
  // {
  //   field: 'sort1',
  //   component: 'InputTextArea',
  //   label: '场景备注',
  //   colProps: { span: 24 },
  //   componentProps: {
  //     rows: 4,
  //   },
  // },
  {
    field: 'groupPurpose',
    component: 'ApiSelect',
    label: '分组用途',
    colProps: { span: 24 },
    componentProps: {
      api: getDictTypeListApi,
      labelField: 'label',
      valueField: 'value',
      params: {
        type: DICT.DATA_SENCE_GROUP_PURPOSE,
      },
    },
  },
];

export const previewSchemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'Id',
    show: false,
  },
  {
    field: 'interface',
    component: 'Select',
    label: '接口选择',
    slot: 'interface-slot',
    colProps: { span: 24 },
  },
  {
    field: 'interfaceName',
    component: 'Input',
    label: '接口名称',
    required: true,
    colProps: { span: 24 },
  },
  {
    field: 'interfaceUrl',
    component: 'Input',
    label: '接口URL',
    componentProps: {
      placeholder: '请输入接口URL，如：httpxxxxx//api/bure',
    },
    required: true,
    colProps: { span: 24 },
  },
  {
    field: 'interfaceType',
    component: 'Select',
    label: '接口类型',
    required: true,
    componentProps: {
      options: [
        { label: 'KV', value: 'KV' },
        { label: 'KL', value: 'KL' },
      ],
    },
    colProps: { span: 12 },
    defaultValue: 'KV',
    show: false,
  },
  {
    field: 'requestMethod',
    component: 'Select',
    label: '接口类型',
    required: true,
    componentProps: {
      options: [
        { label: 'GET', value: 'GET' },
        { label: 'POST', value: 'POST' },
      ],
    },
    defaultValue: 'POST',
    show: false,
  },
  {
    field: 'isConvert',
    component: 'Switch',
    label: 'js转化',
    slot: 'isConvert-slot',
    defaultValue: false,
  },
  {
    field: 'defaultBody',
    component: 'Input',
    label: '接口参数',
    slot: 'body-slot',
    required: true,
    colProps: { span: 24 },
  },
  {
    field: 'defaultQuery',
    component: 'Input',
    label: 'query参数',
    defaultValue: '',
    show: false,
    colProps: { span: 24 },
  },
  {
    field: 'resultJsConvert',
    component: 'Input',
    label: '结果转换脚本',
    slot: 'js-convert-slot',
    required: true,
    colProps: { span: 24 },
  },
  {
    field: 'interfaceScript',
    component: 'Input',
    label: '接口脚本',
    defaultValue: '',
    show: false,
  },
  {
    field: 'defaultHeader',
    component: 'Input',
    label: '默认请求头',
    defaultValue: '',
    show: false,
  },
];

export const bindIndicatorColumns: BasicColumn[] = [
  {
    title: '资源标识',
    dataIndex: 'indicatorCode',
    // width: 200,
  },
  {
    title: '资源名称',
    dataIndex: 'indicatorName',
  },
  {
    title: '资源单位',
    dataIndex: 'unit',
    format: (text) => text || '-',
    width: 120,
  },
];
