<template>
  <div class="chart w-full h-full" ref="chartRef" v-loading="loading"></div>
</template>

<script setup lang="ts">
  // import dayjs from 'dayjs/esm';
  import { uniqBy } from 'lodash-es';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { Params, useChartsData } from './useChartsData';
  import { ref, Ref, PropType, nextTick, watch } from 'vue';
  import { getMaxValue, getMinValue, getChartsGrid, colorList, mockTime } from '/@aoa/utils';
  // import { TimeTypeMap } from '/@aoa/components/SearchForm';
  import { getValue } from '/@aoa/utils/string';

  const props = defineProps({
    api: {
      type: Function,
      required: true,
    },
    params: {
      type: Object as PropType<Params>,
      required: true,
    },
    color: {
      type: String,
      default: () => '#0860CC',
    },
  });

  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const { chartsData, loading } = useChartsData(props, false);

  async function renderEcharts() {
    await nextTick();

    // const { format, format2 } = TimeTypeMap[props.params.timeType];
    const { nowData } = chartsData;
    const uniqUnit = uniqBy(nowData.datas, 'unit');
    const allMax = getMaxValue(nowData.datas);
    const { gridLeft, gridRight } = getChartsGrid(uniqUnit, allMax);
    const yAxisList = uniqUnit.map(({ unit }, key) => {
      const mergeDatas = nowData.datas
        .filter((i) => i.unit === unit)
        .map((i) => i.data)
        .flat();

      let offset = Math.floor(key / 2) * 72;
      const max = getMaxValue(mergeDatas);
      const min = getMinValue(mergeDatas);
      return {
        unit,
        offset,
        position: key % 2 == 0 ? 'left' : 'right',
        type: 'value',
        max,
        splitNumber: 10,
        interval: (max - min) / 10,
        min,
        name: unit ? ` 单位(${unit})` : '',
        nameTextStyle: {
          color: '#999999',
          align:
            key % 2 === 0 ? (max > 10000 ? 'right' : 'center') : max > 10000 ? 'left' : 'center',
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#333',
          fontSize: '12px',
          formatter: (value: string) => {
            return getValue(value, unit);
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A1D0C4',
            type: [5, 10],
            offset: 5,
          },
        },
      };
    });

    const option: any = {
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        formatter: (params) => {
          return params
            .map((param) => {
              const unit =
                nowData.datas?.find((i) => param.seriesName === i.indexTitie)?.unit || '';
              return `${
                param.axisValue + '<br />'
              }<span style="display: inline-flex; width: 100%; justify-content: space-between;"><span>${
                param.marker
              }&nbsp;${
                param.seriesName
              }</span>&nbsp;&nbsp;<span><strong style="color: #333">${getValue(
                param.value,
                unit,
              )}</strong>&nbsp;&nbsp;${
                param.value !== null && param.value !== undefined && param.value !== '' ? unit : ''
              }</span></span>`;
            })
            .join('<br/>');
        },
        backgroundColor: '#E1F3F1',
        borderColor: '#02695E',
      },
      legend: {
        show: true,
        type: 'scroll',
        icon: 'circle',
        left: 0,
        itemWidth: 8,
        itemHeight: 8,
        padding: [
          0,
          gridRight + 80 * Math.floor(yAxisList.length / 2),
          0,
          gridLeft + (yAxisList.length > 2 ? 65 * Math.ceil(yAxisList.length / 2) : 85),
        ],
        textStyle: {
          color: '#999999',
          fontSize: '12px',
        },
        top: 5,
      },
      grid: {
        left: gridLeft + 2,
        right: gridRight + (props.params.timeType == 5 ? 25 : 8),
        bottom: 5,
        top: 32,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#59B29B',
          },
        },
        axisPointer: {
          show: true,
          type: 'line',
          lineStyle: {
            color: '#119078',
            type: [5, 10],
          },
        },
        axisLabel: {
          show: true,
          color: '#333',
          fontSize: '12px',
          padding: [5, 0, 0, 0],
          formatter: (value, index) => {
            if (index === 0) {
              return `{date| ${value}}`;
            }
            return value;
          },
          rich: {
            date: {
              padding: [0, 0, 0, 30],
            },
          },
        },
        data: nowData.time.length ? nowData.time : mockTime(),
      },
      yAxis: yAxisList,
      series: nowData.datas.map(({ indexTitie, data, unit }, index) => ({
        data,
        type: 'line',
        smooth: true,
        symbol: 'none',
        color: colorList[index % colorList.length],
        name: indexTitie,
        yAxisIndex:
          yAxisList.findIndex((val) => val.unit == unit) < 0
            ? 0
            : yAxisList.findIndex((val) => val.unit == unit),
      })),
    };

    setOptions(option);
  }

  watch(() => chartsData, renderEcharts, {
    deep: true,
  });
</script>

<style scoped lang="less">
  .chart {
    padding: 16px;
    overflow: hidden;
  }
</style>
