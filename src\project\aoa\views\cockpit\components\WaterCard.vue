<template>
  <CockpitCardBox title="吨水单耗">
    <template #title-slot>
      <AiCard :questionTemplate="question" questionTitle="吨水单耗" :deepThinking="deepThinking" />
    </template>
    <template #content>
      <div class="water-content w-full h-full" v-if="first">
        <div ref="chartRef" class="chart w-full h-full"></div>
      </div>
      <div class="empty-container w-full h-full" v-else>
        <Empty />
      </div>
    </template>
  </CockpitCardBox>
</template>

<script lang="ts" setup>
  import { ref, PropType, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { CockpitCardBox, Empty } from './CockpitCard';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { getScaleValByClientWidth } from '../data';
  import type { ChartData } from '../type';
  import { lineChartIcon1, lineChartIcon2 } from '/@aoa/views/cockpit/data';
  import { AiCard } from '/@aoa/components/AiCard';

  const props = defineProps({
    data: {
      type: Array as PropType<ChartData[]>,
      default: () => [],
    },
    waterController: Number || null,
    first: Boolean,
    deepThinking: {
      type: Boolean,
      default: false,
    },
    aiQuestionTemplate: {
      type: String,
      default: '',
    },
  });

  const chartRef = ref(null);
  const { setOptions } = useECharts(chartRef as any);
  const colorList = [
    ['rgba(219, 187, 26, 1)', 'rgba(219, 187, 26, 0.4)', 'rgba(219, 187, 26, 0)'],
    ['rgba(42, 208, 144, 1)', 'rgba(42, 208, 144, 0.4)', 'rgba(42, 208, 144, 0)'],
  ];

  const getLendData = (data) => {
    if (!data) return [];
    return data.map((item) => item.name);
  };

  const getXAxisData = (data: ChartData[]) => {
    if (data.length === 0) return [];
    return data[0]?.data.map((item) => item.collectDateTime);
  };

  const getMax = (data: ChartData[]) => {
    const dataArr = data
      .map((item) => item.data)
      .flat()
      .map((item) => item.value)
      .filter((i) => i !== null) as number[];

    return dataArr.length <= 0 ? 0.5 : Math.max(...dataArr);
  };

  const getMin = () => {
    // console.log(data);
    // const dataArr = data
    //   .map((item) => item.data)
    //   .flat()
    //   .map((item) => item.value)
    //   .filter((i) => i !== null);
    // return dataArr.length <= 0 ? 0 : Math.min(...dataArr);
    return 0;
  };

  const getValueFormatter = (value, digit = 2) => {
    return Number(value).toFixed(digit);
  };

  const getSeriesData = (data: ChartData[], max, _, showMarkArea = true, markAreaIndex = 0) => {
    return data.map((item, index) => {
      return {
        name: item.name,
        type: 'line',
        data: item.data.map((i) => i.value),
        symbol: index % 2 ? lineChartIcon1 : lineChartIcon2,
        symbolSize: 12,
        lineStyle: {
          color: colorList[index][0],
          type: 'dashed',
        },
        itemStyle: {
          color: colorList[index][0],
        },
        markLine:
          props.waterController !== null
            ? {
                silent: true,
                symbol: ['none', 'none'],
                lineStyle: {
                  color: 'rgba(255, 87, 87, 1)',
                },
                data: [
                  {
                    yAxis: props.waterController,
                    label: {
                      show: false,
                    },
                  },
                ],
              }
            : {},
        markArea:
          index === markAreaIndex && showMarkArea
            ? {
                data: [
                  // 危险区域
                  [
                    {
                      name: '危险区域',
                      yAxis: item.maxVal ? max : 0,
                      itemStyle: {
                        color: 'rgba(199, 65, 35, 0.44)',
                      },
                      label: {
                        show: false,
                      },
                    },
                    {
                      yAxis: item.maxVal ?? 0,
                    },
                  ],
                  [
                    {
                      name: '警告区域',
                      yAxis: item.maxVal ?? 0,
                      itemStyle: {
                        color: 'rgba(167, 150, 44, 0.44)',
                      },
                      label: {
                        show: false,
                      },
                    },
                    {
                      yAxis: item.minVal ?? 0,
                    },
                  ],
                  [
                    {
                      name: '安全区域',
                      yAxis: item.minVal ?? 0,
                      itemStyle: {
                        color: 'rgba(22, 173, 115, 0.32)',
                      },
                      label: {
                        show: false,
                      },
                    },
                    {
                      yAxis: 0,
                    },
                  ],
                ],
              }
            : undefined,
      };
    });
  };

  const setChart = () => {
    const { data } = props;
    let showMarkArea = true;
    let max = getMax(props.data);
    let min = getMin();

    if (max === 0) {
      max = 0.5;
    } else {
      max = max > 0.5 ? Math.ceil(max) : 0.5;
    }

    const options = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#E1F3F1',
        borderColor: '#02695E',
        appendToBody: true,
        formatter: (params) => {
          return `<span style="font-size: 0.73vw; line-height: 1.5;">${
            Number(dayjs(params[0].axisValue).format('MM')) + '月'
          }</span></br>
          ${params
            .map((item, index) => {
              return `
            ${item.marker}&nbsp;<span style="font-size: 0.73vw;line-height: 1.5;">${
                item.seriesName
              }</span>&nbsp;<span >${
                item.value !== '' && item.value !== null && item.value !== undefined
                  ? `<span style="font-weight: 600; font-size: 0.73vw;line-height: 1.5;">${getValueFormatter(
                      item.value,
                      data[index].digit,
                    )} </span>&nbsp;<span style="font-weight: 600;font-size: 0.73vw;line-height: 1.5;">${
                      data[index]?.unit
                    }</span>`
                  : '-'
              }</span>&nbsp;
            `;
            })
            .join('</br>')}
          `;
        },
        // valueFormatter: (value) => {
        //   return value !== '' && value !== null && value !== undefined
        //     ? `${getValueFormatter(value, data[0].digit)} ${data[0]?.unit}`
        //     : '-';
        // },
      },
      legend: {
        show: false,
        data: getLendData(data),
        top: 0,
        right: 0,
        itemWidth: getScaleValByClientWidth(8),
        itemHeight: getScaleValByClientWidth(8),
        itemGap: 8,
        icon: 'circle',
        textStyle: {
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          lineHeight: getScaleValByClientWidth(13 * 1.25),
        },
      },
      grid: {
        left: 8,
        right: 8,
        bottom: 0,
        top: getScaleValByClientWidth(35),
        containLabel: true,
        show: true,
        backgroundColor: 'rgba(2, 53, 32, 0.40)',
        borderWidth: 0,
      },
      xAxis: {
        type: 'category',
        data: getXAxisData(data),
        boundaryGap: true,
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          padding: [0, 5, 0, 0],
          formatter: (value) => {
            return Number(dayjs(value).format('MM')) + '月';
          },
        },
        axisTick: {
          show: false,
        },
        axisPointer: {
          show: true,
          type: 'line',
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            type: [5, 10],
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            shadowColor: 'rgba(178, 255, 241, 1)',
            shadowOffsetY: -2,
            shadowOffsetX: 0,
            shadowBlur: 5,
            width: 1,
          },
        },
      },
      yAxis: {
        type: 'value',
        name: data[0]?.unit ? `吨水单耗(${data[0].unit})` : '',
        max,
        min,
        interval: (max - min) / 5,
        nameTextStyle: {
          fontSize: getScaleValByClientWidth(13),
          color: '#fff',
          align: 'left',
          padding: [0, 0, 0, getScaleValByClientWidth(-25)],
        },
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          formatter: (value) => getValueFormatter(value, 1),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(210, 255, 247, 0.40)',
            type: [5, 10],
            offset: 5,
          },
        },
      },
      series: getSeriesData(data, max, min, showMarkArea),
    };
    setOptions(options as any);

    // const chartInstance = getInstance();
    // if (chartInstance) {
    //   chartInstance.off('legendselectchanged');
    //   chartInstance.on('legendselectchanged', (params: any) => {
    //     const hasSelected = Object.values(params.selected).some(Boolean);
    //     if (hasSelected) {
    //       const key = Object.values(params.selected);
    //       const markAreaIndex = key.findIndex((i) => i);
    //       const options = chartInstance.getOption();
    //       setOptions({
    //         ...options,
    //         series: getSeriesData(data, max, min, hasSelected, markAreaIndex),
    //       });
    //     }
    //   });
    // }
  };

  watch(
    () => props.data,
    () => {
      setChart();
    },
    { deep: true },
  );

  watch(
    () => props.waterController,
    () => {
      setChart();
    },
    { deep: true },
  );

  const getQuestion = (data) => {
    if (!props.aiQuestionTemplate || !props.aiQuestionTemplate.includes('${data}')) {
      return props.aiQuestionTemplate;
    }
    if (!data.length) {
      return '';
    }
    const str = data[0]?.data
      .map((item) => {
        return `${Number(dayjs(item.collectDateTime).format('MM'))}月${
          item.value !== null && item.value !== undefined
            ? Number(item.value).toFixed(data[0].digit ?? 2)
            : '-'
        }${data[0].unit}`;
      })
      .join('、');
    return props.aiQuestionTemplate.replace('${data}', str);
  };

  const question = computed(() => {
    const data = props.data;
    return getQuestion(data);
  });
</script>
<style lang="less" scoped>
  @media screen and (min-width: 1800px) {
    .water-content {
      .px2vw(6);
      padding: @vw;
    }
  }
</style>
