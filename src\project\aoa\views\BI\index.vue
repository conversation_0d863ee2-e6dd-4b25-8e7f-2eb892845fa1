<template>
  <div class="overview-page">
    <div class="page-decorate">
      <div class="page-decorate-left"> </div>
      <div class="page-decorate-right"> </div>
      <div class="page-decorate-bottom"> </div>
    </div>
    <div class="page-head">
      <div class="head-left">
        <div class="back-box" @click="back"><img :src="back_icon" /></div>
        <ADateTime :theme="theme" />
      </div>
      <div class="title">精确曝气工艺看板</div>
      <div class="info" v-if="showWeather">
        <AWeater :theme="theme" />
      </div>
    </div>
    <div class="page-content">
      <div class="content-inner">
        <div class="content-row content-row-left">
          <div
            class="row-item"
            :data-resource-code="indexCodeList[0].groupCode"
            style="height: 42.32%"
          >
            <!-- 未端溶解氧 -->
            <OxygenCard
              :data="oxygenData"
              :alarmData="alarmData"
              :groupCode="indexCodeList[0].groupCode"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(7)"
            />
          </div>
          <div
            class="row-item"
            :data-resource-code="nitrateData.value || indexCodeList[3].groupCode"
            style="height: 26.98%"
          >
            <!-- 硝氮 -->
            <NitrateCard
              :data="nitrateData"
              :groupCode="indexCodeList[3].groupCode"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(8)"
              @update:value="handleChangeNitrateValue"
            />
          </div>
          <div
            class="row-item"
            :data-resource-code="indexCodeList[1].groupCode"
            style="height: 27.47%"
          >
            <!-- 氨氮 -->
            <AmmoniaCard
              :data="ammoniaData"
              :alarmData="alarmData"
              :groupCode="indexCodeList[1].groupCode"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(9)"
            />
          </div>
        </div>
        <div class="content-row content-row-right">
          <div
            class="row-item"
            :data-resource-code="indexCodeList[2].groupCode"
            style="height: 26.99%"
          >
            <!-- 进水 -->
            <WaterCard
              :data="waterData"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(10)"
            />
          </div>
          <div
            class="row-item"
            style="height: 33.65%"
            :data-resource-code="indexCodeList[4].groupCode"
          >
            <!-- 吨水单耗 -->
            <WaterCostCard
              :data="electricityData.data"
              :first="electricityData.first"
              :deepThinking="deepThinking"
              :aiQuestionTemplate="getCurrentAiQuestionTemplate(11)"
            />
          </div>
          <div class="row-item" style="height: 36.13%">
            <!-- 实时报警 -->
            <AlarmCard :data="alarmData" />
          </div>
        </div>
      </div>
      <div class="in-water" :data-resource-code="indexCodeList[5].groupCode">
        <!-- 进水流量 -->
        <BottomCard :data="waterFlowData" :groupCode="indexCodeList[5].groupCode" />
      </div>
    </div>
    <!-- <tipsItem
      v-for="(item, index) in tipsItemData"
      :key="index"
      :data="item.data"
      :style="formatTipsStyle(item)"
      :theme="theme"
    /> -->
    <bgVideo :alarmData="alarmData" @imgSize="imgSize" @openModal="openModal($event)" />
    <IndicatorModal
      v-model:open="indicatorOpen"
      :width="isIPCRef ? '100%' : '1272px'"
      :bodyStyle="modalBodyStyle"
      :destroyOnClose="true"
      :groupInfo="groupInfo"
      :themeColor="themeColor"
      :echartsConfig="echartsConfig"
      :multiple="multiple"
      :factoryId="factoryId"
      title="指标详情"
      :requestHeader="requestHeader"
      :footer="modalFooter"
      :base-url="baseUrl"
      wrapClassName="aoa-ipc-modal-curve"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onUnmounted, computed } from 'vue';
  // import { useDomain } from '/@/locales/useDomain';
  // import { useUserStore } from '/@/store/modules/user';
  import dayjs from 'dayjs';
  import { useRouter } from 'vue-router';
  import { getParams } from '/@aoa/utils';
  import { getParamKeyApi } from '/@/api/admin/param';
  import { ADateTime } from '/@aoa/components/ADateTime';
  import { AWeater } from '/@aoa/components/AWeater';
  import back_icon from './assets/images/back_icon.png';
  import { indexCodeList } from './data';
  import { mockWaterFlowData, mockElectricityChartData } from './data';
  import { callResourceFunction } from '/@/api/config-center/scenes-group';
  import { getSenceGroupTree } from '/@zhcz/api/scenes-group';
  import { getBiEvent } from '/@aoa/api/event-center';
  import { getFactoryId } from '/@aoa/utils/factory';
  import { useIntervalFn } from '@vueuse/core';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import { ParamsKeyEnum } from '/@aoa/enums';
  import OxygenCard from './components/OxygenCard.vue';
  import AmmoniaCard from './components/AmmoniaCard.vue';
  import WaterCard from './components/WaterCard.vue';
  import NitrateCard from './components/NitrateCard.vue';
  import WaterCostCard from './components/WaterCostCard.vue';
  import AlarmCard from './components/AlarmCard.vue';
  import BottomCard from './components/BottomCard.vue';
  // import tipsItem from '../cockpit/components/tipsItem.vue';
  // import firstImg from '../cockpit/assets/images/1chi.png';
  // import secondImg from '../cockpit/assets/images/2chi.png';
  // import thirdImg from '../cockpit/assets/images/3chi.png';
  import bgVideo from '../cockpit/components/bgVideo2.vue';
  import { IndicatorModal } from 'hlxb-business-ui';
  import { getProcessEditorTheme } from '/@process-editor/assets/theme';
  import { isIPCBreakPoint } from '/@/hooks/event/useBreakpoint';
  import { useWindowSize } from '@vueuse/core';
  import { getToken } from '/@/utils/auth';
  import { useDomain } from '/@/locales/useDomain';
  import { getAppEnvConfig } from '/@/utils/env';

  const { themeColor, echartsConfig } = getProcessEditorTheme();
  const factoryId = getFactoryId();
  const { isIPCRef } = isIPCBreakPoint();
  const { height } = useWindowSize();
  const modalBodyStyle = computed(() => {
    return isIPCRef.value
      ? {
          height: `${height.value - 64}px`,
        }
      : { height: '716px' };
  });
  const modalFooter = computed(() => {
    return isIPCRef.value ? null : ' ';
  });

  const baseUrl = computed(() => getAppEnvConfig().VITE_GLOB_API_URL);

  const { getTenantId } = useDomain();
  const requestHeader: any = {
    Authorization: getToken(),
    'Tenant-Id': getTenantId.value,
  };

  const indicatorOpen = ref(false);
  const groupInfo = ref({
    groupCode: '',
    resourceInterfaceId: '3',
    jsConvert: false,
    indexCodes: '',
  });
  const multiple = ref(false);

  const openModal = ({ indexCode, groupCode }) => {
    console.log(indexCode, groupCode);
    groupInfo.value.indexCodes = indexCode;
    groupInfo.value.groupCode = groupCode;
    indicatorOpen.value = true;
  };
  // import { positionList } from './data';
  // import { codeList } from './data';

  import type { LineChartData, DataList, DataListSelectData, AlarmData } from './type';
  const theme = ref<'light' | 'dark'>('dark');
  const router = useRouter();
  const indexList = ref<any[]>([]);

  const baseWidth = 1920;
  const baseHeight = 1080;
  const imgRect = ref({ left: 0, top: 0, width: baseWidth, height: baseHeight });
  const imgSize = (img: any) => {
    imgRect.value = { ...img };
  };

  // 是否开启天气组件
  const showWeather = ref(false);

  const getShowWeather = async () => {
    const data = await getParamKeyApi(ParamsKeyEnum.HasWeater);
    showWeather.value = data === '1';
  };

  getShowWeather();

  // 是否开启深度思考
  const deepThinking = ref(false);
  const getDeepThinking = async () => {
    const data = await getParamKeyApi(ParamsKeyEnum.DeepThinking);
    deepThinking.value = data === '1';
  };

  getDeepThinking();

  const AiQuestionTemplate = ref<{ label: string; value: string; index: number }[]>([]);

  // 获取AI提问模板
  const getAIQuestionWords = async () => {
    const res = await getDictTypeListApi({
      type: ParamsKeyEnum.AI_QUESTION_WORDS,
    });
    if (res && res.length) {
      AiQuestionTemplate.value = res.map((item) => ({
        label: item.label,
        value: item.value,
        index: item.sortOrder,
      }));
    }
  };
  getAIQuestionWords();

  // 获取对应AI提问模板
  const getCurrentAiQuestionTemplate = (index) => {
    return AiQuestionTemplate.value.find((item) => item.index === index)?.value;
  };

  /**  未端溶解氧 */
  const oxygenData = ref<DataList[]>([]);
  const getOxygenData = async () => {
    const params = getParams(indexCodeList[0]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      oxygenData.value = data;
    } else {
      oxygenData.value = [];
    }
  };

  /** 氨氮数据 */
  const ammoniaData = ref<DataList[]>([]);

  const getAmmoniaData = async () => {
    const params = getParams(indexCodeList[1]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      ammoniaData.value = data;
    } else {
      ammoniaData.value = [];
    }
  };

  /** 进水数据 */
  const waterData = ref<DataList[]>([
    {
      value: '0',
      name: '当月进水量',
      indexCode: '',
      digit: 0,
      unit: 'm³',
    },
    {
      value: '0',
      name: '同期进水量',
      indexCode: '',
      digit: 0,
      unit: 'm³',
    },
  ]);

  // 进水量
  const getWaterData = async () => {
    const todayDate = [
      dayjs().startOf('month').format('YYYY-MM-DD 00:00:00'),
      dayjs().format('YYYY-MM-DD 23:59:59'),
    ];
    const yesterdayDate = [
      dayjs().subtract(1, 'year').startOf('month').format('YYYY-MM-DD 00:00:00'),
      dayjs().subtract(1, 'year').format('YYYY-MM-DD 23:59:59'),
    ];
    const todayParams = getParams(indexCodeList[2], todayDate[0], todayDate[1]);
    const yesterdayParams = getParams(indexCodeList[2], yesterdayDate[0], yesterdayDate[1]);

    const [todayData, yesterdayData] = await Promise.all([
      callResourceFunction(todayParams),
      callResourceFunction(yesterdayParams),
    ]);
    if (todayData && todayData.length) {
      waterData.value[0].digit = todayData[0]?.digit ?? 0;
      waterData.value[0].indexCode = todayData[0]?.indexCode;
      waterData.value[0].value = todayData[0]?.value;
      waterData.value[0].unit = todayData[0]?.unitName;
    } else {
      waterData.value[0].digit = 0;
      waterData.value[0].indexCode = '';
      waterData.value[0].value = null;
      waterData.value[0].unit = '';
    }

    if (yesterdayData && yesterdayData.length) {
      waterData.value[1].digit = yesterdayData[0]?.digit ?? 0;
      waterData.value[1].indexCode = yesterdayData[0]?.indexCode;
      waterData.value[1].value = yesterdayData[0]?.value;
      waterData.value[1].unit = yesterdayData[0]?.unitName;
    } else {
      waterData.value[1].digit = 0;
      waterData.value[1].indexCode = '';
      waterData.value[1].value = null;
      waterData.value[1].unit = '';
    }
  };

  /** 硝氮数据 */
  const nitrateData = reactive<DataListSelectData>({
    value: '',
    options: [],
    data: [],
  });

  const setNitrateOptions = async () => {
    const data =
      indexList.value.find((item) => item.groupCode === indexCodeList[3].groupCode)?.children || [];
    nitrateData.options = data?.map((item) => ({
      value: item.groupCode,
      label: item.name,
    }));
    nitrateData.value = nitrateData.options.length ? nitrateData.options[0]?.value : '';
  };
  const getNitrateData = async () => {
    if (!nitrateData.value) return;
    const tempParams = { ...indexCodeList[3], groupCode: nitrateData.value };
    const params = getParams(tempParams);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      nitrateData.data = data;
    } else {
      nitrateData.data = [];
    }
  };

  const handleChangeNitrateValue = (value) => {
    nitrateData.value = value;
    getNitrateData();
  };

  /** 吨水单耗 */
  const electricityData = reactive<LineChartData>({
    data: mockElectricityChartData,
    first: false,
  });

  const getElectricityData = async () => {
    const startDateTime = dayjs().startOf('month').format('YYYY-MM-DD 00:00:00');
    const endDateTime = dayjs().endOf('month').format('YYYY-MM-DD 23:59:59');
    const params = getParams(indexCodeList[4], startDateTime, endDateTime);

    // const res = await callResourceFunction(params);
    // 传统电耗
    // const _startDateTime = dayjs().startOf('month').format('2024-MM-DD 00:00:00');
    // const _endDateTime = dayjs().endOf('month').format('2024-MM-DD 23:59:59');
    // const _params = getParams(indexCodeList[9], _startDateTime, _endDateTime);

    const [res] = await Promise.all([callResourceFunction(params)]);
    if (res && res.length) {
      electricityData.data = res.map((item) => {
        return {
          name: item.indexName,
          // name1: _res.length ? _res[index].indexName : '',
          value: item.value,
          // value1: _res.length ? _res[index].value : null,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
          collectDateTime: item.collectDateTime,
        };
      });
    } else {
      electricityData.data = mockElectricityChartData;
    }
    electricityData.first = true;
  };

  const levelMap = {
    一级报警: 1,
    二级报警: 2,
    三级报警: 3,
  };
  /** 实时报警 */
  const alarmData = ref<AlarmData[]>([]);
  const getAlarmData = async () => {
    const params = {
      factoryId: getFactoryId(),
    };
    const res = await getBiEvent(params);
    alarmData.value = res.map((item) => ({
      id: item.id,
      title: item.topic,
      warnValue: item.xianzhi,
      limitValue: item.fankuizhi,
      warnEventLevel: levelMap[item.level],
      creationTime: item.time,
      indicatorCode: item.indicatorCode,
    }));
  };

  /** 中心流量 */
  const waterFlowData = ref<DataList[]>(mockWaterFlowData);

  const getWaterFlowData = async () => {
    const params = getParams(indexCodeList[5]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 0,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      waterFlowData.value = data;
    } else {
      waterFlowData.value = mockWaterFlowData;
    }
  };

  // 硝氮数据
  const getNitrateDataAsync = async () => {
    await setNitrateOptions();
    await getNitrateData();
  };

  const getData = () => {
    getOxygenData();
    getAmmoniaData();
    getWaterData();
    getWaterFlowData();
    getElectricityData();
    getAlarmData();
    // getHaoYangData();
    // getQYangData();
    // getYYangData();
  };

  const getPollData = () => {
    getOxygenData();
    getAmmoniaData();
    getWaterData();
    getNitrateData();
    getWaterFlowData();
    getElectricityData();
    getAlarmData();
    // getHaoYangData();
    // getQYangData();
    // getYYangData();
  };

  const getResourceData = async () => {
    const data = await getSenceGroupTree();
    // 工艺监控
    indexList.value =
      data
        .find((item) => item.senceCode === 'JQBQ_V3')
        ?.children?.find((item) => item.groupCode === 'JQBQ_GYJK')?.children || [];
    if (indexList.value.length) {
      getNitrateDataAsync();
    }
  };

  getResourceData();
  getData();

  const { pause } = useIntervalFn(getPollData, 10 * 1000);
  onUnmounted(() => {
    pause();
  });

  // const { getTenantId, getDomain } = useDomain();
  // const userStore = useUserStore();
  // const factoryId = computed(() => userStore.getCurrentFactoryId);
  // 返回
  const back = () => {
    const path = router.currentRoute.value.meta?.backRoutePath ?? '';
    if (path) {
      // router.push({
      //   path: path as string,
      //   query: {
      //     tenantId: getTenantId.value,
      //     domain: getDomain.value,
      //     factoryId: factoryId.value,
      //   },
      // });
      router.push(path);
    } else {
      router.go(-1);
    }
  };
</script>

<style lang="less" scoped>
  @import '/@aoa/assets/css/font.less';

  .overview-page {
    position: relative;
    width: 100%;
    height: 100%;
    // background: url('./assets/images/bi_bim.png') center center no-repeat;
    // background-size: cover;
    overflow: hidden;
    color: #fff;

    .page-decorate {
      .page-decorate-left,
      .page-decorate-right {
        position: absolute;
        width: 20px;
        top: 20px;
        height: calc(100% - 52px);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center top;
        overflow: hidden;
        z-index: 99;
      }

      .page-decorate-left {
        left: 0;
        background-image: url('./assets/images/bi_left_bg.png');
      }

      .page-decorate-right {
        right: 0;
        background-image: url('./assets/images/bi_right_bg.png');
      }

      .page-decorate-bottom {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        height: 18px;
        background-size: 100% 100%;
        background-image: url('./assets/images/bi_footer.png');
        z-index: 99;
      }
    }

    .page-head {
      position: relative;
      padding: 14px 28px 0;
      width: 100%;
      height: 72px;
      background: url('./assets/images/title.png') center top no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 10;

      .head-left {
        .px2vw(24);
        display: flex;
        align-items: center;
        gap: 0 @vw;

        .back-box {
          .width-prop(40);
          .width-prop(40, height);
          border-radius: 50%;
          cursor: pointer;

          img {
            width: 100%;
          }
        }
      }

      .title {
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);
        font-family: 'Alimama ShuHeiTi';
        letter-spacing: 4px;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        line-height: 60px;
      }

      .info {
        display: flex;
        width: max-content;
      }
    }

    .page-content {
      position: relative;
      padding: 10px 28px 6px;
      width: 100%;
      height: calc(100% - 72px - 18px);

      .content-inner {
        display: flex;
        justify-content: space-between;
        gap: 12px;
        height: 100%;

        .content-row {
          height: 100%;
          display: flex;
          justify-content: space-between;
          gap: 12px;

          .row-item {
            position: relative;
          }

          &-left,
          &-right {
            width: 292px;
            display: flex;
            flex-direction: column;
            z-index: 2;
          }

          &-middle {
            position: relative;
            flex: 1;
            padding: 0 12px;
            z-index: 2;
          }
        }
      }
    }
  }

  @media screen and (min-width: 1800px) {
    .overview-page {
      .page-content {
        .content-inner {
          .content-row {
            &-left,
            &-right {
              .width-prop(372);
            }
          }
        }
      }
    }
  }

  @media screen and (min-width: 2000px) {
    .overview-page {
      .page-decorate {
        .page-decorate-left,
        .page-decorate-right {
          .width-prop(24);
          .px2vh(64);
          height: calc(100% - @vh);
          .height-prop(24, top);
        }

        .page-decorate-bottom {
          .height-prop(24, height);
        }
      }

      .page-head {
        .px2vw(28);
        .px2vh(14);
        padding: @vh @vw 0;
        .height-prop(94, height);

        .title {
          .font-size(28);
          .height-prop(86, line-height);
        }
      }

      .page-content {
        .px2vw(28);
        .px2vh(12);
        padding: @vh @vw;
        height: calc(100% - (94 / @design-height) * 100vh - (24 / @design-height) * 100vh);
      }
    }
  }
</style>
