<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    :loading="okLoading"
    width="1000px"
  >
    <Form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :labelCol="{ style: { width: formState.ruleType === 2 ? '110px' : '80px' } }"
    >
      <Row :gutter="12">
        <Col :span="12">
          <FormItem label="规则标题" name="title">
            <Input
              v-model:value="formState.title"
              placeholder="请输入规则标题"
              :disabled="allDisabled"
            />
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="数据适配器" :labelCol="{ style: { width: '100px' } }">
            <Input v-model:value="formState.adapterName" disabled />
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="报警等级" name="warnEventLevel">
            <ApiSelect
              :api="getWarnEventLevelList"
              v-model:value="formState.warnEventLevel"
              labelField="label"
              valueField="value"
              placeholder="请选择报警等级"
              :disabled="allDisabled"
            />
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="报警类型" name="warnEventType" :labelCol="{ style: { width: '100px' } }">
            <ApiSelect
              :api="getWarnEventTypeList"
              v-model:value="formState.warnEventType"
              labelField="name"
              valueField="id"
              placeholder="请选择报警类型"
              :disabled="allDisabled"
            />
          </FormItem>
        </Col>
        <!-- <Col :span="12">
          <FormItem label="事件来源" name="eventSource">
            <ApiSelect
              :api="getEventSourceList"
              v-model:value="formState.eventSource"
              labelField="label"
              valueField="value"
            />
          </FormItem>
        </Col> -->
        <Col :span="12">
          <FormItem label="持续报警" name="isRepeatWarn" required>
            <Select
              v-model:value="formState.isRepeatWarn"
              class="w-full"
              placeholder="请选择是否持续报警"
              :disabled="allDisabled"
            >
              <SelectOption value="open"> 开启 </SelectOption>
              <SelectOption value="close"> 关闭 </SelectOption>
            </Select>
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem
            label="自动解除"
            name="isAutoConfirm"
            required
            :labelCol="{ style: { width: '100px' } }"
          >
            <Select
              v-model:value="formState.isAutoConfirm"
              class="w-full"
              placeholder="请选择是否自动解除"
              :disabled="allDisabled"
            >
              <SelectOption value="open"> 开启 </SelectOption>
              <SelectOption value="close"> 关闭 </SelectOption>
            </Select>
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="报警内容" name="warnContent" required>
            <Input
              v-model:value="formState.warnContent"
              placeholder="请输入报警内容"
              :disabled="allDisabled"
            />
          </FormItem>
        </Col>
        <Col v-if="formState.ruleType === 2" :span="24">
          <FormItem label="报警限值描述" name="limitValue" required>
            <Input
              v-model:value="formState.limitValue"
              placeholder="请输入报警限值描述"
              :disabled="allDisabled"
            />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="规则类型" name="ruleType" required>
            <RadioGroup
              v-model:value="formState.ruleType"
              @change="handleChangeType"
              :disabled="allDisabled"
            >
              <Radio v-for="item in ruleType" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
      </Row>
      <Row v-if="formState.ruleType === 1" :gutter="12" class="easy-rule">
        <Col :span="12">
          <FormItem label="指标名称" name="target">
            <Select
              v-model:value="formState.target"
              :filterOption="filterOption"
              labelInValue
              show-search
              placeholder="请选择指标名称"
              :disabled="allDisabled"
            >
              <SelectOption
                v-for="item in targetList"
                :key="item.indicatorName"
                :value="item.indicatorCode"
                :data="item"
              >
                <Tooltip :title="item.indicatorName + ' ' + item.indicatorCode">
                  {{ item.indicatorName + ' ' + item.indicatorCode }}
                </Tooltip>
              </SelectOption>
            </Select>
          </FormItem>
        </Col>
        <Col :span="4">
          <FormItem label="" name="defaultAction">
            <Select
              v-model:value="formState.defaultAction"
              labelInValue
              class="w-full"
              placeholder="请选择比较运算符"
              @select="handleChangeAction"
              :disabled="allDisabled"
            >
              <SelectOption
                v-for="item in ruleActionList"
                :key="item.id"
                :value="item.id"
                :data="item"
              >
                {{ item.title }}
              </SelectOption>
            </Select>
          </FormItem>
        </Col>
        <Col :span="4">
          <FormItem label="" name="minValue">
            <InputNumber
              v-model:value="formState.minValue"
              placeholder="请输入判断值"
              :disabled="allDisabled"
            />
          </FormItem>
        </Col>
        <Col v-if="valueCount === 2" :span="4">
          <FormItem label="" name="maxValue">
            <InputNumber
              v-model:value="formState.maxValue"
              placeholder="请输入判断值"
              :disabled="allDisabled"
            />
          </FormItem>
        </Col>
      </Row>
      <template v-else>
        <FormItem label="使用标签" name="associatedLabelList" required>
          <Select
            mode="multiple"
            showArrow
            v-model:value="formState.associatedLabelList"
            class="w-full"
            :open="false"
            :options="selectNode"
            placeholder="请选择标签"
            @click="openTagModal"
            @change="handleChangeTag"
          >
            <template #suffixIcon>
              <EditOutlined />
            </template>
          </Select>
        </FormItem>
        <Row :gutter="12" class="custom-rule">
          <Col :span="24">
            <FormItem label="自定义规则" name="script">
              <div>
                <CodeEditor
                  placeholder="请输入自定义规则"
                  v-model:value="formState.script"
                  :disabled="allDisabled"
                  :row="4"
                  class="h-40"
                  mode="JSON"
                />
              </div>
            </FormItem>
          </Col>
        </Row>
      </template>
    </Form>
    <template #footer>
      <a-button @click="handleCancel" class="default-btn" v-if="!allDisabled">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading" v-if="!allDisabled"
        >保存</a-button
      >
      <a-button @click="handleCancel" v-if="allDisabled">关闭</a-button>
    </template>
  </BasicModal>

  <TagModal @register="registerTagModal" @success="tagSelect" />
</template>

<script lang="ts" setup name="EditModal">
  import { computed, ref, reactive, UnwrapRef } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { CodeEditor } from '/@/components/CodeEditor';
  import {
    getAdapterList,
    getWarnEventLevelList,
    getWarnEventTypeList,
    getDefaultRuleActionList,
    getRuleTypeList,
    changeWarnEvent,
  } from '/@aoa/api/event-center';
  import { getIndicatorList } from '/@/api/config-center/monitoring-points';
  import {
    Form,
    FormItem,
    Input,
    Select,
    SelectOption,
    RadioGroup,
    Row,
    Col,
    Radio,
    InputNumber,
    Tooltip,
  } from 'ant-design-vue';
  import { ApiSelect } from '/@/components/Form/index';
  import { createLocalStorage } from '/@/utils/cache';
  import { TENANTID_KEY, FACTORY_KEY } from '/@/enums/cacheEnum';
  import TagModal from './TagModal.vue';
  import { EditOutlined } from '@ant-design/icons-vue';

  interface ruleType {
    label: string;
    value: number;
  }
  interface ruleAction {
    id: string;
    title: string;
    valueCount: number;
  }
  interface FormState {
    id?: string;
    title: string;
    adapterId: string;
    adapterName: string;
    ruleType: number;
    target: any;
    script: string;
    defaultAction: { value: string } | undefined;
    minValue: string;
    maxValue: string;
    warnEventLevel: number | undefined;
    warnEventType: number | undefined;
    // eventSource: number | undefined;
    isRepeatWarn: string;
    isAutoConfirm: string;
    warnContent: string;
    limitValue: string;
    associatedLabelList: string[];
  }

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const title = computed(() => (isEdit.value ? '编辑规则' : '新增规则'));
  const isEdit = ref(false);
  const okLoading = ref(false);
  const formRef = ref();
  const formState: UnwrapRef<FormState> = reactive({
    id: '',
    title: '',
    adapterId: '',
    adapterName: '',
    ruleType: 1,
    target: undefined,
    script: '',
    defaultAction: undefined,
    minValue: '',
    maxValue: '',
    warnEventLevel: undefined,
    warnEventType: undefined,
    // eventSource: undefined,
    isRepeatWarn: 'open',
    isAutoConfirm: 'open',
    warnContent: '',
    limitValue: '',
    associatedLabelList: [],
  });
  const rules: any = {
    title: [{ required: true, message: '请输入规则标题', trigger: 'blur' }],
    target: [{ required: true, message: '请选择指标名称', trigger: 'blur' }],
    defaultAction: [{ required: true, message: '请选择操作符', trigger: 'blur' }],
    minValue: [{ required: true, message: '请输入判断值', trigger: 'blur' }],
    maxValue: [{ required: true, message: '请输入判断值', trigger: 'blur' }],
    script: [{ required: true, message: '请输入自定义规则', trigger: 'blur' }],
    warnEventLevel: [{ required: true, message: '请选择报警等级', trigger: 'blur' }],
    warnEventType: [{ required: true, message: '请选择报警类型', trigger: 'blur' }],
    // eventSource: [{ required: true, message: '请选择事件来源', trigger: 'blur' }],
    warnContent: [{ required: true, message: '请输入报警内容', trigger: 'blur' }],
    limitValue: [{ required: true, message: '请输入报警限值描述', trigger: 'blur' }],
    associatedLabelList: [
      {
        required: true,
        message: '请选择标签',
        trigger: 'change',
      },
    ],
  };
  const ruleType = ref<ruleType[]>([]);
  const targetList: any = ref([]);
  const ruleActionList = ref<ruleAction[]>([]);
  const valueCount = ref(1);
  const allDisabled = ref(false);
  const selectNode = ref<{ value: string; label: string }[]>([]);
  // const jsConvertEditorRef = ref<any>(null);

  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    await initFormSelect();
    isEdit.value = data.isEdit;
    allDisabled.value = data.disabled;
    if (!isEdit.value) {
      return;
    }
    formState.id = data.record.id;
    formState.title = data.record.title;
    formState.ruleType = data.record.ruleType;
    formState.warnEventLevel = data.record.warnEventLevel;
    formState.warnEventType = data.record.warnEventType;
    // formState.eventSource = data.record.eventSource;
    formState.isRepeatWarn = data.record.isRepeatWarn ? 'open' : 'close';
    formState.isAutoConfirm = data.record.isAutoConfirm ? 'open' : 'close';
    formState.warnContent = data.record.warnContent;
    formState.limitValue = data.record.limitValue;
    if (data.record.ruleType === 1) {
      formState.target = {
        value: data.record.targets[0].target,
        key: data.record.targets[0].targetAlias,
      };
      formState.defaultAction = {
        value: data.record.targets[0].defaultAction,
      };
      valueCount.value = ruleActionList.value.find(
        (item) => item.id === data.record.targets[0].defaultAction,
      )?.valueCount as number;
      formState.minValue = data.record.targets[0].values[0];
      if (data.record.targets[0].values.length === 2) {
        formState.maxValue = data.record.targets[0].values[1];
      }
    } else {
      selectNode.value = data.record.targets.map((item) => {
        return {
          value: item.target,
          label: item.targetAlias,
        };
      });
      formState.associatedLabelList = data.record.targets.map((item) => item.target);
      formState.script = data.record.script;
    }
  });
  const [registerTagModal, { openModal: openTagsModal }] = useModal();

  const filterOption = (input, option) => {
    const inputValArr = input.split(' ');
    return inputValArr.every((item) => {
      return (
        option.data.indicatorName.toLowerCase().indexOf(item.toLowerCase()) >= 0 ||
        option.data.indicatorCode.toLowerCase().indexOf(item.toLowerCase()) >= 0
      );
    });
  };

  // 初始化表单下拉框数据
  const initFormSelect = async () => {
    okLoading.value = true;
    await getAdapterList().then((data) => {
      formState.adapterId = data[0]?.id;
      formState.adapterName = data[0]?.title;
    });
    await getRuleTypeList().then((data) => {
      ruleType.value = data;
    });
    const ls = createLocalStorage();
    const tenantId = ls.get(TENANTID_KEY) || '';
    const factoryId = ls.get(FACTORY_KEY) || '';
    const indicatorData = await getIndicatorList({
      page: 1,
      size: 1000,
      factoryId,
      tenantId,
    });
    targetList.value = indicatorData;
    await getDefaultRuleActionList().then((data) => {
      ruleActionList.value = data;
    });
    okLoading.value = false;
  };

  const handleChangeAction = (_value: any, option: any) => {
    valueCount.value = option.data.valueCount;
  };

  const handleChangeType = () => {
    formState.target = undefined;
    formState.defaultAction = undefined;
    formState.minValue = '';
    formState.maxValue = '';
    formState.script = '';
    formState.associatedLabelList = [];
    selectNode.value = [];
  };

  function handleSubmit() {
    try {
      formRef.value
        .validate()
        .then(async () => {
          okLoading.value = true;
          let data = {
            id: isEdit.value ? formState.id : '',
            title: formState.title,
            adapterId: formState.adapterId,
            ruleType: formState.ruleType,
            defaultRule: {},
            script: '',
            customRules: [],
            warnEventLevel: formState.warnEventLevel,
            warnEventType: formState.warnEventType,
            // eventSource: formState.eventSource,
            isRepeatWarn: formState.isRepeatWarn === 'open',
            isAutoConfirm: formState.isAutoConfirm === 'open',
            warnContent: formState.warnContent,
            limitValue: formState.limitValue,
          };
          if (formState.ruleType === 1) {
            data.defaultRule = {
              values: Array.from({ length: valueCount.value }, (_, index) => {
                return index === 0 ? formState.minValue.toString() : formState.maxValue.toString();
              }),
              defaultAction: formState.defaultAction?.value,
              target: formState.target.value,
              targetAlias: formState.target.key,
            };
          } else {
            data.script = formState.script;
            data.customRules = selectNode.value.map((item) => {
              return {
                target: item.value,
                targetAlias: item.label,
              };
            }) as [];
          }
          await changeWarnEvent(data);
          const msg = isEdit.value ? '编辑成功' : '新增成功';
          createMessage.success(msg);
          handleCancel();
          emit('success');
        })
        .catch(() => {
          okLoading.value = false;
        });
    } finally {
      okLoading.value = true;
    }
  }

  function handleCancel() {
    closeModal();
    formRef.value.resetFields();
    okLoading.value = false;
    allDisabled.value = false;
    selectNode.value = [];
  }
  //   function event(data) {
  //   //获取标签的值
  //   var glData2 = cr.getReturnValue('Z_XCCSSPHZ_S', data);
  //   var flag = false;
  //   //自定义判断是否报警，true报警，false不报警
  //   if (glData2 > 7) {
  //     cr.setWarnTag('Z_XCCSSPHZ_S', data)
  //     flag = true;
  //   }
  //   return flag;
  // }

  function getRecentDataSecTemp(data: any[]) {
    return data.map((item, idx) => `var glData${idx + 1} = cr.getReturnValue('${item}',data);`);
  }
  const createFnTemp = (tags: string[]) => {
    const tagsStrList = tags.map((i) => i.replace('#', ''));

    const fnTemp = `
    function event(data){
      ${getRecentDataSecTemp(tagsStrList).join('\n')}
      //自定义判断是否报警，true报警，false不报警
       var flag = false;
      if(glData1 > 0.5){
       cr.setWarnTag('${tagsStrList[0]}', data)
       flag = true;
      } else if (glData2 > 0.3) {
       cr.setWarnTag('${tagsStrList[1]}', data)
       flag = true;
      }
     return flag;
    }
  `;
    return fnTemp;
  };

  const handleChangeTag = (values) => {
    const fnTemp = createFnTemp(values);
    const filterTag: any[] = [];
    values.map((item) => {
      const findItem = selectNode.value.find((i) => item === i.value);
      if (findItem) {
        filterTag.push(findItem);
      }
    });
    selectNode.value = filterTag;
    // jsConvertEditorRef.value.handleSetValue(fnTemp);
    formState.script = fnTemp;
  };

  const tagSelect = (list: any[]) => {
    formState.associatedLabelList = list.map((item) => item.sourceUniqueKey);
    selectNode.value = list.map((item) => ({
      value: item.sourceUniqueKey,
      label: item.displayName,
    }));
    const fnTemp = createFnTemp(formState.associatedLabelList);
    // jsConvertEditorRef.value.handleSetValue(fnTemp);
    formState.script = fnTemp;
    formRef.value.validate(['associatedLabelList', 'script']);
  };

  const openTagModal = () => {
    const valuesList = selectNode.value.map((item) => {
      return {
        displayName: item.label,
        sourceUniqueKey: item.value,
      };
    });
    openTagsModal(true, {
      selectNode: valuesList,
    });
  };
</script>

<style scoped lang="less">
  .easy-rule,
  .custom-rule {
    :deep(.ant-form-item) {
      margin-bottom: 0;
    }
  }
</style>
