<template>
  <CockpitCardBox title="峰平谷用电">
    <template #header-right>
      <div class="right-box">
        <DatePicker
          :allow-clear="false"
          v-model:value="date"
          :disabledDate="disabledDate"
          dropdownClassName="aoa3-picker-dropdown"
        />
      </div>
    </template>
    <template #content>
      <div class="electricity-content">
        <div class="statistics-box">
          <div class="statistics-item" v-for="item in statisticsData" :key="item.indexCode">
            <div class="name">{{ item.name }}</div>
            <div class="value-unit">
              <div class="value">
                <Tooltip>
                  <template #title
                    >{{
                      item.value !== '' && item.value !== null
                        ? Number(item.value).toFixed(item.digit)
                        : '-'
                    }}{{ item.value !== '' && item.value !== null ? item.unit : '' }}</template
                  >
                  {{
                    item.value !== '' && item.value !== null
                      ? Number(item.value).toFixed(item.digit)
                      : '-'
                  }}
                </Tooltip>
              </div>
              <div class="unit">{{
                item.value !== '' && item.value !== null ? item.unit : ''
              }}</div>
            </div>
          </div>
        </div>
        <div class="chart-box">
          <div ref="chartRef" class="chart w-full h-full"></div>
        </div>
      </div>
    </template>
  </CockpitCardBox>
</template>

<script lang="ts" setup>
  import { ref, PropType, watch } from 'vue';
  import { groupBy, uniq } from 'lodash-es';
  import { Tooltip } from 'ant-design-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { DatePicker } from 'ant-design-vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { CockpitCardBox } from './CockpitCard';
  import type { AerationData, ChartData } from '../type';

  const emits = defineEmits(['changeDate']);

  const props = defineProps({
    statisticsData: {
      type: Array as PropType<AerationData[]>,
      default: () => [],
    },
    data: {
      type: Array as PropType<ChartData[]>,
      default: () => [],
    },
    first: Boolean,
  });

  const disabledDate = (current: Dayjs) => {
    return current && current > dayjs().endOf('day');
  };

  const chartRef = ref(null);
  const { setOptions } = useECharts(chartRef as any);
  const date = ref(dayjs().subtract(1, 'day'));

  const colorList = [
    ['rgba(11, 167, 137, 0.72)', 'rgba(117, 255, 229, 0.72)', 'rgba(151, 252, 233, 1)'],
    ['rgba(46, 128, 185, 0.80)', 'rgba(161, 212, 247, 0.80)', 'rgba(193, 229, 253, 1)'],
    ['rgba(97, 185, 197, 0.72)', 'rgba(213, 250, 255, 0.72)', 'rgba(204, 249, 255, 1)'],
    ['rgba(78, 94, 202, 0.72)', 'rgba(181, 191, 255, 0.72)', 'rgba(204, 211, 255, 1)'],
    ['rgba(64, 178, 88, 0.72)', 'rgba(157, 250, 176, 0.72)', 'rgba(178, 244, 192, 1)'],
  ];
  const lineColor = 'rgba(205, 255, 201, 1)';

  watch(
    () => date.value,
    () => {
      emits('changeDate', {
        startDate: dayjs(date.value).format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs(date.value).format('YYYY-MM-DD 23:59:59'),
      });
    },
  );

  const getXAxisData = (data: ChartData[]) => {
    if (data.length === 0) return [];
    return data[0].data?.map((item) => item.collectDateTime);
  };

  const getMax = (data: number[], index) => {
    const dataArr = data.flat().filter((item) => item !== null);
    return dataArr.length <= 0 ? (index === 0 ? 500 : 1) : Math.max(...dataArr);
  };

  const getMin = () => {
    // console.log(data);
    // const dataArr = data.flat().filter((item) => item !== null);
    // return Math.min(...dataArr);
    return 0;
  };

  const getValueFormatter = (value, digit = 2) => {
    return Number(value).toFixed(digit);
  };

  const getYAxisData = (data: ChartData[], key: string) => {
    const names = uniq(data.map((item) => item[key]));
    const group = groupBy(data, (item) => item.unit);
    const numberArr = Object.keys(group).map((key) => {
      const result = group[key].map((item) => {
        const number = item.data.map((i) => i.value);
        return number;
      });
      return result;
    });

    const result: any[] = names.map((item, index) => {
      let max = getMax(numberArr[index], index);
      let min = getMin();
      if (index === 0) {
        if (max === 0) {
          max = 500;
        } else {
          max = Math.ceil(max / 500) * 500 * 1.5;
        }
      } else {
        if (max === 0) {
          max = 1;
        } else {
          max = Math.ceil(max / 10);
        }
      }

      const tempData = {
        type: 'value',
        name: `${data[index]?.name}(${item})`,
        max,
        min,
        interval: (max - min) / 5,
        nameTextStyle: {
          fontSize: 13,
          color: '#fff',
          padding: [0, -20, -5, 0],
        },
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: 12,
          formatter: (value) => getValueFormatter(value, item === '元' ? 2 : 0),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(210, 255, 247, 0.40)',
            type: [5, 10],
            offset: 5,
          },
        },
      };
      return tempData;
    });
    return result;
  };

  const getSeriesData = (data: ChartData[]) => {
    const setiseData = data.map((item, index) => {
      return index % 2
        ? {
            name: item.name,
            type: 'line',
            symbol: 'none',
            step: 'middle',
            yAxisIndex: index,
            lineStyle: {
              color: lineColor,
            },
            itemStyle: {
              color: lineColor,
            },
            data: item.data?.map((item) => item.value),
          }
        : [
            {
              name: item.name,
              type: 'bar',
              barMaxWidth: 16,
              yAxisIndex: index,
              data: item.data?.map((i, idx) => ({
                value: i.value,
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: colorList[idx % 5][0],
                      },
                      {
                        offset: 0.5,
                        color: colorList[idx % 5][1],
                      },
                      {
                        offset: 1,
                        color: colorList[idx % 5][0],
                      },
                    ],
                  },
                },
              })),
            },
            {
              name: '',
              type: 'pictorialBar',
              legendHoverLink: false,
              symbolSize: ['100%', 4],
              symbolOffset: [0, -3],
              barMaxWidth: 16,
              symbolPosition: 'end',
              barGap: '-100%',
              z: 12,
              data: item.data?.map((i, idx) => ({
                value: i.value,
                symbolSize: i.value ? ['100%', 4] : [0, 0],
                itemStyle: {
                  color: colorList[idx % 5][2],
                },
              })),
              tooltip: {
                show: false,
              },
            },
          ];
    });
    return setiseData.flat();
  };

  const setChart = () => {
    const { data } = props;
    const options = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#E1F3F1',
        borderColor: '#02695E',
        appendToBody: true,
        formatter: (params) => {
          return `<span>${params[0].axisValue}</span></br>
          ${params
            .map((item, index) => {
              return `
            ${item.marker}&nbsp;<span style="display: inline-block; width: 50px";>${
                item.seriesName
              }</span>&nbsp;<span >${
                item.value !== '' && item.value !== null && item.value !== undefined
                  ? `<span style="font-weight: 600">${getValueFormatter(
                      item.value,
                      data[index].digit,
                    )} </span>&nbsp;<span style="font-weight: 600">${data[index]?.unit}</span>`
                  : '-'
              }</span>&nbsp;
            `;
            })
            .join('</br>')}
          `;
        },
        // valueFormatter: (value) => {
        //   return value !== '' && value !== null && value !== undefined
        //     ? `${getValueFormatter(value, data[0].digit)} ${data[0]?.unit}`
        //     : '-';
        // },
      },
      legend: {
        show: false,
      },
      grid: {
        left: 8,
        right: 8,
        bottom: 8,
        top: 30,
        containLabel: true,
        show: true,
        backgroundColor: 'rgba(2, 53, 32, 0.40)',
        borderWidth: 0,
      },
      xAxis: {
        type: 'category',
        data: getXAxisData(data),
        boundaryGap: true,
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: 13,
          padding: [0, 5, 0, 0],
          formatter: (value) => {
            return Number(dayjs(value).format('HH')) + '时';
          },
        },
        axisTick: {
          show: false,
        },
        axisPointer: {
          show: true,
          type: 'line',
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            type: [5, 10],
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            shadowColor: 'rgba(178, 255, 241, 1)',
            shadowOffsetY: -2,
            shadowOffsetX: 0,
            shadowBlur: 5,
            width: 1,
          },
        },
      },
      yAxis: getYAxisData(data, 'unit'),
      series: getSeriesData(data),
    };

    setOptions(options as any, false);
  };

  watch(
    () => props.first,
    () => {
      setChart();
    },
    { deep: true },
  );
</script>
<style lang="less" scoped>
  .right-box {
    padding-right: 8px;
    display: flex;
    align-items: center;

    :deep(.ant-picker) {
      background: rgba(12, 80, 68, 0.56);
      border: 1px solid rgba(31, 195, 164, 0.72);
      padding: 0 8px;
      width: 114px !important;

      .ant-picker-input {
        & > input {
          color: #fff;
          font-size: 13px;
          line-height: 28px;
        }
      }

      .ant-picker-suffix {
        color: #fff;
      }
    }
  }

  .electricity-content {
    height: 100%;
    min-height: 280px;
    display: flex;
    flex-direction: column;

    .statistics-box {
      padding: 8px;
      display: flex;
      gap: 8px;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;

      .statistics-item {
        padding: 6px 8px;
        width: calc(33.33% - 8px);
        height: 44px;
        background: rgba(2, 53, 32, 0.4);
        border-radius: 4px;
        border: 1px solid rgba(70, 226, 196, 0.4);
        overflow: hidden;

        &:nth-child(1) {
          .name {
            &:before {
              background: #0ba789;
            }
          }
        }

        &:nth-child(2) {
          .name {
            &:before {
              background: #2e80b9;
            }
          }
        }

        &:nth-child(3) {
          .name {
            &:before {
              background: #61b9c5;
            }
          }
        }

        &:nth-child(4) {
          .name {
            &:before {
              background: #4e5eca;
            }
          }
        }

        &:nth-child(5) {
          .name {
            &:before {
              background: #40b258;
            }
          }
        }

        &:nth-child(6) {
          .name {
            &:before {
              background: #cdffc9;
            }
          }
        }

        .name {
          position: relative;
          padding-left: 10px;
          font-size: 13px;
          line-height: 1;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;

          &:before {
            position: absolute;
            left: 0;
            top: 50%;
            content: '';
            width: 6px;
            height: 6px;
            background: #0ba789;
            border: 1px solid #ffffff;
            transform: translateY(-50%);
            border-radius: 50%;
          }
        }

        .value-unit {
          padding-top: 4px;
          display: flex;
          align-items: center;
          gap: 0 3px;

          .value {
            font-family: Alimama ShuHeiTi;
            font-weight: 500;
            font-size: 14px;
            line-height: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }

          .unit {
            font-size: 12px;
            line-height: 1;
          }
        }
      }
    }

    .chart-box {
      // height: calc(100% - 132px);
      flex: 1;
      width: 100%;
    }
  }

  @media screen and (min-width: 2000px) {
    .right-box {
      padding-right: 12px;

      :deep(.ant-picker) {
        padding: 0 12px;
        width: 170px !important;

        .ant-picker-input {
          & > input {
            font-size: 20px;
            line-height: 42px;
          }
        }
      }
    }

    .electricity-content {
      .statistics-box {
        padding: 12px;
        gap: 12px;

        .statistics-item {
          padding: 12px 8px;
          height: 76px;

          .name {
            padding-left: 20px;
            font-size: 20px;

            &:before {
              width: 10px;
              height: 10px;
            }
          }

          .value-unit {
            padding-top: 8px;
            gap: 0 4px;

            .value {
              font-size: 20px;
            }

            .unit {
              font-size: 18px;
            }
          }
        }
      }
    }
  }
</style>
