<template>
  <BiCardBox title="未端溶解氧">
    <template #title-slot>
      <AiCard
        :questionTemplate="question"
        questionTitle="未端溶解氧"
        type="thickness"
        :deepThinking="deepThinking"
      />
    </template>
    <template #content>
      <div class="oxygen-content" v-if="data.length">
        <div class="box-item" v-for="(item, index) in data" :key="item.indexCode">
          <div
            :class="[
              'item-value-unit',
              { type2: index > 2 },
              `level-${getIndexCodeLevel(item.indexCode, alarmData)}`,
            ]"
          >
            <div class="value" @click="openModal(item.indexCode, groupCode)">{{
              item.value !== '' && item.value !== null
                ? Number(item.value).toFixed(item.digit)
                : '-'
            }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
          <div class="name">{{ item.name }}</div>
        </div>
        <div class="box-center">
          <div class="name">生化池末端DO</div>
        </div>
      </div>
      <div class="empty-container w-full h-full" v-else>
        <Empty />
      </div>
    </template>
  </BiCardBox>
  <IndicatorModal
    v-model:open="indicatorOpen"
    :width="isIPCRef ? '100%' : '1272px'"
    :bodyStyle="modalBodyStyle"
    :destroyOnClose="true"
    :groupInfo="groupInfo"
    :themeColor="themeColor"
    :echartsConfig="echartsConfig"
    :multiple="multiple"
    :factoryId="factoryId"
    title="指标详情"
    :requestHeader="requestHeader"
    :footer="modalFooter"
    :base-url="baseUrl"
    wrapClassName="aoa-ipc-modal-curve"
  />
</template>

<script lang="ts" setup>
  import { ref, PropType, computed } from 'vue';
  import { BiCardBox, Empty } from './BiCard';
  import type { DataList, AlarmData } from '../type';
  import { IndicatorModal } from 'hlxb-business-ui';
  import { getProcessEditorTheme } from '/@process-editor/assets/theme';
  import { isIPCBreakPoint } from '/@/hooks/event/useBreakpoint';
  import { useWindowSize } from '@vueuse/core';
  import { getToken } from '/@/utils/auth';
  import { useDomain } from '/@/locales/useDomain';
  import { getFactoryId } from '/@aoa/utils/factory';
  import { getAppEnvConfig } from '/@/utils/env';
  import { AiCard } from '/@aoa/components/AiCard';
  import { getIndexCodeLevel } from '/@aoa/utils';

  const props = defineProps({
    data: {
      type: Array as PropType<DataList[]>,
      default: () => [],
    },
    alarmData: {
      type: Array as PropType<AlarmData[]>,
      default: () => [],
    },
    groupCode: String,
    deepThinking: {
      type: Boolean,
      default: false,
    },
    aiQuestionTemplate: {
      type: String,
      default: '',
    },
  });

  const { themeColor, echartsConfig } = getProcessEditorTheme();
  const factoryId = getFactoryId();
  const { isIPCRef } = isIPCBreakPoint();
  const { height } = useWindowSize();
  const modalBodyStyle = computed(() => {
    return isIPCRef.value
      ? {
          height: `${height.value - 64}px`,
        }
      : { height: '716px' };
  });
  const modalFooter = computed(() => {
    return isIPCRef.value ? null : ' ';
  });

  const baseUrl = computed(() => getAppEnvConfig().VITE_GLOB_API_URL);

  const { getTenantId } = useDomain();
  const requestHeader: any = {
    Authorization: getToken(),
    'Tenant-Id': getTenantId.value,
  };

  const indicatorOpen = ref(false);
  const groupInfo = ref({
    groupCode: '',
    resourceInterfaceId: '3',
    jsConvert: false,
    indexCodes: '',
  });
  const multiple = ref(false);

  const openModal = (code, groupCode) => {
    groupInfo.value.indexCodes = code;
    groupInfo.value.groupCode = groupCode;
    indicatorOpen.value = true;
  };

  const getQuestion = (data) => {
    if (!props.aiQuestionTemplate || !props.aiQuestionTemplate.includes('${data}')) {
      return props.aiQuestionTemplate;
    }
    const str = data
      .map((item) => {
        return `${item.name}${
          item.value !== null && item.value !== undefined
            ? Number(item.value).toFixed(item.digit ?? 2)
            : '-'
        }${item.unit}`;
      })
      .join('、');
    return props.aiQuestionTemplate.replace('${data}', str);
  };

  const question = computed(() => {
    const data = props.data;
    return getQuestion(data);
  });
</script>
<style lang="less" scoped>
  .oxygen-content {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 298px;

    .box-center {
      position: absolute;
      width: 177px;
      height: 99px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-image: url('/@aoa/views/BI/assets/images/icon_3.png');
      background-size: cover;

      .name {
        position: absolute;
        top: 50px;
        width: 100%;
        text-align: center;
        font-weight: 600;
        font-size: 14px;
        line-height: 1;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), -1px -1px 2px rgba(255, 255, 255, 0.4);
      }
    }

    .box-item {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      top: 50%;

      .name {
        padding-top: 5px;
        font-size: 13px;
        line-height: 1;
      }

      .item-value-unit {
        width: 60px;
        height: 60px;
        background-size: cover;
        text-align: center;
        background-image: url('/@aoa/views/BI/assets/images/icon_1.png');
        display: flex;
        flex-direction: column;
        justify-content: center;

        &.level {
          &-0 {
            background-image: url('/@aoa/views/BI/assets/images/icon_1.png');
          }

          &-1 {
            background-image: url('/@aoa/views/BI/assets/images/icon_1_danger.png');
          }

          &-2 {
            background-image: url('/@aoa/views/BI/assets/images/icon_1_danger.png');
          }

          &-3 {
            background-image: url('/@aoa/views/BI/assets/images/icon_1_danger.png');
          }
        }

        &.type2 {
          background-image: url('/@aoa/views/BI/assets/images/icon_2.png');

          &.level {
            &-0 {
              background-image: url('/@aoa/views/BI/assets/images/icon_2.png');
            }

            &-1 {
              background-image: url('/@aoa/views/BI/assets/images/icon_1_danger.png');
            }

            &-2 {
              background-image: url('/@aoa/views/BI/assets/images/icon_1_danger.png');
            }

            &-3 {
              background-image: url('/@aoa/views/BI/assets/images/icon_1_danger.png');
            }
          }
        }

        .value {
          font-family: Alimama ShuHeiTi;
          font-weight: 500;
          font-size: 14px;
          line-height: 1;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), -1px -1px 2px rgba(255, 255, 255, 0.4);
          cursor: pointer;
        }

        .unit {
          font-weight: 400;
          font-size: 12px;
          line-height: 1;
        }
      }

      &:nth-child(1) {
        left: 8px;
        margin-top: calc((151px - 176px - 79px));
      }

      &:nth-child(2) {
        left: 96px;
        margin-top: calc((151px - 210px - 79px));
      }

      &:nth-child(3) {
        left: 188px;
        margin-top: calc((151px - 192px - 79px));
      }

      &:nth-child(4) {
        left: 8px;
        margin-top: calc((186px - 151px));
      }

      &:nth-child(5) {
        left: 96px;
        margin-top: calc((206px - 151px));
      }

      &:nth-child(6) {
        left: 188px;
        margin-top: calc((173px - 151px));
      }
    }
  }
  @media screen and (min-width: 1800px) {
    .oxygen-content {
      .box-center {
        .name {
          top: 55px;
          font-size: 16px;
        }
      }

      .box-item {
        .name {
          font-size: 14px;
        }

        .item-value-unit {
          .value {
            font-size: 15px;
          }

          .unit {
            font-size: 13px;
          }
        }

        &:nth-child(1) {
          left: 17px;
        }

        &:nth-child(2) {
          left: 130px;
        }

        &:nth-child(3) {
          left: 250px;
        }

        &:nth-child(4) {
          left: 23px;
        }

        &:nth-child(5) {
          left: 140px;
        }

        &:nth-child(6) {
          left: 260px;
        }
      }
    }
  }

  @media screen and (min-width: 2000px) {
    .oxygen-content {
      .box-center {
        position: absolute;
        .width-prop(249);
        .height-prop(152);

        .name {
          .height-prop(85, top);
          .font-size(16);
        }
      }

      .box-item {
        .name {
          .height-prop(6, padding-top);
          .font-size(14);
        }

        .item-value-unit {
          .width-prop(68);
          .height-prop(68);

          .value {
            .font-size(18);
          }

          .unit {
            font-weight: 400;
            .font-size(13);
            line-height: 1;
          }
        }

        &:nth-child(1) {
          .height-prop(-114, margin-top);
        }

        &:nth-child(2) {
          .width-prop(144, left);
          .height-prop(-148, margin-top);
        }

        &:nth-child(3) {
          .width-prop(260, left);
          .height-prop(-140, margin-top);
        }

        &:nth-child(4) {
          .height-prop(50, margin-top);
        }

        &:nth-child(5) {
          .width-prop(144, left);
          .height-prop(55, margin-top);
        }

        &:nth-child(6) {
          .width-prop(250, left);
          .height-prop(30, margin-top);
        }
      }
    }
  }
</style>
