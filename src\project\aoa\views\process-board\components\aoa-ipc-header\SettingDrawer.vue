<template>
  <div class="setting">
    <Dropdown
      placement="bottomLeft"
      overlayClassName="aoa-ipc-dropdown-overlay"
      :trigger="['click']"
      :getPopupContainer="(target) => target.parentNode"
    >
      <div :class="prefixCls" @click="visible = true"></div>
      <template #overlay>
        <Menu @click="handleMenuClick">
          <!-- <MenuItem
            key="notify"
            :text="t('layout.header.dropdownItemNotifyCenter')"
            icon="icon-park-outline:remind"
          /> -->
          <MenuItem
            key="personal"
            :text="t('layout.header.dropdownItemPersonalCenter')"
            icon="icon-park-outline:people"
          />
          <MenuItem
            key="logout"
            :text="t('layout.header.dropdownItemLoginOut')"
            icon="icon-park-outline:power"
          />
        </Menu>
      </template>
    </Dropdown>
  </div>
  <Personal @register="registerPersonal" />
  <MsgDrawer @register="registerDrawer" @update="getCount" :count="count" />
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { Dropdown, Menu } from 'ant-design-vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import Personal from '/@/views/system/personalCenter/index.vue';
  import MsgDrawer from '/@/layouts/default/header/components/notify/MsgDrawer.vue';
  import type { MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useUserStore } from '/@/store/modules/user';
  import { useDrawer } from '/@/components/Drawer';
  import { useIntervalFn } from '@vueuse/core';
  import aoaIpcAvator from '/@aoa/assets/images/avator.png';
  import userAvatar from '/@aoa/assets/images/user-avatar.png';
  import { sleep } from '/@/utils';

  type MenuEvent = 'logout' | 'personal' | 'notify';

  const MenuItem = createAsyncComponent(
    () => import('/@/layouts/default/header/components/user-dropdown/DropMenuItem.vue'),
  );
  const defaultAvator = ref('');
  const ONE_SECONDS = 1000;
  const step = 5;
  const visible = ref(false);
  const { t } = useI18n();
  const userStore = useUserStore();
  const { prefixCls } = useDesign('aoa-ipc-setting');
  const { prefixCls: prefixClsLayoutConfirm } = useDesign('aoa-ipc-setting-layout-confirm');
  const { prefixCls: prefixClsPersonalDrawer } = useDesign('aoa-ipc-setting-personal-drawer');
  const { prefixCls: prefixClsNotifuDrawer } = useDesign('aoa-ipc-setting-notify-drawer');
  const [registerPersonal, { openDrawer, setDrawerProps }] = useDrawer();
  const [registerDrawer, { openDrawer: openDrawerNotify, setDrawerProps: setDrawerNotifyProps }] =
    useDrawer();

  //
  const handleMenuClick = async (e: MenuInfo) => {
    switch (e.key as MenuEvent) {
      case 'logout':
        handleLoginOut();
        break;
      case 'personal':
        setDrawerProps({ rootClassName: prefixClsPersonalDrawer, width: '100%' });
        openDrawer(true);
        await sleep(200);
        defaultAvator.value = document
          .querySelector(
            `.${prefixClsPersonalDrawer} .personal-content .avatar-box .ant-upload img`,
          )
          ?.getAttribute('src');
        if (defaultAvator.value && defaultAvator.value == userAvatar) {
          document
            .querySelector(
              `.${prefixClsPersonalDrawer} .personal-content .avatar-box .ant-upload img`,
            )
            ?.setAttribute('src', aoaIpcAvator);
        }
        break;
      case 'notify':
        setDrawerNotifyProps({
          rootClassName: prefixClsNotifuDrawer,
          mask: false,
          width: '48.8%',
          contentWrapperStyle: { minWidth: '435px' },
        });
        openDrawerNotify();
        break;
    }
  };
  const maskStyle = { 'background-color': 'rgba(0, 0, 0, 0.7)' };
  const handleLoginOut = () => {
    userStore.confirmLoginOut(prefixClsLayoutConfirm, maskStyle);
  };

  const getCount = async () => {
    await userStore.resetProjectMsgCount();
  };

  const count = computed(() => userStore.getMsgCount);
  useIntervalFn(getCount, step * ONE_SECONDS);

  getCount();
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-aoa-ipc-setting';

  .@{prefix-cls} {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    background: url('/@aoa/assets/images/setting.png');
    background-size: 100% 100%;
  }

  .setting {
    position: absolute;
    right: 24px;
    top: 24px;
    width: 40px;
    height: 40px;

    :deep(.aoa-ipc-dropdown-overlay) {
      .ant-dropdown-menu {
        width: 160px;
        background: #e1f3f1;
        box-shadow: 0px 1px 8px 0px #577572;
        border-radius: 4px;
      }

      .menu-item {
        // border-color: rgba(255, 255, 255, 0.1);

        .ant-dropdown-menu-item {
          height: 48px;
          border-radius: 4px;
          // color: #fff;

          .app-iconify {
            color: #333 !important;
          }
        }
      }
    }
  }
</style>

<style lang="less">
  @import '/@aoa/assets/css/font.less';

    @prefix-cls-layout-confirm: ~'@{namespace}-aoa-ipc-setting-layout-confirm';
    @prefix-cls-personal-drawer: ~'@{namespace}-aoa-ipc-setting-personal-drawer';
    @prefix-cls-notify-drawer: ~'@{namespace}-aoa-ipc-setting-notify-drawer';

    @crrentBorderColor: rgba(255, 255, 255, 0.1);
    @crrentColor: rgba(255, 255, 255, 0.6);

    // .ant-modal-wrap{
    //   &.@{prefix-cls-layout-confirm}{

    //   }
    // }

    .ant-drawer{
      &.@{prefix-cls-personal-drawer}{
        .ant-drawer-content{
          background: #e1f3f1;
        }

        .ant-drawer-header{
          padding: 24px 24px 16px;
          height: 80px;

          .ant-drawer-header-title{
            position: relative;

            .ant-drawer-close{
              position: absolute;
              left: 0;
              top: 50%;
              transform: translate(0, -50%);
              width: 40px;
              height: 40px;
              border-radius: 50%;
              cursor: pointer;
              margin: 0;
              background: url('/@aoa/assets/images/back.png');
              background-size: cover;

              & > div{
                display: none;
              }
            }

            .ant-drawer-title{
              padding-left: 52px;

              // display: none;
              .vben-basic-title{
                font-weight: 700;
                font-size: 20px;
                font-family: Alimama ShuHeiTi;
              }
            }
          }
        }

        .ant-drawer-body{
          background: #e1f3f1;
          height: calc(100% - 80px);

          .scrollbar__wrap{
            padding: 12px 0;
          }

          .personal-content{
            background: #e1f3f1 !important;
            padding: 40px 10%;

            .avatar-box{
              .upload-list-inline1{
                // background: #9ECDFF;
                border-radius: 4px;
              }

              .upload-btn-tips{
                margin-top: 0;

                .avatar-info{
                  h1 {
                    margin: 0;
                    // color: #fff;
                  }

                  span{
                    color: #333;
                  }
                }
              }

            }

            .ant-form{

              .ant-input-affix-wrapper{

                .ant-input{

                  &:-webkit-autofill{
                    box-shadow: none;
                  }

                }

              }

              .ant-radio-wrapper-checked{
                &.ant-radio-wrapper-disabled {
                  // color: #fff;

                  .ant-radio-inner{
                    border-color: var(--theme-color);
                    border-width: 8px;

                    &:after{
                      background-color: #fff;
                    }
                  }
                }
              }
            }
          }
        }
      }
      &.@{prefix-cls-notify-drawer}{
        .ant-drawer-content{
          background: #e1f3f1;
        }

        .full-loading{
          background-color: transparent;
        }

        .ant-drawer-header{
          background: #e1f3f1;

          .ant-drawer-header-title{
            .vben-basic-title{
              color: #fff;
            }

            .vben-icon-button{
              &:hover{
                background-color: transparent;
              }

              & > span{
                color: @crrentColor !important;
              }
            }
          }
        }

        .ant-drawer-body{
          background: #e1f3f1;

          .drawer-cont{
            background: #e1f3f1;
            border-color: rgba(255,255,255,0.2);

            .msg-title-cont{
              .ant-radio-group{
                border-radius: 2px;
                overflow: hidden;

                .ant-radio-button-wrapper{
                  background: @crrentBorderColor;
                  color: #fff;

                  &.ant-radio-button-wrapper-checked{
                    background: var(--theme-color);
                  }
                }
              }

              .ant-select{
                .ant-select-selector{
                  background-color: transparent;
                  border-radius: 2px;
                  border: 1px solid rgba(255,255,255,0.2);
                }

                .ant-select-selection-item{
                  color: #fff;
                }

                .ant-select-arrow{
                  color: rgba(255,255,255,.2);
                }

                .ant-tree-select-dropdown{
                  background-color: #e1f3f1;

                  .ant-empty-image{
                   opacity: .6;
                  }

                  .ant-empty-description{
                    color: @crrentColor;
                  }

                  .ant-select-tree{
                    background-color: #e1f3f1;
                    color: #fff;

                    .ant-select-tree-node-selected{
                      background-color: @crrentBorderColor;
                    }
                  }
                }
              }

              .read-icon{
                color: #9ECDFF;

                &.disabled{
                  color: #9ECDFF;
                }
              }
            }

            .ant-radio-button-wrapper{
              &:first-child, &:last-child{
                border-radius: 0;
              }
            }

            .drawer-container {
              .no-data{
                height: 141px;
                background: url('/@aoa/assets/images/no-data.png');
                background-position: center top;
                background-repeat: no-repeat;
                background-size: 120px 120px;

                img{
                  display: none;
                }

                p{
                  padding-top: 120px;
                  color: rgba(255, 255, 255, 0.6);
                }
              }

              .list-cont{
                &:after{
                  background-color: @crrentBorderColor;
                }

                &:hover{
                  background-color: @crrentBorderColor;
                }

                .item-title{
                  color: @crrentColor;
                }

                .msg-cont{
                  .app-iconify{
                    color: #9ECDFF !important;
                  }

                  .msg-text{
                    color: #fff;
                  }

                  .msg-flag{
                    color: #9ECDFF;
                  }
                }
              }
            }

            .drawer-footer-cont{
              border-top: 1px solid @crrentBorderColor;

              .ant-pagination{
                color: #fff;

                .ant-pagination-prev, .ant-pagination-next{
                  border: 1px solid @crrentBorderColor !important;

                  &:hover{
                    background-color: @crrentBorderColor !important;
                  }
                }

                .ant-pagination-simple-pager{
                  input{
                    background-color: transparent;
                    border: 1px solid @crrentBorderColor;
                  }
                }
              }

              .ant-select {
                .ant-select-selector{
                  background-color: transparent;
                  border: 1px solid @crrentBorderColor;

                  .ant-select-selection-item{
                    color: #fff;
                  }
                }

                .ant-select-arrow{
                  color: #fff;
                }
              }

              .drawer-footer-size{
                .ant-select-dropdown{
                  background-color: #e1f3f1;

                  .ant-select-item-option{
                    color: #fff;
                  }
                }
              }
            }
          }
        }
      }
    }
</style>
