<template>
  <BiCardBox title="实时报警" direction="right">
    <template #title-slot>
      <div class="router-btn" v-if="alarmPath" @click="toAlarm(alarmPath)">
        <Icon icon="icon-park-outline:right" :size="18" />
      </div>
    </template>
    <template #content>
      <div class="alarm-content">
        <div class="alarm-header">
          <div class="header-item header-item-name">主题</div>
          <div class="header-item header-item-value">反馈值/限值</div>
          <div class="header-item header-item-time">报警时间</div>
        </div>
        <div class="alarm-list" v-if="data.length && data.length <= 4">
          <div class="scroll-list">
            <div class="item" v-for="item in data" :key="item.id">
              <div class="item-name">
                <div class="icon">
                  <img :src="alarmTypeMap[item.warnEventLevel]" />
                </div>
                <div class="name">
                  <Tooltip>
                    <template #title>{{ item.title }}</template>
                    {{ item.title }}
                  </Tooltip>
                </div>
              </div>
              <div class="item-value">
                <Tooltip>
                  <template #title
                    >{{ Number(item.limitValue).toFixed(2) }}/{{
                      Number(item.warnValue).toFixed(2)
                    }}</template
                  >
                  {{ Number(item.limitValue).toFixed(2) }}/{{ Number(item.warnValue).toFixed(2) }}
                </Tooltip>
              </div>
              <div class="item-time">
                <Tooltip>
                  <template #title>{{ formatterTime(item.creationTime) }}</template>
                  {{ formatterTime(item.creationTime) }}
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="alarm-list swiper-list" v-else-if="data.length && data.length > 4">
          <Swiper
            class="swiper"
            :modules="modules"
            slidesPerView="auto"
            freeMode
            :autoplay="{ disableOnInteraction: false, delay: 5000 }"
            loop
            noSwiping
            direction="vertical"
            @swiper="setSwiper"
            @mouseenter="onSwiperMouseenter"
            @mouseleave="onSwiperMouseleave"
          >
            <SwiperSlide
              v-for="(item, index) in data"
              :key="index"
              noSwipingClass="swiper-no-swiping"
              :class="`level-${[item.warnEventLevel]}`"
            >
              <div class="item-name">
                <div class="icon">
                  <img :src="alarmTypeMap[item.warnEventLevel]" />
                </div>
                <div class="name">
                  <Tooltip>
                    <template #title>{{ item.title }}</template>
                    {{ item.title }}
                  </Tooltip>
                </div>
              </div>
              <div class="item-value">
                <Tooltip>
                  <template #title
                    >{{ Number(item.limitValue).toFixed(2) }}/{{
                      Number(item.warnValue).toFixed(2)
                    }}</template
                  >
                  {{ Number(item.limitValue).toFixed(2) }}/{{ Number(item.warnValue).toFixed(2) }}
                </Tooltip>
              </div>
              <div class="item-time">
                <Tooltip>
                  <template #title>{{ formatterTime(item.creationTime) }}</template>
                  {{ formatterTime(item.creationTime) }}
                </Tooltip>
              </div>
            </SwiperSlide>
          </Swiper>
        </div>
        <div class="empty-container" v-else>
          <Empty />
        </div>
      </div>
    </template>
  </BiCardBox>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { flattenTree } from '/@/utils';
  import { useRouter } from 'vue-router';
  import { BiCardBox, Empty } from './BiCard';
  import { Tooltip } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import type { AlarmData } from '../type';
  import { usePermissionStore } from '/@/store/modules/permission';
  // import dayjs from 'dayjs';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Autoplay } from 'swiper/modules';
  import 'swiper/css';
  import icon1 from '/@aoa/views/BI/assets/images/alarm_1.png';
  import icon2 from '/@aoa/views/BI/assets/images/alarm_2.png';
  import icon3 from '/@aoa/views/BI/assets/images/alarm_3.png';

  defineProps({
    data: {
      type: Array as PropType<AlarmData[]>,
      default: () => [],
    },
  });

  const modules = [Autoplay];

  const swiperInstance = ref();

  const setSwiper = (swiper) => {
    swiperInstance.value = swiper;
  };

  const onSwiperMouseenter = () => {
    swiperInstance.value.autoplay.stop();
  };

  const onSwiperMouseleave = () => {
    swiperInstance.value.autoplay.start();
  };

  const alarmTypeMap = {
    1: icon1,
    2: icon2,
    3: icon3,
  };

  const formatterTime = (time: string) => {
    if (!time) return '-';
    // return dayjs(time).format('YYYY-MM-DD');
    return time;
  };

  const permissionStore = usePermissionStore();
  const router = useRouter();

  const alarmPath = computed(() => {
    const moduleId = permissionStore.getFirstMenuParams?.moduleId;
    const currentMenu = permissionStore.getFirstMenuList.find((item) => item.moduleId === moduleId);
    if (!currentMenu) return '';
    const menuList = flattenTree(currentMenu?.children);
    const path = menuList.find((item) => item.realPath === 'AlarmManagementAlarmRecord')?.path;
    return path;
  });

  const toAlarm = (path) => {
    router.push(path);
  };
</script>
<style lang="less" scoped>
  .router-btn {
    cursor: pointer;
    width: 18px;
    height: 18px;

    @media screen and (min-width: 2000px) {
      width: 27px;
      height: 27px;

      :deep(.app-iconify) {
        transform: scale(1.5) !important;
        transform-origin: left center;
      }
    }
    @media screen and (min-width: 3000px) {
      width: 36px;
      height: 36px;

      :deep(.app-iconify) {
        transform: scale(2) !important;
        transform-origin: left center;
      }
    }
  }

  .alarm-content {
    height: 100%;
    padding-top: 8px;

    .alarm-header {
      width: calc(100% - 16px);
      margin-left: 8px;
      height: 40px;
      box-shadow: inset 1px -1px 4px 0px rgba(2, 23, 19, 1),
        inset -1px 1px 4px 0px rgba(223, 255, 249, 1);
      background: rgba(43, 150, 130, 1);
      border-radius: 4px;
      display: flex;
      align-items: center;
      padding: 0 10px;

      .header-item {
        font-size: 14px;
        line-height: 1;

        &-name {
          width: 35%;
        }

        &-value {
          width: 34%;
        }

        &-time {
          width: 31%;
        }
      }
    }

    .empty-container {
      height: calc(100% - 48px);
      overflow-y: overlay;
    }

    .alarm-list {
      margin-top: 8px;
      padding: 0 8px;
      height: calc(100% - 48px);
      overflow-y: overlay;
      overflow-x: hidden;

      .item {
        display: flex;
        align-items: center;
        height: 40px;
        box-shadow: inset 1px 2px 4px 0px rgba(0, 9, 7, 1),
          inset 0px -1px 4px 0px rgba(210, 255, 246, 1);
        background: rgba(33, 121, 105, 0.32);
        border-radius: 4px;
        padding: 0 8px;
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 1;

        &-name {
          width: 35%;
          display: flex;
          align-items: center;

          .icon {
            width: 18px;
            height: 18px;

            img {
              width: 100%;
            }
          }

          .name {
            width: calc(100% - 22px);
            padding: 0 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        &-value {
          width: 34%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &-time {
          width: 31%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      &.swiper-list {
        overflow-y: hidden;

        :deep(.swiper) {
          height: 100%;

          .swiper-slide {
            display: flex;
            align-items: center;
            height: 40px !important;
            box-shadow: inset 1px 2px 4px 0px rgba(0, 9, 7, 1),
              inset 0px -1px 4px 0px rgba(210, 255, 246, 1);
            background: rgba(33, 121, 105, 0.32);
            border-radius: 4px;
            padding: 0 8px;
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 1;
            backdrop-filter: blur(4px);

            &.level {
              &-1 {
                box-shadow: inset 1px 2px 4px 0px rgba(50, 4, 4, 1),
                  inset 0px -1px 4px 0px rgba(255, 90, 90, 1);
                background: rgba(255, 0, 0, 0.44);
              }

              &-2 {
                box-shadow: inset 1px 2px 4px 0px rgba(59, 36, 1, 1),
                  inset 0px -1px 4px 0px rgba(255, 198, 137, 1);
                background: rgba(232, 91, 0, 0.32);
              }

              &-3 {
                box-shadow: inset 1px 2px 4px 0px rgba(67, 66, 0, 1),
                  inset 0px -1px 4px 0px rgba(252, 236, 134, 1);
                background: rgba(205, 186, 15, 0.32);
              }
            }
          }
        }
      }
    }
  }

  @media screen and (min-width: 1800px) {
    .alarm-content {
      .alarm-header {
        width: calc(100% - 24px);
        margin-left: 12px;

        .header-item {
          &-name {
            width: 39%;
          }

          &-time {
            width: 27%;
          }
        }
      }

      .alarm-list {
        padding: 0 12px;

        .item {
          &-name {
            width: 39%;
          }

          &-time {
            width: 27%;
          }
        }
      }
    }
  }

  @media screen and (min-width: 2000px) {
    .alarm-content {
      .px2vw(12);
      .px2vh(12);
      padding: @vw;

      .alarm-header {
        .height-prop(44);
        .px2vw(14);
        padding: 0 @vw;

        .header-item {
          .font-size(16);

          &-name {
            width: 35%;
          }

          &-value {
            width: 36%;
          }

          &-time {
            width: 29%;
          }
        }
      }

      .empty-container {
        .px2vh(68);
        height: calc(100% - @vh);
      }

      .alarm-list {
        .px2vh(68);
        .height-prop(10, margin-top);
        height: calc(100% - @vh);

        .item {
          .height-prop(44);
          .height-prop(8,margin-bottom);
          .font-size(16);

          &-name {
            width: 35%;

            .icon {
              .px2vw(22);
              .width-prop(22);
              height: @vw;
            }

            .name {
              .px2vw(22);
              width: calc(100% - @vw);
              padding: 0 4px;
            }
          }

          &-value {
            width: 36%;
          }

          &-time {
            width: 29%;
          }
        }

        &.swiper-list {
          :deep(.swiper) {
            .swiper-slide {
              .height-prop(44) !important;
              .height-prop(8,margin-bottom);
              .font-size(16);
            }
          }
        }
      }
    }
  }
</style>
