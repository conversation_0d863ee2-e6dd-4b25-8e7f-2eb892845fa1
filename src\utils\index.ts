import type { RouteLocationNormalized, RouteRecordNormalized } from 'vue-router';
import type { App, Component } from 'vue';
import { getCurrentInstance, nextTick } from 'vue';
import QueryString from 'qs';

import { unref } from 'vue';
import { isObject, isString } from '/@/utils/is';
import { cloneDeep, flatMap, omit } from 'lodash-es';

export const noop = () => {};

/**
 * @description:  Set ui mount node
 */
export function getPopupContainer(node?: HTMLElement): HTMLElement {
  return (node?.parentNode as HTMLElement) ?? document.body;
}

/**
 * Add the object as a parameter to the URL
 * @param baseUrl url
 * @param obj
 * @returns {string}
 * eg:
 *  let obj = {a: '3', b: '4'}
 *  setObjToUrlParams('www.baidu.com', obj)
 *  ==>www.baidu.com?a=3&b=4
 */
export function setObjToUrlParams(baseUrl: string, obj: any): string {
  let parameters = '';
  for (const key in obj) {
    parameters += key + '=' + encodeURIComponent(obj[key]) + '&';
  }
  parameters = parameters.replace(/&$/, '');
  return /\?$/.test(baseUrl) ? baseUrl + parameters : baseUrl.replace(/\/?$/, '?') + parameters;
}

// 深度合并
export function deepMerge<T = any>(src: any = {}, target: any = {}): T {
  let key: string;
  const res: any = cloneDeep(src);
  for (key in target) {
    res[key] = isObject(res[key]) ? deepMerge(res[key], target[key]) : (res[key] = target[key]);
  }
  return res;
}

export function openWindow(
  url: string,
  opt?: {
    target?: TargetContext | string;
    noopener?: boolean;
    noreferrer?: boolean;
    param?: Indexable;
  },
) {
  const { target = '__blank', noopener = true, noreferrer = true, param } = opt || {};
  const feature: string[] = [];

  noopener && feature.push('noopener=yes');
  noreferrer && feature.push('noreferrer=yes');
  let path = url;
  if (param) {
    const source = url.split('?');
    const obj = QueryString.parse(source[1]) || {};
    const params = Object.assign(obj, param);
    path = source[0] + QueryString.stringify(params, { addQueryPrefix: true });
  }
  window.open(path, target, feature.join(','));
}

// dynamic use hook props
export function getDynamicProps<T extends Record<string, unknown>, U>(props: T): Partial<U> {
  const ret: Recordable = {};

  Object.keys(props).map((key) => {
    ret[key] = unref((props as Recordable)[key]);
  });

  return ret as Partial<U>;
}

export function getRawRoute(route: RouteLocationNormalized): RouteLocationNormalized {
  if (!route) return route;
  const { matched, ...opt } = route;
  return {
    ...opt,
    matched: (matched
      ? matched.map((item) => ({
          meta: item.meta,
          name: item.name,
          path: item.path,
        }))
      : undefined) as RouteRecordNormalized[],
  };
}

// https://github.com/vant-ui/vant/issues/8302
type EventShim = {
  new (...args: any[]): {
    $props: {
      onClick?: (...args: any[]) => void;
    };
  };
};

export type WithInstall<T> = T & {
  install(app: App): void;
} & EventShim;

export type CustomComponent = Component & { displayName?: string };

export const withInstall = <T extends CustomComponent>(component: T, alias?: string) => {
  (component as Record<string, unknown>).install = (app: App) => {
    const compName = component.name || component.displayName;
    if (!compName) return;
    app.component(compName, component);
    if (alias) {
      app.config.globalProperties[alias] = component;
    }
  };
  return component as WithInstall<T>;
};

export const setComponentName = {
  install(app, _) {
    function setComponentName(name) {
      const instance = getCurrentInstance();
      if (!instance) return;
      //@ts-ignore
      instance.type.name = name;
      instance.type.__name = name;
    }
    app.provide('setComponentName', setComponentName);
  },
};

export function parseJson(str) {
  try {
    if (isObject(str)) return str;
    return JSON.parse(str);
  } catch (e) {
    return {};
  }
}

export function setTableEmptyHeight(res) {
  // 调整空数据时表格的高度
  const tableBody = document
    .querySelector('.vben-layout-content')
    ?.querySelector('.ant-table-body') as HTMLAreaElement;
  const table = tableBody.querySelector('table') as unknown as HTMLAreaElement;
  if (!res.length) {
    setTimeout(() => {
      const newHeight = `${parseInt(tableBody.style.maxHeight) + 35}px`;
      tableBody.style.height = newHeight;
      tableBody.style.maxHeight = newHeight;
      table.style.height = '100%';
    }, 150);
  } else {
    table.style.height = 'auto';
  }
}

export function isCSSUnit(str: string) {
  return ['%', 'px', 'rem', 'em', 'vh', 'vw'].some((unit) => str.endsWith(unit));
}

export function getCSSValue(str?: string, def: string | number = '') {
  if (!str) {
    return def;
  }

  if (isCSSUnit('' + str)) {
    return str;
  }

  return parseInt(str) + 'px';
}

export function mergeFunctions(...funcs: Fn[]) {
  return async function (...args) {
    let ret;
    for (const func of funcs) {
      ret = await func.apply(window, args);
    }
    return ret;
  };
}

export const sleep = (ms = 200) => new Promise((r) => setTimeout(r, ms));

// 下划线转换成驼峰
export function toHump(name: string) {
  if (!isString(name)) return name;

  return name.replace(/\_(\w)/g, function (_, letter) {
    return letter.toUpperCase();
  });
}

// 驼峰转换下划线
export function toLine(name: string) {
  if (!isString(name)) return name;
  const str = name.replace(/([A-Z])/g, '_$1').toLowerCase();
  if (str.slice(0, 1) === '_') {
    //如果首字母是大写，去掉第一个_
    return str.slice(1);
  }
  return str;
}
/**
 * Checks if any ancestor component, up to the root, contains any of the specified classes.
 * @param {Array<string>} classNames - An array of class names to search for.
 * @returns {Promise<boolean>} A promise that resolves to true if any of the classes are found, otherwise false.
 */
export async function hasAnyAncestorClass(classNames: Array<string>): Promise<boolean> {
  return new Promise((resolve) => {
    nextTick(() => {
      let instance = getCurrentInstance();
      while (instance) {
        const hasClass = classNames.some((className) =>
          instance?.vnode.el?.classList.contains(className),
        );
        if (hasClass) {
          resolve(true);
          return;
        }
        instance = instance.parent;
      }
      resolve(false);
    });
  });
}

export const flattenTree = (tree) => {
  if (!tree || !tree.length) [];
  return flatMap(tree, (node) => {
    if (node.children) {
      return [omit(node, 'children'), ...flattenTree(node.children)];
    }
    return [node];
  });
};
