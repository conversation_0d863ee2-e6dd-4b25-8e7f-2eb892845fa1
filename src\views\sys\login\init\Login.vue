<template>
  <div
    :class="[
      prefixCls,
      'relative w-full h-full',
      {
        [`${prefixCls}-aoa`]: domainList.find((i) => i.domain === 'aoa'),
        'hlxb-jl': domainStore.isHlxbJl,
      },
    ]"
    class="relative w-full h-full"
  >
    <AppDomainPicker
      v-if="domainList.length > 1"
      class="absolute top-4 right-4 z-30 enter-x xl:text-gray-600"
      :showText="false"
      :domainList="domainList"
      :iconColor="isJl ? '' : 'text-black'"
    />
    <span class="-enter-x xl:hidden ipc-logo">
      <AppLogo :alwaysShowTitle="true" class="left-8 !top-5" logoImgWidth="auto" :icon="logo2" />
    </span>

    <div
      class="container-bg container relative h-full py-2 mx-auto sm:px-2"
      style="max-width: 100%"
    >
      <Bgk />

      <div class="container-bg-2 flex h-full">
        <div class="hidden min-h-full pl-4 mr-4 xl:flex xl:flex-col xl:w-3/5">
          <AppLogo class="-enter-x" logoImgWidth="auto" :icon="logo2" />
          <div class="my-auto"> </div>
        </div>
        <div class="container-bg-3 flex justify-end w-full h-full mr-28">
          <div
            :style="{ ...setLoginStyle() }"
            :class="`${prefixCls}-form`"
            class="relative pt-14 pb-11 px-10 my-auto enter-x"
          >
            <div class="w-98">
              <LoginForm :disabled="showAppCode" />
              <ForgetPasswordForm />
              <RegisterForm />
              <MobileForm />
              <QrCodeForm />
            </div>
            <div class="qr-code-box" v-if="showAppCode">
              <img :src="appQrCodeImg" />
              <div class="mt-6">请扫描二维码下载APP</div>
              <div class="download-text">
                <span> 还没有移动端？</span>
                <span
                  class="text"
                  @mouseenter.stop="handleMouseEnter"
                  @mouseleave.stop="handleMouseLeave"
                  >去下载
                </span>
              </div>
            </div>
            <div class="download-app" v-if="appQrCodeImg">
              <span> 还没有移动端？</span>
              <span
                class="text"
                @mouseenter.stop="handleMouseEnter"
                @mouseleave.stop="handleMouseLeave"
                >去下载
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { AppLogo, AppDomainPicker } from '/@/components/Application';
  import type { DropMenu } from '/@/components/Dropdown';
  import LoginForm from './LoginForm.vue';
  import ForgetPasswordForm from './ForgetPasswordForm.vue';
  import RegisterForm from './RegisterForm.vue';
  import MobileForm from './MobileForm.vue';
  import QrCodeForm from './QrCodeForm.vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import Bgk from '../common/bgk.vue';
  import { setLoginStyle } from '../hooks/login';
  import { useDomainStoreWithOut } from '/@/store/modules/domain';

  defineProps({
    sessionTimeout: {
      type: Boolean,
    },
    showVerify: {
      type: Boolean,
    },
    domainList: {
      type: Array as PropType<DropMenu[]>,
      default: () => [],
    },
  });
  const { prefixCls } = useDesign('login');

  const domainStore = useDomainStoreWithOut();

  const logo2 = computed(() => domainStore.getProjectInfo?.logo2);
  const appQrCodeImg = computed(() => domainStore.getProjectInfo?.appQrCodeImg);
  const isJl = computed(() => domainStore.isHlxbJl);

  const showAppCode = ref(false);
  function handleMouseEnter() {
    showAppCode.value = true;
  }
  function handleMouseLeave() {
    showAppCode.value = false;
  }
</script>

<style lang="less">
  @prefix-cls: ~'@{namespace}-login';
  @aoa-prefix-cls: ~'@{namespace}-login-aoa';
  @logo-prefix-cls: ~'@{namespace}-app-logo';
  @countdown-prefix-cls: ~'@{namespace}-countdown-input';
  @form-prefix-cls: ~'@{namespace}-login-form';
  @title-prefix-cls: ~'@{namespace}-login-form-title';

  @dark-bg: #293146;

  html[data-theme='dark'] {
    .@{prefix-cls} {
      background-color: @dark-bg;

      &::before {
        background-image: url(/@/assets/svg/login-bg-dark.svg);
      }

      .ant-input,
      .ant-input-password {
        background-color: #232a3b;
      }

      .ant-btn:not(.ant-btn-link):not(.ant-btn-primary) {
        border: 1px solid #4a5569;
      }

      &-form {
        background: transparent !important;
      }

      .app-iconify {
        color: #fff;
      }
      .@{title-prefix-cls} {
        color: #fff;
      }
    }

    input.fix-auto-fill,
    .fix-auto-fill input {
      -webkit-text-fill-color: #c9d1d9 !important;
      box-shadow: inherit !important;
    }
  }

  .@{prefix-cls} {
    min-height: 100%;
    overflow: hidden;
    @media (max-width: @screen-xl) {
      background-color: #293146;

      .@{prefix-cls}-form {
        background-color: #fff;
      }
    }

    @media (max-width: @screen-ipc) {
      .@{prefix-cls}-form {
        background: rgba(255, 255, 255, 0.2) !important;
        box-shadow: 0px 0px 16px 0px rgba(110, 115, 151, 0.16);
        border-radius: 16px 16px 16px 16px;
        backdrop-filter: blur(32px);
      }
    }

    .container-bg {
      position: relative;

      .img1 {
        height: 100%;
        width: 100%;

        position: absolute;
        top: 0;
        left: 0;
        object-fit: cover;

        @media (max-width: @screen-xl) {
          // display: none;
        }
      }

      .login-error-msg {
        margin-top: -22px;
        font-size: 14px;
        line-height: 22px;
        color: @danger-color;
      }

      .qr-code-box {
        z-index: 999;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        padding: 56px 96px 0;
        background: @white;
        text-align: center;
        color: @text-color-call-out;
        border-radius: 16px;
        font-size: 16px;

        .download-text {
          position: absolute;
          width: 100%;
          left: 0;
          bottom: 40px;
          text-align: center;
          color: @text-color-bold;

          .text {
            display: inline-block;
            cursor: pointer;
            color: @theme-color;
          }
        }
      }

      .download-app {
        margin-top: 24px;
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        font-style: normal;
        text-transform: none;
        text-align: center;

        > .text {
          display: inline-block;
          cursor: pointer;
          color: @theme-color;

          &:hover {
            background: @theme-color-8p;
          }
        }
      }
    }

    .@{logo-prefix-cls} {
      position: absolute;
      top: 0;
      height: 30px;
      z-index: 999;

      &__title {
        font-size: 16px;
        color: @text-color-bold;
        @media (max-width: @screen-xl) {
          color: @white;
        }
      }

      img {
        width: 34px;
      }
    }
    .@{title-prefix-cls} {
      color: @text-color-bold;
    }

    .ant-checkbox-wrapper {
      color: @text-color-bold;
    }

    .container {
      .@{logo-prefix-cls} {
        display: flex;
        align-items: center;
        position: absolute;
        top: 32px;
        left: 20px;

        &__title {
          font-size: 24px;
          color: @text-color-bold;
          margin-left: 12px;
        }

        &__img {
          width: auto;
          height: 28px;
        }
      }

      .@{form-prefix-cls} {
        background: @white;
        box-shadow: 0px 0px 16px 0px rgba(110, 115, 151, 0.16);
        border-radius: 16px;
        color: @text-color-bold;

        .login-btn {
          width: 100%;
          height: 48px;
          opacity: 1;
          // box-shadow: 0px 8px 24px 0px @theme-color-32p !important;
          font-size: 18px;
        }

        .login-form-verify {
          .ant-input-group-addon {
            background: transparent;
            padding: 0;
            cursor: pointer;
          }
        }

        .login-form-item {
          margin-bottom: 24px;

          .ant-input-affix-wrapper-lg {
            height: 48px;
          }
        }
      }
    }

    &-sign-in-way {
      .anticon {
        font-size: 22px;
        color: @text-color-call-out;
        cursor: pointer;

        &:hover {
          color: @primary-color;
        }
      }
    }

    .@{countdown-prefix-cls} input {
      min-width: unset;
    }

    .ant-divider-inner-text {
      font-size: 12px;
      color: @text-color-secondary;
    }
  }

  .@{aoa-prefix-cls} {
    @media (max-width: @screen-ipc) {
      // background: #232a3b url('../../../../assets/logo-bg-ipc.png') center no-repeat;
      // background-size: 100% 100%;

      .ipc-logo {
        .vben-app-logo {
          top: 32px;
          left: 88px;
          transform: scale(1.5);
        }
      }

      .ant-input-affix-wrapper {
        // border-color: rgba(255, 255, 255, 0.2);
        // background-color: rgba(255, 255, 255, 0.1);

        .ant-input {
          // background-color: rgba(255, 255, 255, 0);
          // color: #fff;

          &::placeholder {
            // color: rgba(255, 255, 255, 0.7);
          }

          &:-webkit-autofill,
          &:-webkit-autofill:hover,
          &:-webkit-autofill:focus {
            -webkit-text-fill-color: white;
            box-shadow: none !important;
            // caret-color: #fff;
          }
        }

        .ant-input-prefix {
          > span {
            // color: #fff !important;
          }
        }

        .anticon.ant-input-password-icon {
          // color: #fff;
        }
      }
    }

    .@{aoa-prefix-cls} {
      .container {
        @media (max-width: @screen-ipc) {
          background: #272735;

          h2 {
            color: #fff !important;
          }

          .ant-form {
            .ant-form-item-control-input-content {
              > p {
                font-size: 16px;
                line-height: 1.5;
                color: #fff !important;
              }

              > .ant-checkbox-wrapper {
                > span:nth-child(2) {
                  > span {
                    color: #fff !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .hlxb-jl {
    .container {
      .@{logo-prefix-cls} {
        &__title {
          color: @white;
        }
      }
    }
    .download-app,
    .ant-checkbox-wrapper,
    .@{title-prefix-cls} {
      color: @white;
    }
  }
</style>
