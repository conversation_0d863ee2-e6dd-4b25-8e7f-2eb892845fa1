import { ref, watch, WatchSource } from 'vue';
import { type RouteLocationNormalizedLoaded } from 'vue-router';
import {
  useIntervalFn,
  useElementSize,
  useMutationObserver,
  useIntersectionObserver,
  type MaybeElement,
  type MaybeElementRef,
} from '@vueuse/core';
import { isEmpty, cloneDeep, get } from 'lodash-es';
import {
  getShowModelDetailApi,
  getFlowDataDetailApi2,
  previewDefaultBusinessDataApi,
  indicatorDistributeApi,
} from '/@process-editor/api';
import { useProcess } from '../hooks/useProcess';
import { isOldPen, isNumeric, getTheme, isImgPen, linkImgPenFunc } from './share';
import { ImgPen } from './diagrams/common';
import { getInputStatusImg, processHoverCursor } from './kits/data';
import { useMessage } from '/@/hooks/web/useMessage';
import { IndicatorDistribute } from '../api/model';
import { PRODUCT_DATA_KIT, DISTRIBUTE_STATUS } from './kits/constant';
import { FLOW_VERSION, MODEL_INFO } from '../constant/process';
import { LockState, LineAnimateType, lineAnimateTargetType } from 'hlxb-meta2d-core';
import type { Meta2d, Meta2dData, Pen } from 'hlxb-meta2d-core';
import type { DistributeStatus } from './kits/types';
import type { KitsV2 } from './kits/render';
// 数据引用需优化
import { mapProps } from '../views/editor/data/defaultsConfig';
import { addResourcePrefix, saveFlowDataDetail } from '../utils/index';
import { isNumber } from '/@/utils/is';
import { parseJson } from '/@/utils';
import { filterNoPermissionPens } from '/@process-editor/utils/index';

/**
 * 工艺流程通用逻辑
 * 1. 获取工艺流程渲染数据(流程、业务) => meta2d打开图纸 => 轮询更新套件
 * 2. 监听Meta2d大小变化
 * 3. 检测当前图元是否是旧的图元，如果不是保存流程图，获取模型详情，更新localstorage
 * 4. 监听输入框变化
 */
const { createMessage } = useMessage();

/**
 * 流程数据处理
 * @param {Object} data 流程数据
 * @returns
 */
export function translateFlowData(data: Recordable, mode: 'preview' | 'edit' = 'preview') {
  let pens = data.pens || [];
  if (mode === 'preview') {
    // 预览过滤无权限的图元
    pens = filterNoPermissionPens(data.pens);
    pens.map((i) => {
      // 车辆初始值处理
      if (i.lineAnimateType === LineAnimateType.Custom) {
        switch (i.lineAnimateTargetType) {
          case lineAnimateTargetType.PercentImage:
            i.animatePercent = 0;
            break;
          case lineAnimateTargetType.DurationImage:
            i.duration = 0;
            break;
        }
      }
      // isImgPen
      if (i.isSimulation) {
        i.visible = false;
      }
      return i;
    });
  }
  return {
    ...data,
    locked: mode === 'preview' ? LockState.DisableEdit : LockState.None,
    rule: mode === 'preview' ? false : true,
    scale: data.scale ?? 1,
    originPens: cloneDeep(pens),
    pens: pens,
    background: data.background || mapProps.background,
  };
}

/**
 * 获取工艺流程数据
 * @param {string} flowId 流程id
 * @param {string} version 流程版本
 * @returns
 */
export async function getFlowData(
  flowId: string,
  version = '',
  mode: 'preview' | 'edit' = 'preview',
) {
  const res = await getFlowDataDetailApi2({
    flowId: flowId,
    version: version,
  });
  const flowDataJson = JSON.parse(res?.flowDataJson || '{}');
  const _flowDataJson_ = translateFlowData(flowDataJson, mode);

  return {
    ...res,
    flowDataJson: _flowDataJson_,
  };
}

/**
 * 获取工艺流程业务数据
 * @param {string} flowId 流程id
 * @param {string} version 流程版本
 * @returns
 */
export async function getBusinessData(flowId: string, version = '') {
  const res = await previewDefaultBusinessDataApi({
    flowId: flowId,
    version: version,
  });

  const result = res.map((i) => {
    const businessData = JSON.parse(i.businessData);
    if (!businessData.hasOwnProperty('resourceInterfaceId')) {
      businessData.resourceInterfaceId = '';
    }
    return {
      ...i,
      businessData,
    };
  });

  return result;
}

/**
 * 获取工艺流程渲染数据
 * @param {string} flowId 流程id
 * @param {string} version 流程版本
 * @returns
 */
export async function getProcessData(
  flowId: string,
  version?: string,
  mode: 'preview' | 'edit' = 'preview',
) {
  const promises = [getFlowData(flowId, version, mode), getBusinessData(flowId, version)];
  const result = { flowData: {}, businessData: [] };

  await Promise.allSettled(promises).then((results: Recordable[]) => {
    const values = results.map((i) => i.value);
    const [flowData, businessData] = values;
    result.flowData = flowData;
    result.businessData = businessData;
  });

  return result as {
    flowData: any;
    businessData: Recordable[];
  };
}

/**
 * 获取Meta2d渲染数据
 * @param {Object} kitIns 套件实例
 * @param {Object} flowData 流程数据
 * @param {Object} businessData 业务数据
 * @returns
 */
export async function getMeta2dData(
  kitIns: KitsV2,
  flowData: Meta2dData,
  businessData: Recordable[],
  mode: 'preview' | 'edit' = 'preview',
) {
  let pens = await kitIns.getPens(flowData.pens, businessData, flowData.scale || 1);
  pens = pens.filter((i) => i);
  // 把pens中isSimulation值为true的放在最后面（零像素设备层级最高）
  pens = pens.filter((i) => !i?.isSimulation).concat(pens.filter((i) => i?.isSimulation));

  // 遍历pens将operation属性为navigation的pen元素替换成文本元素
  if (mode === 'preview') {
    pens.forEach((item) => {
      if (item?.operation === 'navigation') {
        item.text = '';
        item.width = item.width;
        item.height = item.height;
        item.borderRadius = 0.1;
        item.name = 'rectangle';
        item.lineCap = item.id;
        item.id = 'butt';
        item.children = [];
        item.x = item.x;
        item.y = item.y;
        item.lineWidth = 0;
        item.fontSize = 12;
        item.lineHeight = 1.5;
        item.rotate = 0;
        item.color = '';
        item.hoverColor = 'rgba(255,255,255,0)';
        item.background = 'rgba(255,255,255,0)';
        item.image = '';
      }
      if (item.image) {
        processHoverCursor(item);
      }
    });
  }

  const _flowData_ = cloneDeep(flowData);
  if (mode === 'preview') {
    _flowData_.locked = LockState.DisableMoveScale;
    _flowData_.disableScale = true;
    _flowData_.disableTranslate = true;
    pens = pens.filter((i) => !i?.isSimulation);
  }
  _flowData_.pens = pens;

  console.log('_flowData_', _flowData_, pens);

  return _flowData_;
}

/**
 * 检查当前设备是否为移动设备
 *
 * @returns {boolean} 如果是移动设备，返回 true；否则返回 false
 */
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * meta2d打开图纸
 * @param {Object} meta2dIns meta2d实例
 * @param {Object} kitIns 套件实例
 * @param {Object} flowData 流程数据
 * @param {Object} businessData 业务数据
 * @returns
 */
export async function openMeta2d(
  meta2dIns: Meta2d,
  kitIns: KitsV2,
  flowData: Meta2dData,
  businessData: Recordable[],
  mode: 'preview' | 'edit' = 'preview',
) {
  if (isEmpty(flowData)) return;

  meta2dIns.clear();
  kitIns.setTheme(getTheme(flowData.background || '#fff'));
  flowData.pens = flowData.pens.map((item) => {
    if (item.image) {
      processHoverCursor(item);
      item.image = addResourcePrefix(item.image);
    }
    if (item.video) {
      item.video = addResourcePrefix(item.video);
    }

    return item;
  });
  const _flowData_ = await getMeta2dData(kitIns, flowData, businessData, mode);

  if (mode === 'preview') {
    _flowData_.locked = LockState.DisableMoveScale;
    _flowData_.disableScale = true;
    _flowData_.disableTranslate = true;
  } else if (mode === 'edit') {
    // 禁用后，图片不能拖动到画布
    // _flowData_.locked = LockState.DisableScale;
  }

  if (isMobile()) {
    _flowData_.locked = LockState.DisableEdit;
  }

  console.log('_flowData_', _flowData_);
  meta2dIns.open(_flowData_);

  if (mode === 'preview') {
    // 处理video，只有渲染完成才可以，操作dom
    const videosPen = _flowData_.pens.filter((i) => i.name === 'video');
    for (const [pen] of videosPen.entries()) {
      const videoEl = pen.calculative.media;
      if (!videoEl) return;
      const videoBoxEl = videoEl.parentElement;
      if (!videoBoxEl) return;
      // 视频移上去出现声音图标，覆盖le5le事件，后续优化
      videoBoxEl.onmouseenter = null;
      if (pen.noPause) {
        videoBoxEl.onclick = (e) => e.preventDefault();
      }
    }

    // 临时逻辑
    const names = _flowData_.pens.map((i) => i.name);
    meta2dIns.resize();
    if (names.includes('鼓风机底座')) {
      meta2dIns.fitView(true, 100);
    } else {
      // 预览的内边距由配置设置
      let viewPadding = _flowData_?.viewPadding;
      viewPadding = viewPadding ? Number(viewPadding) : 10;
      meta2dIns.fitView(true, viewPadding);
    }
  }
}

/**
 * 轮询更新套件
 * @param {Function} fn 更新函数
 * @param {number} time 时间间隔
 * @returns
 */
export function updateKitInterval(fn: Fn, time = 1000 * 10) {
  const pausable = useIntervalFn(fn, time);

  return pausable;
}

/**
 * 更新套件
 * @param {Object} kitIns 套件实例
 * @param {string} flowId 流程id
 * @param {string} version 流程版本
 * @param {number} scale 缩放大小
 */
export async function updateKits(kitIns: KitsV2, flowId: string, version: string, scale: number) {
  if (!kitIns) return;

  const kits = await previewDefaultBusinessDataApi({
    flowId: flowId,
    version: version || '',
  });
  const formatData = kits.map((i) => ({
    ...i,
    businessData: JSON.parse(i.businessData),
  }));

  penInit(kitIns.meta2dInstance, formatData);
  await kitIns.update(formatData, scale);
}

export function penInit(meta2dInstance: Meta2d, formatData: Recordable[] = []) {
  if (!meta2dInstance) return;
  // @ts-ignore
  const pens: Pen[] = cloneDeep(meta2dInstance.data().originPens || []);

  updateLinePens(pens, formatData, meta2dInstance);

  updateImagePens(pens, formatData, meta2dInstance);
}

/**
 * 更新图片图元
 * @param {Array} pens 图元
 * @param {Array} formatData 业务数据
 */

function updateImagePens(pens: Pen[], formatData: Recordable[], meta2dInstance: Meta2d) {
  const imgPens = pens.filter((p) => isImgPen(p)) as unknown as ImgPen[];
  imgPens.forEach((pen) => {
    const penData = formatData.find((i) => i.flowModelId === pen.id);
    if (!penData) return;
    if (!penData.businessData?.scriptResult) return;

    const newPen = meta2dInstance.findOne(pen!.id as string) as unknown as ImgPen;

    const scriptResult = parseJson(penData.businessData?.scriptResult || '');

    const _lineStatus = scriptResult.lineStatus || 0;
    const _imgStatus = scriptResult.imgStatus || 0;
    const _playback = _lineStatus != 1;
    if (newPen!._lineStatus == _lineStatus && newPen!._imgStatus == _imgStatus) {
      return;
    }
    // 更新图片图元
    meta2dInstance.setValue({
      id: pen.id,
      _playback,
      _imgStatus,
      _lineStatus,
    });

    linkImgPenFunc(pen, meta2dInstance);
  });
}

/**
 * 更新线条图元
 * @param {Array} pens 图元
 * @param {Array} formatData 业务数据
 */
function updateLinePens(pens: Pen[], formatData: Recordable[], meta2dInstance: Meta2d) {
  pens.forEach((pen) => {
    if (pen.name === 'line') {
      const penData = formatData.find((i) => i.flowModelId === pen.id);
      if (!penData) return;
      updateLinePen(pen, penData, meta2dInstance);
      // meta2dInstance.setValue()
    }
  });
}
/**
 * 更新线条图元
 * @param {Object} pen 图元
 * @param {Array} formatData 业务数据
 */
function updateLinePen(pen: Pen, formatData: Recordable, meta2dInstance) {
  const data = formatData.businessData?.productDataInfos || [];
  const dataValue = parseFloat(data[0]?.dataValue);
  if (!isNumber(dataValue)) return;
  //  获取车辆数据
  if (pen.lineAnimateType === LineAnimateType.Custom) {
    switch (pen.lineAnimateTargetType) {
      case lineAnimateTargetType.PercentImage:
        setAnimateTypeForValue(pen, dataValue, meta2dInstance);
        break;
      case lineAnimateTargetType.DurationImage:
        setAnimateTypeForTime(pen, dataValue, meta2dInstance);
        break;
    }
  }
}

function setAnimateTypeForValue(pen: Pen, data: number, meta2dInstance) {
  const point = Math.round(data * 100) / 10000;
  const animatePercent = isNaN(point) ? 1 : point;

  meta2dInstance.setValue({
    id: pen.id,
    animatePercent,
    visible: animatePercent > 0,
    autoPlay: data >= 0 || pen.autoPlay,
  });
}

function setAnimateTypeForTime(pen: Pen, data: number, meta2dInstance) {
  const newPen = meta2dInstance.findOne(pen.id);
  if (data < 0) {
    meta2dInstance.stopAnimate(pen.id);
  } else if (newPen?.duration != data) {
    meta2dInstance.startAnimate(pen.id);
  }

  meta2dInstance.setValue({
    id: pen.id,
    duration: data,
    visible: data > 0,
    autoPlay: true,
  });
}

/**
 * 监听Meta2d大小变化
 * @param {HTMLElement} element 页面元素
 * @param {Object} meta2dIns meta2d实例
 */
export function onMeta2dSizeChange(element: MaybeElement, meta2dIns: Meta2d): WatchSource {
  const { width, height } = useElementSize(element);
  const stop = watch(
    () => [width.value, height.value],
    ([newW, newH]) => {
      try {
        if (newW && newH) {
          meta2dIns?.resize();
          meta2dIns?.fitView();
        } else {
          meta2dIns?.clear();
          meta2dIns?.destroy();
        }
      } catch (error) {
        console.error('工艺流程resize', error);
      }
    },
  );

  return stop;
}

// interface ModelParams {
//   flowId: string;
//   modelId: string;
//   version: string;
// }

const { getEditorLocalStorage, updateModelInfo, updateFlowVersion, updateCanvasData } =
  useProcess();

// declare const meta2d: Meta2d;

/**
 * 获取模型详情数据
 * @param {Object} activePen 选中的图元
 * 只在编辑页面使用
 */
export async function getModelData(activePen: Pen, route: RouteLocationNormalizedLoaded) {
  const params = {
    flowId: route.query.flowId,
    modelId: activePen.id,
    version: getEditorLocalStorage(FLOW_VERSION),
  };

  const res = await getShowModelDetailApi(params);
  return res?.modelInfo || {};
}

/**
 * 更新位置(无边框)
 * @param {number} e 外框方位
 * @param {Object} data 源数据
 * @param {Object} activePen 选中的图元
 */
function updatePositionNoBorder(e: number, data: Recordable, activePen: Pen) {
  const modelX = Number(activePen.x);
  const modelY = Number(activePen.y);
  const modelWidth = Number(activePen.width);
  const modelHeight = Number(activePen.height);
  const modelWidthHalf = Number(activePen.width) / 2;
  const frameWidth = Number(data.frameWidth);
  const frameWidthHalf = Number(data.frameWidth) / 2;
  const itemHeight = 32;
  const margin = 8;

  switch (e) {
    case 0:
      // 上
      data.positionX = modelX + modelWidthHalf - frameWidthHalf;
      data.positionY = modelY - itemHeight - margin;
      break;
    case 4:
      // 下
      data.positionX = modelX + modelWidthHalf - frameWidthHalf;
      data.positionY = modelY + modelHeight + margin;
      break;
    case 6:
      // 左
      data.positionX = modelX - frameWidth - margin;
      data.positionY = modelY;
      break;
    case 2:
      // 右
      data.positionX = modelX + modelWidth + margin;
      data.positionY = modelY;
      break;
    default:
      break;
  }
}

/**
 * 更新位置(有边框)
 * @param {number} e 外框方位
 * @param {Object} data 源数据
 * @param {Object} activePen 选中的图元
 */
function updatePositionHasBorder(e: number, data: Recordable, activePen: Pen) {
  // 边框width: 326
  // 边框height: 407
  const modelX = Number(activePen.x);
  const modelY = Number(activePen.y);
  const modelWidth = Number(activePen.width);
  const modelHeight = Number(activePen.height);
  const modelWidthHalf = Number(activePen.width) / 2;
  const frameWidth = 326;
  const frameWidthHalf = 326 / 2;
  const itemHeight = 32;
  const margin = 8;

  switch (e) {
    case 0:
      // 上
      data.positionX = modelX + modelWidthHalf - frameWidthHalf;
      data.positionY = modelY - itemHeight - margin;
      break;
    case 4:
      // 下
      data.positionX = modelX + modelWidthHalf - frameWidthHalf;
      data.positionY = modelY + modelHeight + margin;
      break;
    case 6:
      // 左
      data.positionX = modelX - frameWidth - margin;
      data.positionY = modelY;
      break;
    case 2:
      // 右
      data.positionX = modelX + modelWidth + margin;
      data.positionY = modelY;
      break;
    default:
      break;
  }
}

/**
 * 处理业务数据
 * @param data 业务数据
 * @param activePen 选中的图元
 * @returns {Array} 业务数据
 */
function handleBusinessData(data: Recordable[], activePen: Pen) {
  function updatePosition(info, activePen) {
    const { frameType, framePlacement } = info;
    if (typeof frameType === 'number' && typeof framePlacement === 'number') {
      switch (info.frameType) {
        case 0:
          updatePositionNoBorder(info.framePlacement, info, activePen);
          break;
        case 1:
          updatePositionHasBorder(info.framePlacement, info, activePen);
          break;
        default:
          break;
      }
    }
  }

  const result = data.map((i) => {
    const _item_ = cloneDeep(i);
    const update = [PRODUCT_DATA_KIT];
    if (update.includes(_item_.kitTypeName)) updatePosition(_item_, activePen);

    return {
      ..._item_,
      businessData: i.businessData ? JSON.parse(i.businessData) : {},
    };
  });

  return result;
}

/**
 * 获取模型套件数据
 * @param activePen 选中的图元
 * @param businessInfos 业务数据
 * @returns {Array} 套件数据
 */
export async function getModelKitData(activePen, businessInfos: Recordable[]) {
  const kits = handleBusinessData(businessInfos, activePen);

  return kits;
}

/**
 * 获取模型数据(通过判断是否是旧的图元)
 * @param activePen 选中的图元
 * @param pens 所有图元
 * @param RouteLocationNormalizedLoaded 路由
 * @returns {Object} 模型数据
 */
export async function getModelDetailByIsOldPen(
  activePen: Pen,
  pens: Pen[],
  route: RouteLocationNormalizedLoaded,
  callback?: (data: Recordable) => void,
) {
  const isOld = isOldPen(activePen, pens);
  if (!isOld) {
    // 保存流程图
    const flowInfo = await saveFlowDataDetail({
      flowId: route.query.flowId || '',
      version: getEditorLocalStorage(FLOW_VERSION),
    });
    // 更新流程图版本
    updateFlowVersion(flowInfo.version);
    // 更新画布数据
    updateCanvasData(flowInfo.canvasData);
    // 获取模型详情
    const modelInfo = await getModelData(activePen, route);
    // 更新模型数据
    updateModelInfo(modelInfo);
    if (callback) callback(getEditorLocalStorage(MODEL_INFO));
  }

  const modelInfo = getEditorLocalStorage(MODEL_INFO);
  return modelInfo;
}

/**
 * 输入框变化事件
 * @param {Meta2d} meta2dIns meta2d实例
 * @returns
 */
export function inputChangeEvent(meta2dIns: Meta2d, rootNodeClass: string) {
  const div: HTMLDivElement | null = document.querySelector(`.${rootNodeClass} .input-div`);
  if (!div) {
    // throw Error(`找不到(.${rootNodeClass} .input-div)的元素`);
    return;
  }

  const hasChildList = ref(false);
  const { stop } = useMutationObserver(
    div,
    (mutations) => {
      hasChildList.value = mutations.map((i) => i.type).includes('childList');
    },
    {
      childList: true,
      attributeFilter: ['data-pen-id'],
    },
  );

  function getDistributeParams(id: string): IndicatorDistribute {
    const pen = findPen(id);
    const rawData = pen['rawData'] || {};

    return {
      indicatorCode: rawData.code || '',
      indicatorValue: pen.text || '',
    };
  }

  function findPen(id: string, path: string | string[] = 'id', cb = (i) => i) {
    const pens = meta2dIns.store.data.pens.filter(cb);
    const pen = pens.find((i) => get(i, path, '') === id) || {};

    return pen;
  }

  async function distribute(params: IndicatorDistribute) {
    if (!Object.values(params).every(Boolean)) {
      if (!params.indicatorCode) {
        createMessage.info('指标CODE不能为空');
      } else if (!params.indicatorValue) {
        createMessage.info('指标值不能为空');
      }

      return;
    }

    // "-1": 正常  "0": 下发成功  "1":下发中  "2":下发超时  "3":下发失败
    let status: DistributeStatus = '3';
    try {
      await indicatorDistributeApi(params);
      createMessage.success('指令已下发');
      status = '1';
    } catch (error) {
      createMessage.error('指令下发失败');
      status = '3';
    }

    return status;
  }

  function validate(pen: Pen, prevValue: string) {
    const currentValue = pen.text || '';
    const indexStatus = pen['rawData'].indexStatus;
    const isNum = isNumeric(currentValue);

    if (indexStatus === '1') {
      // "-1": 正常  "0": 下发成功  "1":下发中  "2":下发超时  "3":下发失败
      createMessage.info('指令下发中，请等待');
    } else if (!isNum) {
      createMessage.info('请输入数字');
    }

    return isNum && prevValue !== currentValue && indexStatus !== '1';
  }

  function updateInputStatus(pen: Pen, status: DistributeStatus) {
    const rawData = pen['rawData'] || {};
    const statusId = rawData.statusId;
    const statusPen = findPen(
      statusId,
      ['rawData', 'statusId'],
      (i) => i.categoryCN === DISTRIBUTE_STATUS,
    );

    meta2dIns.setValue({
      id: statusPen.id,
      name: status === '1' ? 'gif' : 'image',
      visible: true,
      image: getInputStatusImg(status),
    });
    meta2dIns.setValue({
      id: pen.id,
      rawData: { ...pen['rawData'], indexStatus: status },
    });
  }

  const dataPenId = ref('');
  const prevValue = ref('');
  watch(
    () => hasChildList.value,
    async (newVal) => {
      if (!newVal) {
        console.log('失去焦点');
        const pen = findPen(dataPenId.value);
        const validateResult = validate(pen, prevValue.value);

        if (validateResult) {
          const params = getDistributeParams(dataPenId.value);
          const status = await distribute(params);
          status && updateInputStatus(pen, status);
        }
      } else {
        dataPenId.value = div.getAttribute('data-pen-id') || '';
        const pen = findPen(dataPenId.value);
        prevValue.value = pen.text || '';
        console.log('获取焦点');
      }
    },
  );

  return { stop };
}

interface UpdateKitParams {
  kitIns: KitsV2;
  flowId: string;
  version: string;
  scale: number;
}

/**
 * 通过元素可见性开启/关闭轮询
 * @param {UpdateKitParams} kit updateKits函数形参
 * @param {number} interval 轮询间隔
 * @param {MaybeElementRef} target 监听的dom
 * @returns {Function} 停止函数
 */
let globalStopFn = () => {};
export function pollingObserver(kit: UpdateKitParams, interval: number, target: MaybeElementRef) {
  const { kitIns, flowId, version, scale } = kit;

  const { pause, resume } = updateKitInterval(() => {
    updateKits(kitIns, flowId, version, scale);
  }, interval);

  const observer = useIntersectionObserver(target, ([{ isIntersecting }]) => {
    if (isIntersecting) {
      resume();
    } else {
      pause();
    }
  });
  globalStopFn();

  function stop() {
    pause();
    observer.stop();
  }
  globalStopFn = stop;

  return stop;
}

/**
 * 套件轮询
 * @param {UpdateKitParams} kit updateKits函数形参
 * @param {number} interval 轮询间隔
 */
export function kitsPolling(kit: UpdateKitParams, interval: number) {
  const { kitIns, flowId, version, scale } = kit;

  const pausable = updateKitInterval(() => {
    updateKits(kitIns, flowId, version, scale);
  }, interval);

  return pausable;
}
