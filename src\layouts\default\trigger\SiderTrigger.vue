<template>
  <div @click.stop="toggleCollapsed" :class="getTriggerBoxClass">
    <div
      class="icon"
      :class="{
        'flex-1': getCollapsed,
        'text-center': getCollapsed,
      }"
    >
      <Icon size="20" :icon="`icon-park-outline:menu-${getCollapsed ? 'fold' : 'unfold'}-one`" />
    </div>
    <div
      class="trigger-desc overflow-hidden"
      :class="{
        'w-0': getCollapsed,
        'ml-2': !getCollapsed,
      }"
    >
      收起导航
    </div>
  </div>
</template>
<script lang="ts">
  import Icon from '/@/components/Icon/index';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { defineComponent, computed, unref, watch } from 'vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { isIPCBreakPoint } from '/@/hooks/event/useBreakpoint';

  export default defineComponent({
    name: 'SiderTrigger',
    components: { Icon },
    setup() {
      const { prefixCls } = useDesign('trigger-box');
      const { getCollapsed, toggleCollapsed, getMenuTheme, setMenuSetting } = useMenuSetting();
      const { isIPCRef } = isIPCBreakPoint();

      const getTriggerBoxClass = computed(() => {
        return [
          'flex',
          'h-full',
          prefixCls,
          `${prefixCls}-${unref(getMenuTheme)}`,
          {
            'border-top': !unref(getCollapsed),
          },
        ];
      });

      watch(
        () => unref(isIPCRef),
        (val) => {
          setMenuSetting({
            collapsed: !!val,
          });
        },
        { deep: true, immediate: true },
      );

      return { getCollapsed, toggleCollapsed, getTriggerBoxClass };
    },
  });
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-trigger-box';

  .@{prefix-cls} {
    text-align: left;
    padding: 0 8px;
    margin: 0 8px;
    border-radius: 4px;
    transition: background-color 0.3s;

    .icon {
      font-size: 20px;
    }

    &-dark {
      color: #ededed;

      &:hover {
        color: @white;
        background-color: @sider-hover-bg-color;
      }
    }

    &-light {
      color: rgb(0 0 0 / 65%);

      &:hover {
        background-color: @sider-hover-bg-color;
      }
    }

    // &.border-top {
    //   border-top: 1px solid rgb(255 255 255 / 20%);
    // }

    .trigger-desc {
      transition: all @ease-in-out 0.3s;
    }
  }
</style>
