<template>
  <CockpitCardBox title="碳收益">
    <template #title-slot>
      <div class="title-container">
        <div class="title-slot">
          <Tooltip placement="topLeft" overlayClassName="aoa3-tooltip">
            <template #title>
              碳收益​是指企业通过减少温室气体排放或开发碳减排项目，在碳市场中获得的直接经济回报​或间接财务价值。<br />
              计算公式：<br />
              <div
                style="
                  background: rgba(0, 43, 25, 0.56);
                  border-radius: 4px 4px 4px 4px;
                  border: 1px solid rgba(255, 255, 255, 0.16);
                  padding: 8px 12px;
                "
              >
                碳收益 = 碳价格 × 碳排量<br />
                碳价格 = 基准价 × 技术系数 × 质量系数 × 区域浮动。<br />
                基准价 = 全国碳市场CEA均价80元 × CCE常规折扣率85%。<br />
                技术系数 = 精确曝气行业中间值1.09。<br />
                质量系数 = 精确曝气行业中间值1.22。<br />
                区域浮动 = 深圳区域调整1.12。
              </div>
            </template>
            <Icon icon="icon-park-solid:help" :size="18" />
          </Tooltip>
        </div>
        <AiCard :questionTemplate="question" questionTitle="碳收益" :deepThinking="deepThinking" />
      </div>
    </template>
    <template #content>
      <div class="carbon-profit-content w-full h-full" v-if="first">
        <div ref="chartRef" class="chart w-full h-full"></div>
      </div>
      <div class="empty-container w-full h-full" v-else>
        <Empty />
      </div>
    </template>
  </CockpitCardBox>
</template>

<script lang="ts" setup>
  import { ref, PropType, watch, computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { Tooltip } from 'ant-design-vue';
  import { CockpitCardBox, Empty } from './CockpitCard';
  import dayjs from 'dayjs';
  import { getScaleValByClientWidth } from '../data';
  import { useECharts } from '/@/hooks/web/useECharts';
  import type { ChartData } from '../type';
  import { AiCard } from '/@aoa/components/AiCard';

  const props = defineProps({
    data: {
      type: Array as PropType<ChartData[]>,
      default: () => [],
    },
    first: Boolean,
    deepThinking: {
      type: Boolean,
      default: false,
    },
    aiQuestionTemplate: {
      type: String,
      default: '',
    },
  });

  const chartRef = ref(null);
  const { setOptions } = useECharts(chartRef as any);

  const getXAxisData = (data: ChartData[]) => {
    if (data.length === 0) return [];
    return data[0].data?.map((item) => item.collectDateTime);
  };

  const getMax = () => {
    // console.log(data);
    // const dataArr = data
    //   .map((item) => item.data)
    //   .flat()
    //   .map((item) => item.value)
    //   .filter((item) => item !== null);
    // return dataArr.length <= 0 ? 1 : Math.max(...dataArr);
    return 2.5;
  };

  const getMin = () => {
    // console.log(data);
    // const dataArr = data
    //   .map((item) => item.data)
    //   .flat()
    //   .map((item) => item.value)
    //   .filter((item) => item !== null);
    // return Math.min(...dataArr);
    return 0;
  };

  const getValueFormatter = (value, digit = 2) => {
    return Number(value).toFixed(digit);
  };

  const getSeriesData = (data: ChartData[]) => {
    const setiseData = data.map((item) => {
      return {
        name: item.name,
        type: 'line',
        symbol: 'none',
        smooth: true,
        data: item.data?.map((item) => item.value),
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(209, 249, 255, 0.64)',
              },
              {
                offset: 1,
                color: 'rgba(209, 249, 255, 0)',
              },
            ],
          },
        },
        itemStyle: {
          color: '#caf2f7',
        },
      };
    });
    return setiseData.flat();
  };

  const setChart = () => {
    const { data } = props;
    let max = getMax();
    let min = getMin();

    const options = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#E1F3F1',
        borderColor: '#02695E',
        appendToBody: true,
        formatter: (params) => {
          return `<span style="font-size: 0.73vw; line-height: 1.5;">${
            Number(dayjs(params[0].axisValue).format('MM')) + '月'
          }</span></br>
          ${params
            .map((item, index) => {
              return `
            ${item.marker}&nbsp;<span  style="font-size: 0.73vw;">${
                item.seriesName
              }</span>&nbsp;<span >${
                item.value !== '' && item.value !== null && item.value !== undefined
                  ? `<span style="font-weight: 600;font-size: 0.73vw;">${getValueFormatter(
                      item.value,
                      data[index].digit,
                    )} </span>&nbsp;<span style="font-weight: 600;font-size: 0.73vw;">${
                      data[index]?.unit
                    }</span>`
                  : '-'
              }</span>&nbsp;
            `;
            })
            .join('</br>')}
          `;
        },
        // valueFormatter: (value) => {
        //   return value !== '' && value !== null && value !== undefined
        //     ? `${getValueFormatter(value, data[0].digit)} ${data[0]?.unit}`
        //     : '-';
        // },
      },
      legend: {
        show: false,
      },
      grid: {
        left: 8,
        right: 8,
        bottom: 0,
        top: getScaleValByClientWidth(35),
        containLabel: true,
        show: true,
        backgroundColor: 'rgba(2, 53, 32, 0.40)',
        borderWidth: 0,
      },
      xAxis: {
        type: 'category',
        data: getXAxisData(data),
        boundaryGap: false,
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          padding: [0, 5, 0, 0],
          formatter: (value) => {
            return Number(dayjs(value).format('MM')) + '月';
          },
        },
        axisTick: {
          show: false,
        },
        axisPointer: {
          show: true,
          type: 'line',
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            type: [5, 10],
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            shadowColor: 'rgba(178, 255, 241, 1)',
            shadowOffsetY: -2,
            shadowOffsetX: 0,
            shadowBlur: 5,
            width: 1,
          },
        },
      },
      yAxis: {
        type: 'value',
        name: data[0]?.unit ? `预估价格(${data[0].unit})` : '',
        max,
        min,
        interval: (max - min) / 5,
        nameTextStyle: {
          fontSize: getScaleValByClientWidth(13),
          color: '#fff',
          align: 'left',
          padding: [0, 0, 0, getScaleValByClientWidth(-34)],
        },
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          formatter: (value) => getValueFormatter(value, data[0].digit ?? 2),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A1D0C4',
            type: [5, 10],
            offset: 5,
          },
        },
      },
      series: getSeriesData(data),
    };

    setOptions(options as any, false);
  };

  watch(
    () => props.data,
    () => {
      setChart();
    },
    { deep: true },
  );

  const getQuestion = (data) => {
    if (!props.aiQuestionTemplate || !props.aiQuestionTemplate.includes('${data}')) {
      return props.aiQuestionTemplate;
    }
    if (!data.length) {
      return '';
    }
    const str = data[0]?.data
      .map((item) => {
        return `${Number(dayjs(item.collectDateTime).format('MM'))}月${
          item.value !== null && item.value !== undefined
            ? Number(item.value).toFixed(data[0].digit ?? 2)
            : '-'
        }${data[0].unit}`;
      })
      .join('、');
    return props.aiQuestionTemplate.replace('${data}', str);
  };

  const question = computed(() => {
    const data = props.data;
    return getQuestion(data);
  });
</script>
<style lang="less" scoped>
  .title-container {
    display: flex;
    align-items: center;
    .width-prop(5, margin-left);
  }

  .title-slot {
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      color: #fff;
    }
  }
  @media screen and (min-width: 1800px) {
    .carbon-profit-content {
      .px2vw(6);
      padding: @vw;
    }
  }
</style>
