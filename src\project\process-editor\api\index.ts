import { defZhczHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';
import { ResultEnum } from '/@/enums/httpEnum';
import { createLocalStorage } from '/@/utils/cache';
import { PLATFORM_KEY } from '/@/enums/cacheEnum';
import { IndicatorDistribute } from './model';
import type { SenceGroupIndicatorTree, DataSetType } from './type';
import { getFactoryId } from '/@process-editor/utils';

export enum Api {
  GET_FLOW_DATA_PAGE = '/process-configuration/equipmentFlow/page',
  GET_FLOW_DATA_LIST = '/process-configuration/equipmentFlow/list',
  STANDARD_SOURCE_TYPE_PAGE = '/process-configuration/standardSourceType/page',
  STANDARD_SOURCE_TYPE_LIST = '/process-configuration/standardSourceType/list',
  STANDARD_SOURCE_SORT_LIST = '/process-configuration/standardSource/getStandardSourceSortList',
  ADD_OR_UPDATE_STANDARD_SOURCE = '/process-configuration/standardSource/addOrUpdate',
  SAVE_FLOW_DATA_DETAIL = '/process-configuration/equipmentFlow/saveFlowDataDetail',
  SAVE_SHOW_MODEL_DETAIL = '/process-configuration/equipmentFlow/saveShowModelDetail',
  SHOW_MODEL_DETAIL = '/process-configuration/equipmentFlow/getShowModelDetail',
  GET_ALL_FLOW_TAGS = '/process-configuration/equipmentFlow/getAllFlowTags',
  BIND_FLOW = '/process-configuration/equipmentFlow/bindFlow',
  GET_ALL_EQ = '/process-configuration//equipmentFlow/getAllEq',
  BIND_EQUIPMENT = '/process-configuration//equipmentFlow/bindEquipment',
  GET_CAMERA_INFO_PAGE_LIST = '/camera/cameraInfo/getCameraInfoPageList',
  GET_FLOW_DATA_DETAIL = '/process-configuration/equipmentFlow/getFlowDataDetail',
  PREVIEW_DEFAULT_BUSINESS_DATA = '/process-configuration/equipmentFlow/previewDefaultBusinessData',

  ADD_OR_UPDATE_FLOW_DATA = '/process-configuration/equipmentFlow/addOrUpdate',
  STANDARD_SOURCE_PAGE = '/process-configuration/standardSource/page',
  ADD_OR_UPDATE_STANDARD_SOURCE_TYPE = '/process-configuration/standardSourceType/addOrUpdate',
  GET_SENCE_GROUP_INDICATOR_TREE = '/data-sence/groupInfo/getSenceGroupIndicatorTree',
  GetResourceInterfacePage = '/data-sence/resourceInterface/page',
  GET_FACTORY_LIST = '/factory/factory/getList',
  GET_FlOW_LIST_BY_FACTORIES = '/process-configuration/equipmentFlow/list',
  UPLOAD_EQUIPMENT_FILE = '/data-sence/resourceIndex/uploadFile',
  GET_KIT_DATA_SET_TREE = '/data-sence/groupInfo/getKitDataSetTree',
  GET_PLATFORM_LIST = '/data-sence/platformInfo/list',
  GET_INDEX_LIST = '/data-sence/displayResourceInfo/interval',
  GET_USER_CURRENT_FACTROY_ID = '/factory/factory/getUserCurrentFactroyId',
  GET_CURRENT_PLATFORM_ID = '/data-sence/platformInfo/getCurrentPlatformId',
  EXPORT_EQUIPMENT_FLOW = '/process-configuration/equipmentFlow/exportEquipmentFlow',
  IMPORT_EQUIPMENT_FLOW = '/process-configuration/equipmentFlow',
  DELETE_EQUIPMENT_FLOW = '/process-configuration/equipmentFlow/deleteEquipmentFlow',
  INDICATOR_DISTRIBUTE = '/data/indicator/distribute',
  COPY_EQUIPMENT_FLOW = '/process-configuration/equipmentFlow/copyEquipmentFlow',

  UPLOAD_FILE = '/data-sence/resourceIndex/uploadFile',
  CALL_RESOURCE_FUNCTION = '/data-sence/resourceInterface/callResourceFunction',

  GET_PAGE_WARN_EVENT = '/event-center/warnEvent/getPage',
  GET_DIAGNOSIS_RECORD_BY_INDICATOR_CODE = '/diagnosis/diagnosis-record/getDiagnosisRecordByIndicatorCode',
  SUMO_DISTRIBUTE = '/data/indicator/sumoDistribute',
  SUMO_DISTRIBUTE_INFO = '/data/indicator/sumoDistributeInfo',
  UPDATE_SUMO_DISTRIBUTE = '/data/indicator/updateSumoDistribute',

  CONTROL_SEND_SMS = '/data/indicator/sendSms',
  CONTROL_CHECK_NEED_SMS = '/data/indicator/checkNeedSms',
  CONTROL_VERIFY_SMSCODE = '/data/indicator/verifySmsCode',
}

const ls = createLocalStorage();

function proxyDefZhczHttp() {
  return new Proxy(defZhczHttp, {
    get(target, key) {
      // 抽离公共函数
      const original = target[key];
      if (typeof original === 'function') {
        return function (...args) {
          const [config, ...rest] = args;
          if (config.params) {
            config.params.factoryId = getFactoryId();
          } else {
            config.params = { factoryId: getFactoryId() };
          }
          return original.call(target, config, ...rest);
        };
      }
      return original;
    },
  });
}

export const defHttpHasFactoryId = proxyDefZhczHttp();

export const getFlowDataPageApi = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.GET_FLOW_DATA_PAGE,
    params,
  });

export const getFlowDataListApi = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.GET_FLOW_DATA_LIST,
    params,
  });

export const standardSourceTypePageApi = () =>
  defHttpHasFactoryId.post<any>({
    url: Api.STANDARD_SOURCE_TYPE_PAGE,
  });

export const standardSourceTypeListApi = () =>
  defHttpHasFactoryId.post<any>({
    url: Api.STANDARD_SOURCE_TYPE_LIST,
  });

export const standardSourceSortListApi = () =>
  defHttpHasFactoryId.get<any>({
    url: Api.STANDARD_SOURCE_SORT_LIST,
  });

export const getFlowDataDetailApi = (flowId) =>
  defHttpHasFactoryId.get<any>({
    url: Api.GET_FLOW_DATA_DETAIL,
    params: { flowId },
  });

export const getFlowDataDetailApi2 = (params) =>
  defHttpHasFactoryId.get<any>({
    url: Api.GET_FLOW_DATA_DETAIL,
    params,
  });

export const previewDefaultBusinessDataApi = (params) =>
  defHttpHasFactoryId.get<any>({
    url: Api.PREVIEW_DEFAULT_BUSINESS_DATA,
    timeout: 1000 * 60,
    params,
  });

export const bindFlowApi = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.BIND_FLOW,
    params,
  });

export const bindEquipmentApi = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.BIND_EQUIPMENT,
    params,
  });

export const getAllEqApi = (facId) =>
  defHttpHasFactoryId.get<any>(
    {
      url: Api.GET_ALL_EQ,
      params: { facId },
    },
    {
      errorMessageMode: 'none',
    },
  );

export const saveFlowDataDetailApi = (
  params,
): Promise<{
  dataJson: string;
  curVersion: string;
  flowId: string;
}> =>
  defHttpHasFactoryId.post<any>({
    url: Api.SAVE_FLOW_DATA_DETAIL,
    params,
  });

export const saveShowModelDetailApi = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.SAVE_SHOW_MODEL_DETAIL,
    params,
  });

export const getShowModelDetailApi = (params) =>
  defHttpHasFactoryId.get<any>({
    url: Api.SHOW_MODEL_DETAIL,
    params,
  });

export const getAllFlowTagsApi = (params) =>
  defHttpHasFactoryId.get<any>({
    url: Api.GET_ALL_FLOW_TAGS,
    params,
  });

export const getCameraInfoPageListApi = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.GET_CAMERA_INFO_PAGE_LIST,
    params,
  });

export const addOrUpdateFlowDataApi = (params) =>
  defHttpHasFactoryId.post({
    url: Api.ADD_OR_UPDATE_FLOW_DATA,
    params,
  });

export const standardSourcePageApi = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.STANDARD_SOURCE_PAGE,
    params,
  });

export const addOrUpdateStandardSourceApi = (params) =>
  defHttpHasFactoryId.post({
    url: Api.ADD_OR_UPDATE_STANDARD_SOURCE,
    params,
  });

interface UploadApiResult {
  msg: string;
  code: number;
  data: string[];
}

export const uploadApi = async (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) => {
  const ret = await defHttpHasFactoryId.uploadFile<UploadApiResult>(
    {
      url: Api.UPLOAD_EQUIPMENT_FILE,
      onUploadProgress,
    },
    params,
  );

  const { data } = ret;
  const hasSuccess = data && Reflect.has(data, 'code') && data.code === ResultEnum.SUCCESS;
  if (hasSuccess) {
    return data;
  } else {
    Promise.reject(data);
    return data;
  }
};

export const addOrUpdateStandardSourceTypeApi = (params) =>
  defHttpHasFactoryId.post({
    url: Api.ADD_OR_UPDATE_STANDARD_SOURCE_TYPE,
    params,
  });

export const getSenceGroupIndicatorTreeApi = (
  platformId = ls.get(PLATFORM_KEY) || '1',
): Promise<SenceGroupIndicatorTree> =>
  defHttpHasFactoryId.get({
    url: Api.GET_SENCE_GROUP_INDICATOR_TREE,
    params: { platformId },
  });

// 获取接口列表
export const getResourceInterfacePage = (params) =>
  defHttpHasFactoryId.get<any>({
    url: Api.GetResourceInterfacePage,
    params,
  });

export const getFactoryListApi = (
  params = {
    name: '',
    bizType: null,
  },
) =>
  defHttpHasFactoryId.post({
    url: Api.GET_FACTORY_LIST,
    params,
  });

export const getFlowListByFactoriesApi = (
  params = {
    bindSourceFrom: '',
    flowName: '',
    bindSourceUniqueIds: [],
  },
) =>
  defHttpHasFactoryId.post({
    url: Api.GET_FlOW_LIST_BY_FACTORIES,
    params,
  });

export const getKitDataSetTreeApi = (params: {
  platformId: string;
  groupPurpose: 0 | 1 | 2;
}): Promise<DataSetType> =>
  defHttpHasFactoryId.get({
    url: Api.GET_KIT_DATA_SET_TREE,
    params,
  });

export const getPlatformListApi = (params: { platformCode: ''; displayName: ''; orgCode: '' }) =>
  defHttpHasFactoryId.post({
    url: Api.GET_PLATFORM_LIST,
    params,
  });

export const getIndexListApi = (params: {
  startDateTime: string;
  endDateTime: string;
  indexCodes: string;
  tenantId: string;
}) =>
  defHttpHasFactoryId.post({
    url: Api.GET_INDEX_LIST,
    params,
  });

export const getFactoryIdApi = () =>
  defHttpHasFactoryId.get({
    url: Api.GET_USER_CURRENT_FACTROY_ID,
  });

export const getCurrentPlatformIdApi = () =>
  defHttpHasFactoryId.get(
    {
      url: Api.GET_CURRENT_PLATFORM_ID,
    },
    {
      errorMessageMode: 'none',
    },
  );

export const exportEquipmentFlowApi = (flowId: string) =>
  defHttpHasFactoryId.post({
    url: `${Api.EXPORT_EQUIPMENT_FLOW}?flowId=${flowId}`,
  });

export const importEquipmentFlowApi = (file) =>
  defHttpHasFactoryId.post({
    url: Api.IMPORT_EQUIPMENT_FLOW,
    params: file,
  });

export const deleteEquipmentFlowApi = (flowId: string) =>
  defHttpHasFactoryId.post({
    url: `${Api.DELETE_EQUIPMENT_FLOW}/${flowId}`,
  });

export const indicatorDistributeApi = (data: IndicatorDistribute) =>
  defHttpHasFactoryId.post({
    url: Api.INDICATOR_DISTRIBUTE,
    data,
  });

export const copyEquipmentFlowApi = (id: string) =>
  defHttpHasFactoryId.post({
    url: Api.COPY_EQUIPMENT_FLOW,
    data: { id },
  });

export const processFileUploadRemindingApi = async (params: UploadFileParams) => {
  const ret = await defHttpHasFactoryId.uploadFile<UploadApiResult>(
    {
      url: Api.UPLOAD_FILE,
    },
    params,
  );

  const { data } = ret;
  const hasSuccess = data && Reflect.has(data, 'code') && data.code === ResultEnum.SUCCESS;
  if (hasSuccess) {
    return data;
  } else {
    Promise.reject(data);
    return data;
  }
};

// 场景资源接口
export const callResourceFunction = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.CALL_RESOURCE_FUNCTION,
    params,
  });

// 获取报警规则列表
export const getWarnEventPage = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.GET_PAGE_WARN_EVENT,
    params,
  });

// 获取诊断记录
export const getDiagnosisRecordByIndicatorCode = (params) =>
  defHttpHasFactoryId.post<any>({
    url: Api.GET_DIAGNOSIS_RECORD_BY_INDICATOR_CODE,
    params,
  });

export const sumoDistributeApi = (data) =>
  defHttpHasFactoryId.post({
    url: Api.SUMO_DISTRIBUTE,
    data,
  });

export const sumoDistributeInfoApi = (data) =>
  defHttpHasFactoryId.post({
    url: Api.SUMO_DISTRIBUTE_INFO,
    data,
  });

export const updateSumoDistributeApi = (data) =>
  defHttpHasFactoryId.post({
    url: Api.UPDATE_SUMO_DISTRIBUTE,
    data,
  });

export const controlSendSmsApi = (params: { rcvPhone: string }) =>
  defHttpHasFactoryId.post({
    url: Api.CONTROL_SEND_SMS,
    params,
  });
export const controlCheckNeedSmsApi = (params: { rcvPhone: string }) =>
  defHttpHasFactoryId.post({
    url: Api.CONTROL_CHECK_NEED_SMS,
    params,
  });
export const controlVerifySmsCodeApi = (params: { rcvPhone: string; authCode: string }) =>
  defHttpHasFactoryId.post({
    url: Api.CONTROL_VERIFY_SMSCODE,
    params,
  });
