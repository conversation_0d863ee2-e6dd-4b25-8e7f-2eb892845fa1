<template>
  <div class="page h-full">
    <div :class="['content-container', { aoa3: isTabsLabel }]">
      <div class="tree-content" :class="{ 'hide-tree-container': !isShowTree, aoa3: isTabsLabel }">
        <div class="tit">生产指标</div>
        <Tree
          v-model:checked-keys="checkedKeys"
          :tree-data="indicatorTreeData"
          blockNode
          checkable
          defaultExpandAll
          @check="handleSelectNode"
          v-if="indicatorTreeData.length"
        >
          <template #title="node">
            <div style="overflow: hidden; text-overflow: ellipsis">
              <!-- <Tooltip>
                <template #title>{{ node.groupName || node.title }}</template>
                {{ node.groupName || node.title }}
              </Tooltip> -->
              <!-- {{ node.groupName || node.title }} -->
              <EllipsisTooltip :text="node.groupName || node.title" />
            </div>
          </template>
        </Tree>
        <div class="empty" v-if="indicatorTreeData.length <= 0"><HEmpty /></div>
      </div>
      <div
        class="toggle-tree"
        @click="handleChangeTreeShow"
        :style="{
          width: isShowTree ? '0' : '24px',
          borderRight: 'none',
          background: '#e1f3f1',
        }"
      >
        <div
          class="toggle-tree-btn"
          :style="{
            left: isShowTree ? '-24px' : '0',
            borderRadius: isShowTree ? '4px 0px 0px 4px' : '0px 4px 4px 0px',
            background: isShowTree ? '' : 'var(--theme-color)',
            borderColor: isShowTree ? '' : 'var(--theme-color)',
          }"
        >
          <LeftOutlined v-show="isShowTree" style="color: #999" />
          <RightOutlined v-show="!isShowTree" style="color: #fff" />
        </div>
      </div>
      <div ref="rightBoxRef" class="right-container">
        <div class="search-bar flex items-center">
          <SearchForm @change="Object.assign(params, $event)" />
          <div class="flex gap-12px items-center ml-12px">
            <Dropdown trigger="click">
              <IconButton
                icon="icon-park-outline:filter"
                :size="16"
                tooltip="筛选"
                hoverColor="rgba(17,144,120,0.16)"
              />
              <template #overlay>
                <Menu style="min-width: 120px">
                  <Menu.Item>
                    <Checkbox v-model:checked="compare" :disabled="merge">同比环比</Checkbox>
                  </Menu.Item>
                  <Menu.Item v-if="chartType === ChartType.LINE">
                    <Checkbox v-model:checked="merge">单图显示</Checkbox>
                  </Menu.Item>
                </Menu>
              </template>
            </Dropdown>
            <IconButton
              :icon="
                chartType == ChartType.LINE
                  ? 'icon-park-outline:table-file'
                  : 'icon-park-outline:chart-line'
              "
              :size="16"
              :tooltip="chartType == ChartType.LINE ? '表格' : '曲线'"
              hoverColor="rgba(17,144,120,0.16)"
              @click="handleTypeChange"
            />
            <IconButton
              icon="icon-park-outline:upload"
              :size="16"
              tooltip="导出"
              hoverColor="rgba(17,144,120,0.16)"
              :disabled="!Boolean(tableDataTotal)"
              @click="handleExport"
            />
          </div>
        </div>
        <div class="chart-wrapper" v-if="chartType === ChartType.LINE">
          <LineChartAxisPointerLink :api="getDataCurve" :params="params" v-if="compare && !merge" />
          <LineChart :api="getDataCurve" :params="params" v-else-if="!compare && !merge" />
          <LineMergeChart :api="getDataCurve" :params="params" v-if="merge" />
        </div>
        <div class="table-container" v-if="chartType === ChartType.TABLE">
          <IndicatorDataTable :api="dataCurvePageTable" :params="params" :compare="compare" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="ProductionCurve">
  import { onMounted, ref, reactive, watch, computed } from 'vue';
  import { Checkbox, Tree, Menu, Dropdown } from 'ant-design-vue';
  import { IconButton } from '/@/components/Button';
  import { EllipsisTooltip } from '/@/components/EllipsisTooltip';
  import {
    getGroupTreeWithIndicators,
    getDataCurve,
    dataCurvePageTable,
    dataCurveExport,
  } from '/@aoa/api/config-center/scenes-group';
  import HEmpty from '/@/components/HEmpty';

  import { groupBy } from 'lodash-es';
  import { downloadByData } from '/@/utils/file/download';
  import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
  import { flatMapDeep } from 'lodash-es';
  import dayjs from 'dayjs';
  import { debounce } from 'lodash-es';
  import { TimeType, ChartType } from './hooks/datas';
  import { LineChart, LineChartAxisPointerLink, LineMergeChart } from '/@aoa/components/LineChart';
  import { SearchForm } from '/@aoa/components/SearchForm';
  import { IndicatorDataTable, getTableParams } from '/@aoa/components/IndicatorDataTable';
  import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';

  const { getCardTabsType } = useMultipleTabSetting();
  const { getIsLabelType } = useMenuSetting();

  const compare = ref(false);
  const merge = ref(false);
  const params = reactive({
    startDateTime: '',
    endDateTime: '',
    timeType: TimeType.Day,
    indexCodes: '',
    groupCodes: '',
    interval: 1,
  });

  const chartType = ref(ChartType.LINE);

  const isTabsLabel = computed(() => getIsLabelType && getCardTabsType);

  const handleTypeChange = () => {
    chartType.value = chartType.value == ChartType.LINE ? ChartType.TABLE : ChartType.LINE;
  };

  const tableDataTotal = ref(0);
  const indicatorTreeData: any = ref([]);
  const checkedKeys = ref([]);
  const checkedIndicators: any = ref([]);
  const rightBoxRef = ref<HTMLElement | null>(null);
  const isShowTree = ref(true);
  const handleChangeTreeShow = () => {
    isShowTree.value = !isShowTree.value;
    if (rightBoxRef.value) {
      rightBoxRef.value.style.width = isShowTree.value ? 'calc(100% - 248px)' : 'calc(100% - 24px)';
    }
  };
  const setParams = () => {
    const paramsGroup = groupBy(checkedIndicators.value, 'groupCode');
    params.groupCodes = Object.keys(paramsGroup)
      .map((key) => key)
      .join(',');
    params.indexCodes = checkedIndicators.value.map((i) => i.indexCode).join(',');
  };

  const handleExport = async () => {
    const _params = getTableParams(params);
    const res = await dataCurveExport({
      ..._params,
      size: 10000,
    });

    let { startDateTime, endDateTime } = _params;
    startDateTime = dayjs(startDateTime).format('YYYY-MM-DD');
    endDateTime = dayjs(endDateTime).format('YYYY-MM-DD');
    downloadByData(res.data, `${startDateTime}-${endDateTime}指标数据.xlsx`);
  };

  const flattenTree = (tree) => {
    return flatMapDeep(tree, (node) => {
      const children = node.children ? flattenTree(node.children) : [];
      return [node, ...children];
    });
  };

  const setDefaultKeys = () => {
    const firstTree = indicatorTreeData.value[0]?.children || [];
    const flattenedArray = flattenTree(firstTree);
    const allIndexList = flattenedArray.filter((i) => i.indexCode);
    const firstGroupCode = allIndexList[0]?.groupCode;
    const indexList = allIndexList.length
      ? allIndexList.filter((i) => i.groupCode === firstGroupCode)
      : [];
    checkedKeys.value = indexList.map((i) => i.key);
    checkedIndicators.value = indexList.map((i) => ({
      indexCode: i.indexCode,
      groupCode: i.groupCode,
      resourceInterfaceId: i.resourceInterfaceId,
    }));

    if (indexList.length) {
      setParams();
    }
  };

  const throttledHandleSelectNode = debounce((e) => {
    const indexList = e.checkedNodes.filter((i) => i.indexCode);
    // if (indexList.length > 15) {
    //   checkedKeys.value = [];
    //   createMessage.error('最多选择15个指标');
    //   return;
    // }
    checkedIndicators.value = indexList.map((i) => ({
      indexCode: i.indexCode,
      groupCode: i.groupCode,
      resourceInterfaceId: i.resourceInterfaceId,
    }));

    if (indexList.length) {
      setParams();
    }
  }, 500);

  const handleSelectNode = (_, e) => {
    throttledHandleSelectNode(e);
  };

  const processTreeNodes = (data) => {
    data.forEach((node) => {
      node.title = node.groupName;
      node.key = node.groupCode;
      node.selectable = false;
      if (node.children && node.children.length > 0) {
        processTreeNodes(node.children);
      }
      if (node.indicators && node.indicators.length > 0) {
        node.children = node.children || [];
        node.children = node.children.concat(
          node.indicators.map((item) => ({
            title: item.name,
            key: node.groupCode + item.code,
            indexCode: item.code,
            groupCode: node.groupCode,
            resourceInterfaceId: node.defaultInterfaceId,
            selectable: false,
          })),
        );
      }
    });
  };

  // 查询指标树
  const queryIndicatorTree = async () => {
    const data = await getGroupTreeWithIndicators('shujuguanli-shengchanquxian');
    processTreeNodes([data]);
    if (data.id) {
      indicatorTreeData.value = [
        {
          ...data,
          title: data.groupName,
          key: data.groupCode,
          selectable: false,
        },
      ];
      // 设置默认选中值
      setDefaultKeys();
    }
  };

  const getTableData = () => {
    setTimeout(async () => {
      const _params = await getTableParams(params);
      const { data } = await dataCurvePageTable({ ..._params, size: 10 });
      tableDataTotal.value = data?.length;
    }, 200);
  };

  watch(
    () => params,
    () => {
      getTableData();
    },
    { deep: true },
  );

  onMounted(async () => {
    queryIndicatorTree();
  });
</script>

<style lang="less" scoped>
  .page {
    padding: 0 16px 16px;
    overflow: hidden;

    .content-container {
      display: flex;
      height: 100%;
      border: 1px solid @aoa3-join-border;

      &.aoa3 {
        border-radius: 4px;
        overflow: hidden;

        .tree-content {
          background: @aoa3-join-from-bg;
          border-radius: 0;

          .tit {
            border-bottom: 1px solid @aoa3-join-from-border;
          }
        }

        .right-container {
          border-left: 1px solid @aoa3-join-from-border;
          flex: 1;
          margin-left: 0;
          background-color: @aoa3-join-from-bg;
          border-radius: 0;
        }
      }
    }
  }

  .tree-content {
    width: 248px;
    background: #fff;
    border-radius: 4px;
    // max-height: calc(100vh - 56px - 60px - 16px);
    position: relative;
    flex-shrink: 0;
    transition: all 0.3s;

    &.hide-tree-container {
      width: 0;
      padding: 0;
      border-right: none;

      .tit {
        display: none;
      }
    }

    .tit {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid #d8d8d8;
      display: flex;
      align-items: center;
      padding: 0 12px;
      white-space: nowrap;
    }

    :deep(.ant-tree) {
      overflow-y: auto;
      height: calc(100% - 48px);
      padding: 12px;
      background: transparent;
      overflow-x: hidden;

      .ant-tree-treenode {
        height: 40px;
        align-items: center;
        padding: 0;

        .ant-tree-switcher {
          width: auto;
          min-width: 12px;
          display: flex;
          align-items: center;
        }

        .ant-tree-checkbox {
          margin-inline-end: unset;
          margin-block-start: unset;
          margin-left: 8px;
        }

        .ant-tree-node-content-wrapper {
          line-height: 40px;
          padding: 0 0 0 12px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .ant-tree-node-selected {
          background: none;
          color: var(--theme-color);
        }
      }
    }
  }

  .show-tree-content {
    width: 248px;
    left: 0 !important;
  }

  .resize {
    cursor: col-resize;
    position: absolute;
    top: 45%;
    left: 256px;
    background-color: #d6d6d6;
    border-radius: 999px;
    font-size: 32px;
    color: white;
    user-select: none;
  }

  .toggle-show {
    width: 24px;
    height: 32px;
    border-radius: 0 4px 4px 0;
    border: 1px solid #e9e9e9;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 45%;
    z-index: 999;
    display: none;
    transition: all 0.3s;

    .anticon {
      color: #333333;
      opacity: 0.65;
      margin-right: 0;
    }
  }

  .toggle-show-right {
    border-radius: 4px 0 0 4px;
    left: 220px;
  }

  .toggle-tree {
    position: relative;
    width: 24px;
    height: 100%;
    background: #fff;
    border-right: 1px solid #e9e9e9;
    cursor: pointer;

    .toggle-tree-btn {
      position: absolute;
      top: 50%;
      width: 24px;
      height: 32px;
      border-radius: 4px 0px 0px 4px;
      border: 1px solid #e9e9e9;
      transform: translateY(-50%);
      cursor: pointer;
      text-align: center;
      line-height: 30px;
      z-index: 999;
      background: white;
      transition: all 0.3s;

      .anticon {
        margin: 0;
      }
    }
  }

  .right-container {
    flex: 1;
    margin-left: 12px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;

    .search-bar {
      padding: 8px 16px;

      :deep(.ant-dropdown-open) {
        .vben-icon-button {
          color: @theme-color;
          background-color: @theme-color-12p;
        }

        .ant-tooltip {
          display: none;
        }
      }

      :deep(.vben-icon-button) {
        color: #666;
      }
    }

    .chart-wrapper,
    .table-container {
      width: 100%;
      height: calc(100% - 48px);
    }

    .table-container {
      :deep(.vben-basic-table) {
        .ant-table-wrapper {
          margin: 0;
          padding: 4px 0 0 0;
          border-bottom: none;
          border-left: none;
          border-right: none;
        }
      }
    }
  }

  .ant-btn-default:hover {
    background: #f8f8f8;
    color: #333;
    border: 1px solid #e9e9e9;
  }

  @media screen and (max-width: 1200px) {
    .tree-content {
      position: absolute;
      left: 0;
      z-index: 999;
      height: 100%;
    }

    .resize {
      display: none;
    }

    .toggle-show {
      display: flex;
    }

    .right-container {
      margin-left: 0;
    }
  }

  .empty {
    height: calc(100% - 72px);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .data-empty {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>

<style lang="less" scoped>
  .vben-page-wrapper {
    padding: 0 16px;
    overflow: hidden;
  }
</style>
