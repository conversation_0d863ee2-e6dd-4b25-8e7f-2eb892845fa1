<template>
  <div class="w-full h-full" ref="wrapperRef">
    <div class="topology-main" v-loading="spinning" loading-background="transparent">
      <div id="diagram-wrap">
        <div ref="topologyTwoRef" :id="wrapId" style="width: 100%; height: 100%"></div>
      </div>
      <VideoModal
        v-model:open="videoOpen"
        :width="modalWidth"
        :destroyOnClose="true"
        :data="detailData"
        :themeColor="themeColor"
        :fieldNames="{ label: 'displayName', value: 'playerUrl' }"
        :title="deviceName || '设备监控'"
        :bgImage="{
          header: headerBg,
          body: bodyBg,
          footer: footerBg,
        }"
      >
        <template #footer></template>
      </VideoModal>
      <IndicatorModal
        v-model:open="indicatorOpen"
        :width="isIPCRef ? '100%' : '1272px'"
        :bodyStyle="{ height: '670px' }"
        :destroyOnClose="true"
        :groupInfo="groupInfo"
        :themeColor="themeColor"
        :echartsConfig="echartsConfig"
        :multiple="multiple"
        :factoryId="factoryId"
        title="指标详情"
        :requestHeader="requestHeader"
        :bgImage="bgImage"
        :footer="modalFooter"
        :base-url="baseUrl"
        wrapClassName="aoa-ipc-modal-curve"
      />

      <Modal
        v-model:open="modelOpen"
        :width="modalWidth"
        :destroyOnClose="true"
        :title="deviceName"
        :themeColor="themeColor"
        :bgImage="bgImage"
        :footer="modalFooter"
      >
        <div
          id="topologyThree"
          :style="{ height: diagramHeight }"
          :class="classRef"
          ref="topologyThreeRef"
        ></div>
      </Modal>
      <ToolTip v-model:open="tooltipVisible" :data="tooltipData" :position="position" />
      <DiagnosticAnalysis
        v-model:open="analysisVisible"
        :data="analysisData"
        :position="analysisPosition"
      />

      <ControlSetting @register="registerControlSettingModal" />
      <PhoneVerificationModal
        @register="registerPhoneVerificationModal"
        @success="handlePhoneVerificationSuccess"
        @afterClose="handlePhoneVerificationAfterClose"
      />
      <FullScreen
        v-if="showFullScreenBtn && wrapperRef && flowId"
        class="absolute right-4 top-4 z-100"
        style="color: #fff"
        :el="wrapperRef"
        :flowId="flowId"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    onMounted,
    onBeforeUnmount,
    onActivated,
    onDeactivated,
    nextTick,
    watch,
    computed,
    toRaw,
  } from 'vue';
  import { useModal } from '/@/components/Modal';
  import ControlSetting from '/@process-editor/components/CusModal/ControlSetting.vue';
  import PhoneVerificationModal from '/@process-editor/components/CusModal/PhoneVerificationModal.vue';
  import { onBeforeRouteLeave } from 'vue-router';
  import { IndicatorModal, VideoModal, Modal } from 'hlxb-business-ui';
  import ToolTip from '../ToolTip.vue';
  import DiagnosticAnalysis from '../DiagnosticAnalysis.vue';
  import {
    getProcessData,
    getMeta2dData,
    pollingObserver,
    inputChangeEvent,
    kitsPolling,
    updateKits,
    penInit,
    // onMeta2dSizeChange,
  } from '../../core/common';
  import { Meta2dInstance } from '../../core/instance';
  import { getTheme, isImgPen, linkImgPenFunc } from '../../core/share';
  import { KitsV2 } from '../../core/kits/render';
  import { getCurrentPlatformIdApi } from '../../api/index';
  import { createLocalStorage } from '/@/utils/cache';
  import { PLATFORM_KEY } from '/@/enums/cacheEnum';
  import {
    PRODUCT_DATA_KIT,
    PRODUCT_CONTROL_KIT,
    TABLE_KIT,
    VIDEO_KIT,
    KIT_INDEX_BORDER_TITLE,
    TABLE_KIT_INDEX_BORDER_TITLE,
    KIT_INDEX_VALUE,
    TABLE_KIT_INDEX_VALUE,
    DIAGNOSTIC_ANALYSIS_EVENT,
    DIAGNOSTIC_MOUSELEAVE_EVENT,
    CONTROL_INDEX_VALUE,
  } from '../../core/kits/constant';
  import { pointInRect } from 'hlxb-meta2d-core';

  import type { Meta2d, Meta2dData, Pen } from 'hlxb-meta2d-core';
  import type { KitsV2 as KitIns } from '../../core/kits/render';
  import headerBg from '../../assets/images/header.png';
  import bodyBg from '../../assets/images/body.png';
  import footerBg from '../../assets/images/footer.png';
  import { getToken } from '/@/utils/auth';
  import { useDomain } from '/@/locales/useDomain';
  import { buildShortUUID } from '/@/utils/uuid';
  import { isIPCBreakPoint } from '/@/hooks/event/useBreakpoint';
  import { cloneDeep } from 'lodash-es';
  import { useWindowSize } from '@vueuse/core';
  import { getFactoryId } from '/@process-editor/utils';
  import { defZhczHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { parseJson } from '/@process-editor/utils/index';
  import { getAppEnvConfig } from '/@/utils/env';
  import { sleep } from '/@/utils/index';
  import { getProcessEditorTheme } from '/@process-editor/assets/theme';
  import { DEVICE_CHANGE_SWITCH_API } from '/@process-editor/constant';
  import { controlCheckNeedSmsApi } from '/@process-editor/api/index';
  // import { usePermissionStore } from '/@/store/modules/permission';
  import { useUserStore } from '/@/store/modules/user';
  import FullScreen from '../FullScreen.vue';

  const userInfo = useUserStore().getUserInfo;
  const { themeColor, echartsConfig } = getProcessEditorTheme();
  const { createMessage, createConfirm } = useMessage();
  // const permissionStore = usePermissionStore();

  defineOptions({
    name: 'ProcessBoardOverview',
  });

  const props = defineProps({
    flowId: { type: String, required: true },
    showFullScreenBtn: { type: Boolean, default: false },
  });

  const emit = defineEmits([
    'navigation-event',
    'getModalParams',
    'afterApiEventSuccess',
    'metaInsInit',
  ]);

  const wrapperRef = ref<HTMLDivElement>();
  const topologyTwoRef = ref<HTMLDivElement>();
  let metaIns: Meta2d | null = null;

  const spinning = ref(false);

  const { isIPCRef } = isIPCBreakPoint();
  const { height } = useWindowSize();
  const modalWidth = computed(() => (isIPCRef.value ? '100%' : '70%'));

  const modalFooter = computed(() => {
    return isIPCRef.value ? null : undefined;
  });
  const bgImage = computed(() => {
    return isIPCRef.value ? {} : { header: headerBg, body: bodyBg, footer: footerBg };
  });
  const diagramHeight = computed(() => {
    return isIPCRef.value ? `${height.value - 64}px` : '700px';
  });
  const baseUrl = computed(() => getAppEnvConfig().VITE_GLOB_API_URL);

  const wrapId = ref(randomId());
  function randomId(prefix = 'topologyTwo', len = 16) {
    return `${prefix}-${Math.random()
      .toString(16)
      .slice(2, len + 2)}`;
  }

  const meta2dData = ref<Meta2dData>();
  const meta2dDataRaw = ref<Meta2dData>();
  const kitData = ref<Recordable[]>([]);

  const version = ref('');
  const scale = ref(1);
  const isClickCar = ref(false);
  const refreshTime = ref(10000);

  async function init() {
    metaIns = Meta2dInstance(wrapId.value);
    metaIns.setOptions({ hoverCursor: 'default' });
    loading();
    const hData = await getProcessData(props.flowId);
    kitIns = new KitsV2(metaIns, hData.flowData.flowDataJson, 'view');
    const mData = await getMeta2dData(kitIns, hData.flowData.flowDataJson, hData.businessData);
    kitIns.setTheme(getTheme(mData.background || '#fff'));
    updateData(mData, hData);
    onMeta2dEvent(metaIns);
    // onMeta2dSizeChange(wrapperRef.value, metaIns);
    if (metaIns) {
      let options = cloneDeep(mData);
      metaIns.open(options);
      metaIns.resize();
      let viewPadding = options?.viewPadding;
      viewPadding = viewPadding ? Number(viewPadding) : 10;
      metaIns.fitView(true, viewPadding);
      meta2dOpened(options);
      emit('metaInsInit', metaIns);
    }
  }

  function meta2dOpened(options) {
    let pens = options.pens || [];
    let videosPen = pens.filter((i) => i.name === 'video');
    for (let pen of videosPen.values()) {
      let videoEl = pen.calculative.media;
      if (!videoEl) return;
      let videoBoxEl = videoEl.parentElement;
      if (!videoBoxEl) return;
      // 视频移上去出现声音图标，覆盖le5le事件，后续优化
      videoBoxEl.onmouseenter = null;
      if (pen.noPause) {
        videoBoxEl.onclick = (e) => e.preventDefault();
      }
    }
  }

  function updateData(mData: Meta2dData, hData: { flowData: any; businessData: Recordable[] }) {
    meta2dData.value = mData;
    meta2dDataRaw.value = mData;
    kitData.value = hData.businessData;

    const flowDataJson = hData.flowData.flowDataJson;
    refreshTime.value = flowDataJson.refreshTime || 10000;
    scale.value = flowDataJson.scale || 1;

    setTimeout(() => {
      penInit(metaIns!, kitData.value);
    }, 200);
  }

  function loading() {
    spinning.value = true;

    if (metaIns) {
      metaIns.on('opened', () => {
        spinning.value = false;
      });
    }
  }

  const factoryId = getFactoryId();
  async function updateLocalStore() {
    const platform = await getCurrentPlatformIdApi();
    const ls = createLocalStorage();
    ls.set(PLATFORM_KEY, platform.id);
  }

  function onMeta2dEvent(metaIns: Meta2d) {
    if (!metaIns) return;
    metaIns.on('click', (e) => {
      clickCarEvent(e);
      if (!e.pen) return;

      indicatorDetailEvent(e.pen);
      deviceDetailEvent(e.pen);
      videoEvent(e.pen);
      navigationEvent(e.pen);
      tooltipEvent(e.pen);
      if (!isClickCar.value) {
        extEvent(e.pen);
      }
      productControlEvent(e.pen);
    });

    onMeta2dEmit();
    inputChangeEvent(metaIns, 'topology-main');
  }
  /* 其他的点击事件处理 */
  async function extEvent(pen) {
    // 触发获取弹窗参数事件
    if (pen.disabled) return;
    if (pen._isModal && pen.modalForm?.title) {
      onGetModalParams(pen.modalForm, pen);
      emit('getModalParams', pen.modalForm);
    }

    if (!pen.modalForm?.title && pen._isApi && !pen?.apiForm?.disabled && pen?.apiForm?.url) {
      if (pen.isVerifyPhone) {
        let isPass = await verifyPhone(pen);
        if (!isPass) return;
        commonKitControlReq(pen);
      } else {
        createConfirm({
          iconType: 'info',
          title: '提示',
          content: '是否确定发送指令？',
          async onOk() {
            commonKitControlReq(pen);
          },
        });
      }
    }
  }

  // 手机号码验证
  async function verifyPhone(pen) {
    if (!pen.isVerifyPhone) return true;
    let isNeedSms = userInfo?.phone
      ? await controlCheckNeedSmsApi({ rcvPhone: userInfo.phone })
      : true;

    if (isNeedSms) {
      openPhoneVerificationModalFn(true, {});
      ({ resolve: verifyPhoneResolve, promise: verifyPhonePromise } = Promise.withResolvers());
      return verifyPhonePromise;
    } else {
      return true;
    }
  }

  // 通用套件控制下发请求
  async function commonKitControlReq(pen) {
    let { url, params } = pen.apiForm;
    params = parseJson(params);
    const imagePenFlag = isImgPen(pen);

    try {
      if (imagePenFlag) {
        params.changeVal = pen._imgStatus == 1 ? params.unCheckVal : params.checkVal;
      }
      const afterFetchPipelineFlow = params?.afterFetchPipelineFlow;
      delete params.afterFetchPipelineFlow;
      const afterSuccessParams = params?.afterSuccessParams;
      delete params.afterSuccessParams;
      await defZhczHttp.post({ url, params: { ...params } });

      emit('afterApiEventSuccess', { ...params, afterSuccessParams });

      if (imagePenFlag) {
        await sleep(200);
        if (params.pipleFlows) {
          const pipleFlows = params.pipleFlows;
          const _playback = !pipleFlows[params.changeVal];

          metaIns?.setValue({ id: pen.id, _playback });
          delete params.pipleFlows;
        }

        metaIns?.setValue({ id: pen.id, _imgStatus: +params.changeVal });
      } else {
        metaIns?.setValue({ id: pen.id, _playback: !afterFetchPipelineFlow });
      }
      linkImgPenFunc(pen, metaIns!);
      setTimeout(async () => {
        await updateKits(kitIns as KitsV2, props.flowId, version.value, scale.value);
      }, 2000);

      createMessage.success('指令已下发');
    } catch (e) {
      console.error(e);
      createMessage.error('指令下发失败');
    }
  }

  function isClickAllowCar(e) {
    if (!metaIns) return false;
    if (isImgPen(e.pen)) {
      return e.pen._pointer;
    }
    return !!e.pen;
  }
  function clickCarEvent(e) {
    isClickCar.value = false;
    if (isClickAllowCar(e)) {
      return;
    }

    const pens = metaIns?.store.data.pens;

    pens?.forEach((pen: Pen) => {
      if (pen.imageRect && pen.animatePercent) {
        const { x = 0, y = 0, width = 16, height = 16 } = pen.imageRect || {};
        const expandedRect = {
          x: x - width,
          y: y - height,
          width: width * 2, // 总宽度增加 20px
          height: height * 2, // 总高度增加 20px
        };
        const calcX = e.x;
        const calcY = e.y;

        const ret = pointInRect({ x: calcX, y: calcY }, expandedRect);

        isClickCar.value = ret;

        if (ret) {
          // 车辆点击事件
          extEvent(pen);
        }
      }
    });
  }

  const tooltipVisible = ref(false);
  const tooltipData = ref({});
  const position = ref({
    left: 0,
    top: 0,
  });

  const analysisVisible = ref(false);
  const analysisData = ref({});
  const analysisPosition = ref({
    left: 0,
    top: 0,
  });

  function getPosition(pen) {
    if (!pen) return { left: 0, top: 0 };
    const store = pen.calculative.canvas.store;
    const worldRect = pen.calculative.worldRect;
    let pos = {
      left: worldRect.x + store.data.x + worldRect.width / 2,
      top: worldRect.y + store.data.y,
    };

    return pos;
  }

  function getAnalysisPosition(pen) {
    if (!pen) return { left: 0, top: 0 };
    const store = pen.calculative.canvas.store;
    const worldRect = pen.calculative.worldRect;
    const textDrawRect = pen.calculative.textDrawRect;

    let pos = {
      left: worldRect.x + store.data.x + textDrawRect.width / 2,
      top: worldRect.y + store.data.y,
    };

    return pos;
  }

  function onMeta2dEmit() {
    if (!metaIns) return;
    metaIns.on('pen-enter', (e) => {
      const pen = e.pen;
      if (!pen.groupCode || !pen.resourceInterfaceId) return;
      tooltipVisible.value = true;
      tooltipData.value = pen;

      const pos = getPosition(pen);
      position.value = {
        left: pos.left,
        top: pos.top,
      };
    });

    metaIns.on('valve-click', (e) => {
      const pen = e.pen;
      if (pen._isApi && !pen?.apiForm?.disabled && pen?.apiForm?.url == DEVICE_CHANGE_SWITCH_API) {
        return;
      }
      extEvent(pen);
    });

    metaIns.on(DIAGNOSTIC_ANALYSIS_EVENT, (e) => {
      console.log(
        '%ce===>428： ',
        'background: rgb(25, 197, 237,.6); color: #ff5025; font-size:18px;font-weight:700',
        e,
      );

      metaIns &&
        metaIns.setOptions({
          hoverCursor: 'pointer',
        });
      const pen = e.pen;
      if (!pen.rawData) return;
      if (pen.rawData.isWarning) {
        analysisVisible.value = true;
        analysisData.value = { ...pen, ...pen.rawData };
        const pos = getAnalysisPosition(pen);

        analysisPosition.value = {
          left: pos.left,
          top: pos.top,
        };
      }
    });

    metaIns.on(DIAGNOSTIC_MOUSELEAVE_EVENT, (e) => {
      // 鼠标移出事件
      metaIns &&
        metaIns.setOptions({
          hoverCursor: 'default',
        });
      console.log('鼠标移出', e.pen);
      analysisVisible.value && (analysisVisible.value = false); // 隐藏 diagnostic-analysis 弹窗
    });
  }

  function tooltipEvent(pen) {
    const events = pen.events || [];
    if (!pen.groupCode || !pen.resourceInterfaceId || events.every((i) => i.name !== 'click'))
      return;

    tooltipVisible.value = true;
    tooltipData.value = pen;

    const pos = getPosition(pen);
    position.value = {
      left: pos.left,
      top: pos.top,
    };
  }

  async function productControlEvent(pen) {
    if (pen.categoryCN !== CONTROL_INDEX_VALUE) return;
    let { scriptResult = {}, controlUrl } = pen.rawData;
    scriptResult = parseJson(scriptResult, 'object');
    let changeVal = scriptResult?.changeVal;
    if ([null, undefined, ''].includes(changeVal) || !controlUrl) return;
    let params = {
      indicatorCodes: [pen.rawData.code],
      changeVal,
    };
    createConfirm({
      iconType: 'info',
      title: '提示',
      content: '是否确定发送指令？',
      // wrapClassName: 'zoology-wzb-custom-confirm-dialog',
      async onOk() {
        try {
          await defZhczHttp.post({
            url: controlUrl,
            params,
          });
          metaIns?.setValue({ id: pen.id, checked: !pen.checked });
          setTimeout(
            () => updateKits(kitIns as KitsV2, props.flowId, version.value, scale.value),
            2000,
          );
          createMessage.success('指令已下发');
        } catch (error) {
          createMessage.error('指令下发失败');
        }
      },
    });
  }

  const { getTenantId } = useDomain();
  const requestHeader: any = {
    Authorization: getToken(),
    'Tenant-Id': getTenantId.value,
  };
  const indicatorOpen = ref(false);
  const groupInfo = ref({
    groupCode: 'zhinengbi-shengchanshuju',
    resourceInterfaceId: '3',
    jsConvert: false,
    indexCodes: 'Smart.M580.FROM_CS.PH',
  });
  const multiple = ref(false);
  function indicatorDetailEvent(pen: Recordable) {
    const data = pen.rawData;
    const types = [PRODUCT_DATA_KIT, TABLE_KIT, PRODUCT_CONTROL_KIT];
    const arr = [!types.includes(pen.category), !data, pen?.input, pen?.isShowIndicator];

    if (arr.some(Boolean)) return;

    const categoryCNArr = [
      KIT_INDEX_BORDER_TITLE,
      // TABLE_KIT_INDEX_BORDER_TITLE,
      KIT_INDEX_VALUE,
      TABLE_KIT_INDEX_VALUE,
    ];
    if (categoryCNArr.includes(pen.categoryCN)) {
      if (pen.category === PRODUCT_DATA_KIT) {
        multiple.value = pen.categoryCN === KIT_INDEX_BORDER_TITLE;
      } else if (pen.category === TABLE_KIT) {
        multiple.value = pen.categoryCN === TABLE_KIT_INDEX_BORDER_TITLE;
      }
      console.log('-----> data', data);
      groupInfo.value.groupCode = data.groupCode;
      groupInfo.value.indexCodes = data.code;
      groupInfo.value.resourceInterfaceId =
        data?.resourceInterfaceId || groupInfo.value.resourceInterfaceId;

      indicatorOpen.value = true;
    }
  }

  const detailData = ref<Meta2dData>();
  const deviceName = ref('');
  const videoOpen = ref(false);
  function videoEvent(pen) {
    if (pen.category !== VIDEO_KIT) return;

    // 单视频、多视频套件详情
    const kitId = pen.kitId;
    const findItem = kitData.value.find((i) => i.id === kitId);
    deviceName.value = findItem?.kitDisplayName || '';
    detailData.value = findItem?.businessData || {};
    videoOpen.value = true;
  }

  const modelOpen = ref(false);
  const topologyThreeRef = ref<HTMLDivElement>();
  const _flowData_ = ref({});

  let pauseFn = () => {};
  let resumeFn = () => {};
  let meta2dDetailIns: Meta2d | null = null;
  let kitIns: KitIns | null = null;
  const classRef = ref(`meta2d${buildShortUUID()}`);
  let flag1 = false;
  let flag2 = false;
  watch(
    () => modelOpen.value,
    async (newVal) => {
      await nextTick();
      if (newVal) {
        resumeFn();
      } else {
        pauseFn();

        meta2dDetailIns?.clear();
        meta2dDetailIns?.destroy();
        meta2dDetailIns = null;
      }
    },
  );

  function polling(kitIns: KitIns, data) {
    const kitParams = {
      kitIns,
      flowId: data.flowId,
      version: data.version,
      scale: data.flowDataJson.scale || 1,
    };
    const pausable = kitsPolling(kitParams, 1000 * 20);

    pauseFn = pausable.pause;
    resumeFn = pausable.resume;
  }
  async function deviceDetailEvent(pen) {
    if (pen.showDetail && pen['operation'] !== 'navigation') {
      const hData = await getProcessData(pen.showDetail);
      _flowData_.value = hData.flowData;
      const mData = await getMeta2dData(
        kitIns as KitIns,
        hData.flowData.flowDataJson,
        hData.businessData,
      );
      detailData.value = mData;
      deviceName.value = pen.name === 'text' ? pen.text : pen.name;
      modelOpen.value = true;
      if (topologyThreeRef.value) {
        topologyThreeRef.value.innerHTML = '';
      }

      await nextTick(() => {
        meta2dDetailIns = Meta2dInstance('topologyThree');
        kitIns = new KitsV2(meta2dDetailIns, hData.flowData.flowDataJson, 'view');
        meta2dDetailIns.open(mData);
        if (kitIns) {
          kitIns && kitIns.setTheme(getTheme(mData.background || '#fff'));
          if (pen.text === '参数汇总' && !flag1) {
            polling(kitIns, _flowData_.value);
            flag1 = true;
          } else if (pen.text === '参数设定' && !flag2) {
            polling(kitIns, _flowData_.value);
            flag2 = true;
          }
        }
        inputChangeEvent(meta2dDetailIns, classRef.value);
      });
    }
  }

  function navigationEvent(pen) {
    if (!pen.showDetail || pen['operation'] !== 'navigation') return;
    emit('navigation-event', pen);
  }

  let stopFn = () => {};
  onMounted(async () => {
    // indicatorDisplayScript
    await nextTick(() => {});
    await updateLocalStore();
    if (wrapperRef.value?.offsetWidth && wrapperRef.value?.offsetHeight) {
      try {
        await init();
        if (!metaIns) return;
        stopFn?.();
        stopFn = pollingObserver(
          {
            kitIns: kitIns as KitIns,
            flowId: props.flowId,
            version: version.value,
            scale: scale.value,
          },
          refreshTime.value,
          wrapperRef,
        );
      } catch (error) {
        console.log('error', error);
        spinning.value = false;
        stopFn();
      }
    }
    handleLoaded();
  });

  function meta2dDestroy(ins: Meta2d | null) {
    if (!ins) {
      console.log('销毁失败, 当前meta2d实例为null');
      return undefined;
    }

    ins?.clear();
    ins?.destroy();
    metaIns = null;
  }
  onBeforeUnmount(() => {
    clearPageData();
  });

  watch(
    () => props.flowId,
    async () => {
      clearPageData();
      if (topologyTwoRef.value) {
        topologyTwoRef.value.innerHTML = '';
      }
      await nextTick();
      await init();
      if (!metaIns) return;
      stopFn = pollingObserver(
        {
          kitIns: kitIns as KitIns,
          flowId: props.flowId,
          version: version.value,
          scale: scale.value,
        },
        refreshTime.value,
        wrapperRef,
      );
    },
  );

  async function handleLoaded() {
    if (meta2dDataRaw.value && !meta2dData.value) {
      if (topologyTwoRef.value) {
        topologyTwoRef.value.innerHTML = '';
      }
      await nextTick();
      metaIns = Meta2dInstance(wrapId.value);
      meta2dData.value = meta2dDataRaw.value;
      onMeta2dEvent(metaIns);
      let options = cloneDeep(toRaw(meta2dData.value));
      metaIns.open(options);
      metaIns.resize();
      let viewPadding = options?.viewPadding;
      viewPadding = viewPadding ? Number(viewPadding) : 10;
      metaIns.fitView(true, viewPadding);
      meta2dOpened(options);
    }
  }
  onActivated(() => {
    handleLoaded();
  });

  onDeactivated(() => {
    clearPageData();
  });

  onBeforeRouteLeave(() => {
    clearPageData();
  });
  function clearPageData() {
    meta2dDestroy(metaIns);
    meta2dData.value = undefined;
    spinning.value = false;
    stopFn();
  }

  // 注册下控弹窗
  const [registerControlSettingModal, { openModal: openControlSettingModalFn }] = useModal();

  // 注册手机验证码弹窗
  const [registerPhoneVerificationModal, { openModal: openPhoneVerificationModalFn }] = useModal();

  let verifyPhonePromise = null;
  let verifyPhoneResolve = null;
  // 手机验证码验证成功处理
  const handlePhoneVerificationSuccess = () => {
    verifyPhoneResolve && verifyPhoneResolve(true);
  };
  const handlePhoneVerificationAfterClose = () => {
    verifyPhoneResolve && verifyPhoneResolve(false);
  };
  // 下控设置，前置验证
  async function openControlSettingCheckFn(v, pen) {
    let isPass = await verifyPhone(pen);
    if (!isPass) return;
    openControlSettingModalFn(true, v);
  }

  const onGetModalParams = (v, pen) => {
    switch (v.route) {
      case 'ControlSetting':
        openControlSettingCheckFn(v, pen);
        break;
    }
  };
</script>

<style lang="less">
  .hlxb-modal-wrap {
    .ant-modal {
      @media (max-width: 1024px) {
        margin: 0;
        max-width: 100vw;
        padding-bottom: 0;
      }

      .ant-modal-close {
        width: 22px;
        height: 22px;

        .ant-modal-close-x {
          width: auto;
          height: auto;
          display: block;
          font-size: 16px;
          font-style: normal;
          line-height: 22px;
          text-align: center;
          text-transform: none;
          text-rendering: auto;
        }
      }

      .ant-modal-content {
        .ant-modal-body {
          padding: 0 24px 24px !important;

          @media (max-width: 1024px) {
            padding: 0 !important;
          }
        }
      }
    }
  }

  .hlxb-modal-wrap--dark {
    .ant-modal {
      @media (max-width: 1024px) {
        .ant-modal-content {
          .ant-modal-header {
            background-color: #041946;
          }
        }
      }

      .ant-modal-close-x {
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }

      .ant-tree {
        .ant-tree-title {
          color: inherit;
        }
      }

      .ant-select-selection-item {
        color: inherit;
      }

      span.anticon:not(.app-iconify) {
        font-size: inherit;
        margin-right: 0;
        vertical-align: inherit;
      }
    }
  }

  .aoa-ipc-modal-curve {
    .ant-modal {
      @media (max-width: 1024px) {
        .ant-modal-content {
          .ant-modal-header {
            background-color: inherit;
          }
        }
      }

      .ant-spin-nested-loading {
        .ant-spin-container::after {
          display: none;
        }
      }

      .ant-table-thead {
        & > tr {
          :where(.ant-table-cell) {
            color: #fff !important;
          }
        }
      }

      .ant-table .ant-table-row .ant-table-cell {
        color: #fff !important;
      }

      .ant-pagination {
        .ant-pagination-item {
          border-color: transparent !important;

          &.ant-pagination-item-active {
            border: 1px solid #2d82fe !important;
          }

          a {
            color: #fff !important;
          }

          &:hover {
            background-color: transparent !important;

            a {
              color: #fff !important;
            }
          }
        }

        .ant-pagination-jump-next,
        .ant-pagination-jump-prev,
        .ant-pagination-next,
        .ant-pagination-prev {
          border-color: transparent !important;

          .ant-pagination-item-ellipsis,
          .anticon {
            color: #fff !important;
          }

          &:hover {
            background-color: transparent !important;

            .ant-pagination-item-ellipsis,
            .anticon {
              color: #fff !important;
            }
          }
        }
      }
    }
  }

  /* 设置全局二次确认弹框样式 */
  .zoology-wzb-custom-confirm-dialog {
    .ant-modal-content {
      background: linear-gradient(180deg, #001f67 1%, #003372 100%);

      .ant-modal-confirm-title,
      .ant-modal-confirm-content {
        color: #fff !important;
      }

      .ant-modal-close,
      .ant-modal-close-x {
        &:hover {
          background-color: unset !important;
          opacity: 0.5;
        }

        svg path {
          fill: #fff;
        }
      }

      .ant-modal-confirm-body-wrapper {
        .ant-modal-confirm-btns {
          .ant-btn {
            &:hover {
              opacity: 0.5;
            }

            &.ant-btn-default {
              background: unset !important;
              color: #fff !important;

              &:hover {
                background-color: unset !important;
              }
            }

            &.ant-btn-primary {
              background-color: #0b62cb !important;
            }
          }
        }
      }
    }
  }
</style>

<style lang="less" scoped>
  // :deep(.ant-modal-confirm-confirm) {
  // }
  @font-face {
    font-family: YouSheBiaoTiHei;
    src: url('/@/assets/fonts/YouSheBiaoTiHei/YouSheBiaoTiHei-2.ttf');
  }

  @font-face {
    font-family: D-DIN-PRO;
    src: url('/@/assets/fonts/D-DIN-PRO/D-DIN-PRO-400-Regular.otf');
  }

  .topology-main {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: visible;

    .spin-wrapper {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      z-index: 100;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  #diagram-wrap {
    height: 100%;
    width: 100%;
  }
</style>
