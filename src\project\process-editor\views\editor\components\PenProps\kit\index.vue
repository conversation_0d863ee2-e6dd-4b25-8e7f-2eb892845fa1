<template>
  <div class="kit">
    <Row style="padding: 8px 16px" v-if="!isItem">
      <Col :span="8" style="line-height: 32px">套件类型</Col>
      <Col :span="16">
        <Select
          v-model:value="kitType"
          optionLabelProp="label"
          style="width: 100%"
          @change="initStateData"
          :allowClear="!!kitType"
        >
          <SelectOption
            v-for="kit in kitOptions"
            :key="kit.value"
            :value="kit.value"
            :label="kit.label"
          >
            <Popover
              :title="kit.label"
              trigger="hover"
              placement="left"
              autoAdjustOverflow
              v-if="kit.TipsComponents"
            >
              <template #content>
                <component :is="kit.TipsComponents" />
              </template>
              <div style="transform: translateX(-10px)">
                <div style="margin-left: 10px">{{ kit.label }}</div>
              </div>
            </Popover>
            <span v-else>{{ kit.label }} </span>
          </SelectOption>
        </Select>
      </Col>
    </Row>
    <a-collapse
      :bordered="false"
      ghost
      v-model:activeKey="activeKey"
      expand-icon-position="right"
      class="my-collapse"
    >
      <div>
        <!-- {{ item.businessData }} -->
        <!-- 模型id-{{ detailModelId }} -->
        <!-- {{ tables[0]?.businessData.displayTableDataInfos[0] }} -->
        <!-- {{ liquidLevel[0]?.businessData.indicator }} -->
      </div>
      <template v-if="kitType === PRODUCT_DATA_KIT">
        <a-collapse-panel
          v-for="(production, index) in productions"
          :key="`production${index + 1}`"
          header="生产套件"
        >
          <template #extra>
            <delete-outlined v-show="index > 0" @click="deleteKit(index, PRODUCT_DATA_KIT)" />
          </template>
          <ProductionKit
            v-model:data="productions[index]"
            :formList="[{ data: production }]"
            :index="index"
            :activePen="activePen"
            @change-view-mode="onChangeViewMode"
          />
        </a-collapse-panel>
      </template>
      <template v-if="kitType === PRODUCT_CONTROL_KIT">
        <a-collapse-panel
          v-for="(productionControl, index) in productionControls"
          :key="`productionControl${index + 1}`"
          header="生产下控套件"
        >
          <template #extra>
            <delete-outlined v-show="index > 0" @click="deleteKit(index, PRODUCT_CONTROL_KIT)" />
          </template>
          <ProductionControlKit
            v-model:data="productionControls[index]"
            :formList="[{ data: productionControl }]"
            :index="index"
            :activePen="activePen"
            @change-view-mode="onChangeViewMode"
          />
        </a-collapse-panel>
      </template>
      <template v-if="kitType === TABLE_KIT">
        <a-collapse-panel
          v-for="(table, index) in tables"
          :key="`table${index + 1}`"
          header="table套件"
        >
          <TableKit
            v-model:data="tables[index]"
            :formList="[{ data: table }]"
            :index="index"
            :activePen="activePen"
            @change-view-mode="onChangeViewMode"
          />
        </a-collapse-panel>
      </template>
      <template v-if="kitType === CHART_KIT">
        <a-collapse-panel
          v-for="(chart, index) in charts"
          :key="`chart${index + 1}`"
          header="chart套件"
        >
          <ChartKit
            v-model:data="charts[index]"
            :formList="[{ data: chart }]"
            :index="index"
            :activePen="activePen"
            @change-view-mode="onChangeViewMode"
          />
        </a-collapse-panel>
      </template>
      <template v-if="kitType === VIDEO_KIT">
        <a-collapse-panel
          v-for="(video, index) in videos"
          :key="`video${index + 1}`"
          header="视频套件"
        >
          <template #extra>
            <delete-outlined v-show="index > 0" @click="deleteKit(index, VIDEO_KIT)" />
          </template>
          <VideoKit
            v-model:data="videos[index]"
            :formList="[{ data: video }]"
            :index="index"
            :activePen="activePen"
            @change-view-mode="onChangeViewMode"
          />
        </a-collapse-panel>
      </template>
      <template v-if="kitType === LIQUID_LEVEL_KIT">
        <a-collapse-panel key="liquidLevel1" header="液位套件">
          <LiquidLevelKit
            v-model:data="liquidLevel"
            :formList="[{ data: liquidLevel }]"
            :activePen="activePen"
            :update="liquidLevelFlag"
            @change-view-mode="onChangeViewMode"
          />
        </a-collapse-panel>
      </template>
      <template v-if="kitType === PIPELINE_KIT">
        <a-collapse-panel key="pipeline1" header="管线套件">
          <PipelineKit
            v-model:data="pipeLine"
            :formList="[{ data: pipeLine }]"
            :activePen="activePen"
            :update="pipelineFlag"
            @change-view-mode="onChangeViewMode"
          />
        </a-collapse-panel>
      </template>
      <template v-if="kitType === COMMON_KIT">
        <a-collapse-panel key="common" header="数据套件">
          <CommonKit
            v-model:data="common"
            :activePen="activePen"
            :update="commonFlag"
            v-model:script="common.businessData.script"
            v-model:productDataInfos="common.businessData.productDataInfos"
            :key="activePen.id"
          />
        </a-collapse-panel>
      </template>
      <a-collapse-panel key="item1" header="小元件" v-if="isItem">
        <ItemKit
          v-model:data="item"
          :formList="[{ data: item }]"
          :activePen="activePen"
          :update="itemFlag"
          @change-view-mode="onChangeViewMode"
        />
      </a-collapse-panel>
    </a-collapse>
    <div class="action-wrap">
      <a-button class="save-btn" type="primary" @click="save">更新</a-button>
    </div>
  </div>
</template>

<script>
  import { Collapse, CollapsePanel, Select, Row, Col, SelectOption, Popover } from 'ant-design-vue';
  import { DeleteOutlined } from '@ant-design/icons-vue';
  import ProductionKit from './ProductionKit.vue';
  import ProductionControlKit from './ProductionControlKit.vue';
  import VideoKit from './VideoKit.vue';
  import ItemKit from './ItemKit.vue';
  import TableKit from './table-kit/index.vue';
  import ChartKit from './chart-kit/index.vue';
  import LiquidLevelKit from './liquid-level-kit/index.vue';
  import PipelineKit from './pipeline-kit/index.vue';
  import CommonKit from './common-kit/index.vue';
  import { FLOW_VERSION, CANVAS_DATA } from '/@process-editor/constant/process';
  import {
    PRODUCT_DATA_KIT,
    PRODUCT_CONTROL_KIT,
    TABLE_KIT,
    CHART_KIT,
    VIDEO_KIT,
    LIQUID_LEVEL_KIT,
    PIPELINE_KIT,
    COMMON_KIT,
  } from '/@process-editor/core/kits/constant';
  import { kitOptions } from './kit.data';
  import { KIT_ENUM } from '/@/project/process-editor/constant';

  const ACollapse = Collapse;
  const ACollapsePanel = CollapsePanel;

  export {};
</script>

<script setup>
  import { reactive, ref, toRefs, onMounted, watch, toRaw, unref } from 'vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep, isEmpty } from 'lodash-es';
  import {
    initProductionData,
    initProductionControlData,
    initVideoData,
    initItemData,
    initTableData,
    initChartData,
    initLiquidLevelData,
    initPipelineData,
    initCommonData,
  } from './kit.data';
  import { KitsV2 } from '/@process-editor/core/kits/render';
  import { useProcess } from '/@process-editor/hooks/useProcess';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    saveShowModelDetailApi,
    getShowModelDetailApi,
    previewDefaultBusinessDataApi,
    getCurrentPlatformIdApi,
  } from '/@process-editor/api/index';
  import {
    getFactoryId,
    saveFlowDataDetail,
    preprocessSaveKitDataFn,
  } from '/@process-editor/utils';
  import {
    productKitDataToFormData,
    productControlKitDataToFormData,
    tableKitDataToFormData,
    videoKitDataToFormData,
    itemKitDataToFormData,
    chartKitDataToFormData,
    liquidLevelKitDataToFormData,
    pipelineKitDataToFormData,
    commonKitDataToFormData,
  } from './adapter';
  import { isOldPen } from '/@process-editor/core/share';
  import { updateKitPosition } from '/@process-editor/core/kits/data';
  import { createLocalStorage } from '/@/utils/cache';
  import { PLATFORM_KEY } from '/@/enums/cacheEnum';
  // import { addResourcePrefix, deleteResourcePrefix } from '/@process-editor/utils/index';
  import { getFlowDataStrByMeta2dData } from '/@process-editor/utils/index';

  const route = useRoute();
  const { createMessage } = useMessage();

  const {
    getEditorLocalStorage,
    updateUnsavedKits,
    updateModelInfo,
    updateFlowVersion,
    updateCanvasData,
    updateScaleSync,
  } = useProcess();

  let defKits = null;

  const state = reactive({
    productions: [],
    productionControls: [],
    item: {},
    videos: [],
    tables: [],
    charts: [],
    liquidLevel: {},
    pipeLine: {},
    common: {},
  });
  const {
    productions,
    productionControls,
    videos,
    item,
    tables,
    charts,
    liquidLevel,
    pipeLine,
    common,
  } = toRefs(state);

  const isValid = (val) => ![null, undefined, ''].includes(val);

  function validateEmpty(val, msg) {
    if (isValid(val)) return true;
    createMessage.error(msg);
    throw new Error(msg);
  }

  function validateFrameType(kit) {
    return validateEmpty(kit.frameType, '请先设置外框类型');
  }

  function validateFramePlacement(kit) {
    return validateEmpty(kit.framePlacement, '请先设置外框方位');
  }

  function validatePosition(kitData) {
    if (!kitData.positionX || !kitData.positionY) {
      createMessage.error('请先设置套件位置');
      throw new Error('请先设置套件位置');
    }
    return true;
  }

  const allKitData = {
    [KIT_ENUM.PRODUCT_DATA_KIT]: {
      kitValue: productions, // 原始数据
      getKitData: (val) => unref(val), // 保存的时候需要的数据处理
      initData: initProductionData, // 新增时初始数据
      validateFn: (val) =>
        unref(val).forEach((i) => {
          return validateFrameType(i), validateFramePlacement(i);
        }), // 保存时验证的函数
    },
    [KIT_ENUM.PRODUCT_CONTROL_KIT]: {
      kitValue: productionControls,
      getKitData: (val) => unref(val),
      initData: initProductionControlData,
      validateFn: (val) =>
        unref(val).forEach((i) => {
          return validateFrameType(i), validateFramePlacement(i);
        }),
    },
    [KIT_ENUM.TABLE_KIT]: {
      kitValue: tables,
      getKitData: (val) => unref(val),
      initData: initTableData,
      validateFn: (val) =>
        unref(val).forEach((i) => {
          return validateFrameType(i), validateFramePlacement(i);
        }),
    },
    [KIT_ENUM.CHART_KIT]: {
      kitValue: charts,
      getKitData: (val) => unref(val),
      initData: initChartData,
      validateFn: (val) =>
        unref(val).forEach((i) => {
          return validatePosition(i);
        }),
    },
    [KIT_ENUM.SMALL_SEMAPHORE]: {
      kitValue: item,
      getKitData: (val) => [unref(val)],
      initData: initItemData,
      validateFn: (i) => validatePosition(unref(i)),
    },
    [KIT_ENUM.LIQUID_LEVEL_KIT]: {
      kitValue: liquidLevel,
      getKitData: (val) => [unref(val)],
      initData: initLiquidLevelData,
      validateFn: (i) => validatePosition(unref(i)),
    },
    [KIT_ENUM.PIPELINE_KIT]: {
      kitValue: pipeLine,
      getKitData: (val) => [unref(val)],
      initData: initPipelineData,
      validateFn: (i) => validatePosition(unref(i)),
    },
    [KIT_ENUM.COMMON_KIT]: {
      kitValue: common,
      getKitData: (val) => [unref(val)],
      initData: initCommonData,
    },
    [KIT_ENUM.VIDEO_KIT]: {
      kitValue: videos,
      getKitData: (val) => handleVideoKitData(cloneDeep(unref(val))),
      initData: initVideoData,
      validateFn: (val) =>
        unref(val).forEach((i) => {
          return validateFramePlacement(i);
        }),
    },
  };

  function handleVideoKitData(videos) {
    const result = [];
    videos.forEach((item, index) => {
      if (item.type === 'SimpleVedioKit') {
        // 单视频套件
        item.businessData.videoUniqueId = item.videoUniqueId;
        item.businessData.playerUrl = item.playerUrl;
        item.businessData.displayName = item.displayName;
      } else if (item.type === 'MutiVedioKit') {
        // 多视频套件
        item.businessData = item.videoList.map((i) => ({
          videoUniqueId: i.id,
          displayName: i.displayName || i.name,
          playerUrl: i.playerUrl,
        }));
      }

      result.push(item);
    });

    return result;
  }
  const activeKey = ref([]);

  watch(
    () => [
      productions.value,
      productionControls.value,
      tables.value,
      videos.value,
      item.value,
      liquidLevel.value,
      pipeLine.value,
      common.value,
    ],
    () => {
      console.log('---------------------> watch');
      // 保存套件数据，方便头部保存按钮拿到套件数据
      const kitData = getUnsavedSaveKitData();
      updateUnsavedKits(kitData);
    },
    {
      deep: true,
    },
  );

  let activePen = {};
  const isItem = ref(false);
  const itemFlag = ref(false);
  const liquidLevelFlag = ref(false);
  const pipelineFlag = ref(false);
  const commonFlag = ref(false);
  const detailModelId = ref('');

  const kitType = ref('');

  const ls = createLocalStorage();
  // let unsetKistData = false; //true 为初始化后获取到的套件配置为空

  // 监听选中图元
  onMounted(async () => {
    meta2d.on('active', async (args) => {
      // 只修改一个
      if (args.length >= 1) {
        activePen = reactive(args[0]);
        itemFlag.value = false;
        liquidLevelFlag.value = false;
        pipelineFlag.value = false;
        commonFlag.value = false;
        const someArr = [activePen.isSimulation, !activePen.category, activePen.name !== 'line'];
        const meta2dData = getEditorLocalStorage(CANVAS_DATA);
        const pens = meta2dData.pens || [];
        isItem.value = activePen?.ModelType === 'Annunciator';
        // 是保存后的图元或者是模拟元素或者不是套件、线，就获取模型详情更新套件表单
        if (isOldPen(activePen, pens) && someArr.some((i) => i)) {
          await _updateModelInfo_(activePen);
        } else {
          kitType.value = isItem.value ? KIT_ENUM.SMALL_SEMAPHORE : '';
          initStateData(true);
        }

        itemFlag.value = true;
        liquidLevelFlag.value = true;
        pipelineFlag.value = true;
        commonFlag.value = true;
        // 是小元件就更新表单数据
        if (isItem.value) {
          item.value.positionX = activePen.x;
          item.value.positionY = activePen.y;
        }
      }

      if (args[0]?.ModelType) seActiveKey(args[0].ModelType);
    });

    const factoryId = getFactoryId();
    const platform = await getCurrentPlatformIdApi();
    ls.set(PLATFORM_KEY, platform.id);
  });

  // 监听删除套件图元
  onMounted(() => {
    meta2d.on('delete', (_) => {});
  });

  function getInitKitDataByType(type) {
    let { initData, kitValue } = allKitData[type];
    let res = cloneDeep(initData);
    return Array.isArray(kitValue.value) ? [res] : res;
  }
  let clearKitState = false; //true为删除已存套件
  function initStateData(v) {
    clearKitState = v ? false : true;
    if (clearKitState) {
      const jsonData = window.meta2d.data();
      // 清空pen上的数据，主要针对基础套件（按钮上的数据）
      for (let i = 0; i < jsonData.pens.length; i++) {
        const item = jsonData.pens[i];
        if (item.id == activePen.id) {
          activePen.dataForm = {};
          item.dataForm = {};
          break;
        }
      }
    }
    clearAllKitData();
    if (!kitType.value) return;
    let { kitValue } = allKitData[kitType.value];
    kitValue.value = getInitKitDataByType(kitType.value);
    updateUnsavedKits(unref(kitValue));
  }
  function clearAllKitData() {
    let keys = Object.keys(allKitData);
    keys.forEach((key) => clearKitData(key));
  }
  function clearKitData(type) {
    let kitValue = allKitData[type].kitValue;
    kitValue.value = Array.isArray(kitValue.value) ? [] : {};
  }

  // function initModelKitData() {
  //   kitType.value = '';
  //   initStateData(true);
  // }

  /**
   * 获取模型和套件数据
   * @param {Object} activePen 选中的图元
   */
  async function getModelAndKitData(activePen) {
    const params = {
      flowId: route.query.flowId,
      modelId: activePen.id,
      version: getEditorLocalStorage(FLOW_VERSION),
    };

    const res = await getShowModelDetailApi(params);
    const kits = handleBusinessInfos(res.businessInfos, activePen);

    return { modelInfo: res?.modelInfo, kits };
  }

  /**
   * 更新模型信息
   * @param {Object} activePen 选中的图元
   * @param {Boolean} updateForm 是否更新表单
   */
  async function _updateModelInfo_(activePen, updateForm = true) {
    try {
      const info = await getModelAndKitData(activePen);
      if (updateForm) setModelKitData(info.kits, activePen);
      detailModelId.value = info.modelInfo?.id || '';
      updateModelInfo(info.modelInfo);
      // unsetKistData = kitType.value ? false : true;
    } catch (err) {
      console.log('------------> err', err);
    }
  }

  /**
   * 设置collapse展开的key
   * @param {String} ModelType 模型类型
   */
  function seActiveKey(ModelType) {
    // 收起套件
    activeKey.value = [];

    const productionKeys = productions.value.map((_, index) => `production${index + 1}`);
    const productionControlKeys = productionControls.value.map(
      (_, index) => `productionControl${index + 1}`,
    );
    const videoKeys = videos.value.map((_, index) => `video${index + 1}`);
    const itemKey = isEmpty(item.value) ? [] : ['item1'];
    const tableKeys = tables.value.map((_, index) => `table${index + 1}`);
    const chartKeys = charts.value.map((_, index) => `chart${index + 1}`);
    const liquidLevelKeys = isEmpty(liquidLevel.value) ? [] : ['liquidLevel1'];
    const pipelineKeys = isEmpty(pipeLine.value) ? [] : ['pipeline1'];
    const commonKeys = isEmpty(common.value) ? [] : ['common'];
    switch (ModelType) {
      case 'Simulation':
        activeKey.value = productionKeys.concat(
          productionControlKeys,
          videoKeys,
          itemKey,
          tableKeys,
          chartKeys,
          pipelineKeys,
        );
        break;
      default:
        break;
    }
  }

  /**
   * 监听切换面板
   */
  function onChangeViewMode() {}

  /**
   * 获取未保存的套件数据
   */
  function getUnsavedSaveKitData() {
    if (!allKitData[kitType.value]) return [];
    let { kitValue, getKitData } = allKitData[kitType.value];
    let res = getKitData(kitValue);
    return res;
  }

  /**
   * 添加套件
   * @param {String} type 套件类型
   */
  // function addKit(type) {
  //   switch (type) {
  //     case PRODUCT_DATA_KIT:
  //       productions.value.push(cloneDeep(initProductionData));
  //       break;
  //     case PRODUCT_CONTROL_KIT:
  //       productionControls.value.push(cloneDeep(initProductionControlData));
  //       break;
  //     case VIDEO_KIT:
  //       videos.value.push(cloneDeep(initVideoData));
  //       break;
  //     default:
  //       console.log('添加套件失败');
  //       break;
  //   }
  // }

  /**
   * 删除套件
   * @param {String} type 套件类型
   */
  function deleteKit(index, type) {
    switch (type) {
      case PRODUCT_DATA_KIT:
        productions.value.splice(index, 1);
        break;
      case PRODUCT_CONTROL_KIT:
        productionControls.value.splice(index, 1);
        break;
      case VIDEO_KIT:
        videos.value.splice(index, 1);
        break;
      default:
        console.log('删除套件失败');
        break;
    }
  }

  /**
   * 处理业务(套件)数据
   * @param {Array} data 业务数据
   * @param {Object} activePen 选中pen
   */
  function handleBusinessInfos(data, activePen) {
    const result = data.map((i) => {
      const _item_ = cloneDeep(i);
      const { frameType, framePlacement } = _item_;
      const arr = [typeof frameType === 'number', typeof framePlacement === 'number'];
      if (arr.every(Boolean)) {
        updateKitPosition(framePlacement, _item_, activePen);
      }

      return {
        ..._item_,
        businessData: i.businessData ? JSON.parse(i.businessData) : {},
      };
    });

    return result;
  }

  /**
   * 设置模型的套件数据
   * @param {Object} activePen 选中的图元
   */
  function setModelKitData(kits, activePen) {
    kitType.value = kits[0]?.kitTypeName || '';
    productions.value = productKitDataToFormData(kits, activePen);
    productionControls.value = productControlKitDataToFormData(kits, activePen);
    tables.value = tableKitDataToFormData(kits, activePen);
    videos.value = videoKitDataToFormData(kits);
    item.value = itemKitDataToFormData(kits, item.value, activePen);
    charts.value = chartKitDataToFormData(kits, activePen);
    liquidLevel.value = liquidLevelKitDataToFormData(kits);
    pipeLine.value = pipelineKitDataToFormData(kits);
    common.value = commonKitDataToFormData(kits);
  }

  /**
   * 表单校验
   */
  async function validate() {
    const saveKitData = getUnsavedSaveKitData();
    // 允许不选择套件，数据套件的数据可以清除
    if (saveKitData.length === 0 || !kitType.value || !allKitData[kitType.value]) return true;
    let { validateFn, kitValue } = allKitData[kitType.value];
    validateFn && validateFn(kitValue.value);
  }

  /**
   * 判断是否保存流程图
   */
  function isSaveFlow() {
    const newJsonStr = getFlowDataStrByMeta2dData();
    const canvasData = getEditorLocalStorage(CANVAS_DATA);
    const oldJsonStr = JSON.stringify(toRaw(canvasData));

    return oldJsonStr !== newJsonStr;
  }

  /**
   * 保存流程图
   */
  async function saveFlow() {
    let res = await saveFlowDataDetail(
      { flowId: route.query.flowId || '', version: getEditorLocalStorage(FLOW_VERSION) },
      {},
    );

    return res;
  }

  /**
   * 保存套件
   */
  async function saveKits() {
    const params = {
      detailModelId: detailModelId.value,
      kits: getUnsavedSaveKitData(),
    };
    params.kits = preprocessSaveKitDataFn(params.kits);
    console.log('params', params);
    await saveShowModelDetailApi(params);
    createMessage.success('保存套件成功');
  }

  /**
   * 获取全部套件数据
   */
  async function getKitsData() {
    const kitData = await previewDefaultBusinessDataApi({
      flowId: route.query.flowId,
    });

    const _kitData = kitData.map((i) => ({
      ...i,
      businessData: JSON.parse(i.businessData),
    }));

    return _kitData;
  }

  /**
   * 渲染套件
   */
  async function renderKits(kitData) {
    if (!defKits) {
      defKits = new KitsV2(meta2d, ls.get(CANVAS_DATA));
    }

    const meta2dData = window.meta2d.data();
    const scale = meta2dData.scale || 1;
    defKits.updateMeta2dData(ls.get(CANVAS_DATA));
    defKits.render(kitData, scale);
  }

  /**
   * 更新缓存中的工艺流程数据
   */
  function updateProcessStoreData(flowInfo, kitData) {
    if (!isEmpty(flowInfo)) {
      // 更新流程图版本
      updateFlowVersion(flowInfo.version);
      // 更新画布数据
      updateCanvasData(flowInfo.canvasData);
    }

    updateScaleSync(true);
  }

  async function save() {
    if (kitType.value === COMMON_KIT) {
      common.value.displayMode = 0;
      onChangeViewMode();
    }
    // 表单校验
    try {
      await validate();
    } catch (err) {
      console.log('-----> err', err);
      return;
    }

    console.log('显示内容--------保存');
    // 保存流程图
    if (isSaveFlow()) {
      const flowInfo = await saveFlow();
      // 获取模型详情拿到模型id,改保存套件用
      const meta2dData = getEditorLocalStorage(CANVAS_DATA);
      const pens = meta2dData.pens || [];
      if (isOldPen(activePen, pens)) {
        await _updateModelInfo_(activePen, false);
      }
    }

    // 保存套件
    await saveKits();

    // 获取所有套件数据
    const kitData = await getKitsData();
    // 渲染套件
    await renderKits(kitData);

    // 更新缓存中的数据
    updateProcessStoreData({}, kitData);
  }
</script>

<style lang="less" scoped>
  .kit {
    .my-collapse {
      padding-bottom: 88px;
    }

    .action-wrap {
      position: fixed;
      right: 0;
      bottom: 0;
      padding: 0 24px;
      width: 376px;
      height: 72px;
      display: flex;
      gap: 16px;
      justify-content: flex-end;
      align-items: center;
      border-top: 1px solid #f5f6f7;
      background-color: #fff;
    }
  }
</style>
