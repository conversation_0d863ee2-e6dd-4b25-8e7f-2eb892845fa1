import {
  DASHED_BORDER_MIN_WIDTH,
  TABLE_KIT,
  CHART_KIT,
  VIDEO_KIT,
  LIQUID_LEVEL_KIT,
  PIPELINE_KIT,
  CHART_WIDTH,
  CHART_HEIGHT,
  PRODUCT_CONTROL_KIT,
} from './constant';
import { KIT_ENUM } from '/@process-editor/constant/index';
import { PLACEMEN_ENUM, FRAMETYPE_ENUM } from '/@process-editor/core/kits/constant';
import { h } from 'vue';
import { getLineOption } from '/@process-editor/core/kits/data';
import { ITEM_TYPES } from '/@process-editor/core/kits/constant';
import cardBg1 from '/@process-editor/assets/images/card-bg1.png';
import cardBg2 from '/@process-editor/assets/images/card-bg2.png';
import productTip from '/@process-editor/assets/images/product-tip.png';
import tableTip from '/@process-editor/assets/images/table-tip.png';
import lineTip from '/@process-editor/assets/images/line-tip.png';
import videoTip from '/@process-editor/assets/images/video-tip.png';
import {
  INDICATOR_TYPE_ENUM,
  DISPLAY_MODE_ENUM,
  VIDEO_IMG_WIDTH,
  VIDEO_IMG_HEIGHT,
  DEVICE_CHANGE_SWITCH_API,
} from '/@process-editor/constant';

export const displayOptions = [
  { label: '是', value: DISPLAY_MODE_ENUM.SHOW },
  { label: '否', value: DISPLAY_MODE_ENUM.HIDDEN },
];

const generCommontTipCom = (imgUrl) => () =>
  h(
    'div',
    {
      style: {
        width: '240px',
        height: '190px',
        backgroundColor: '#041946',
        display: 'flex',
        borderRadius: '4px',
        justifyContent: 'center',
        alignItems: 'center',
      },
    },
    h('img', {
      src: imgUrl,
      style: {
        width: '216px',
        height: 'auto',
      },
    }),
  );
export const kitOptions = [
  {
    label: '生产套件',
    value: KIT_ENUM.PRODUCT_DATA_KIT,
    TipsComponents: generCommontTipCom(productTip),
  },
  {
    label: 'table套件',
    value: KIT_ENUM.TABLE_KIT,
    TipsComponents: generCommontTipCom(tableTip),
  },
  {
    label: '折线图套件',
    value: KIT_ENUM.CHART_KIT,
    TipsComponents: generCommontTipCom(lineTip),
  },
  {
    label: '视频套件',
    value: KIT_ENUM.VIDEO_KIT,
    TipsComponents: generCommontTipCom(videoTip),
  },
  {
    label: '数据套件',
    value: KIT_ENUM.COMMON_KIT,
  },
  {
    label: '生产下控套件',
    value: KIT_ENUM.PRODUCT_CONTROL_KIT,
    TipsComponents: null,
  },
  // {
  //   label: '液位套件',
  //   value: LIQUID_LEVEL_KIT,
  // },
  // {
  //   label: '管线套件',
  //   value: PIPELINE_KIT,
  // },
];

export const expressionType = [
  {
    label: '数值型',
    value: 0,
  },
  {
    label: '文字型',
    value: 1,
  },
];

export const borderOptions = [
  {
    label: '无边框',
    value: FRAMETYPE_ENUM.NO_BORDER,
  },
  {
    label: '边框',
    value: FRAMETYPE_ENUM.HAS_BORDER,
  },
  // {
  //   label: '弹窗',
  //   value: 2
  // },
];

export const tableOptions = [
  // {
  //   label: '单行单指标',
  //   value: 0,
  // },
  {
    label: '单行多指标',
    value: 1,
  },
];

export const formItemOptions = [
  {
    label: '下拉选择器',
    value: 'Select',
  },
  // {
  //   label: '日期选择器',
  //   value: 'DatePicker',
  // },
];

export const placementOptions = [
  {
    label: '上',
    value: PLACEMEN_ENUM.TOP,
  },
  {
    label: '右',
    value: PLACEMEN_ENUM.RIGHT,
  },
  {
    label: '下',
    value: PLACEMEN_ENUM.BOTTOM,
  },
  {
    label: '左',
    value: PLACEMEN_ENUM.LEFT,
  },
];

export const colTypeOptions = [
  { label: '文本', value: 'text' },
  { label: '输入框', value: 'input' },
];

export const textAlignOptions = [
  { label: '左对齐', value: 'left' },
  { label: '居中', value: 'center' },
  { label: '右对齐', value: 'right' },
];

export enum frameBackgroundTypeEnum {
  MODE1 = 10,
  MODE2 = 20,
  ModE3 = 30,
  CUSTOM = 100,
}

export const frameBackgroundOptions = [
  { label: '暗色', img: cardBg1, value: frameBackgroundTypeEnum.MODE1 },
  { label: '浅色', img: cardBg2, value: frameBackgroundTypeEnum.MODE2 },
];

export enum expressionTypeEnum {
  regular = 0,
  indicator = 10,
  rule = 20,
}

export const expressionTypeOptions = [
  { label: '常规配置', value: expressionTypeEnum.regular },
  { label: '指标表达式', value: expressionTypeEnum.indicator },
  { label: '报警规则表达式', value: expressionTypeEnum.rule },
];

export const initProductionData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: KIT_ENUM.PRODUCT_DATA_KIT,
  positionX: '',
  positionY: '',
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  frameType: FRAMETYPE_ENUM.HAS_BORDER,
  framePlacement: PLACEMEN_ENUM.BOTTOM,
  frameWidth: DASHED_BORDER_MIN_WIDTH + '',
  frameHeight: '',
  framePadding: [14, 14, 14, 14],
  frameBackgroundType: frameBackgroundTypeEnum.MODE1,
  frameBackground: '',
  // tableType: '',
  businessData: {
    isShowIndicator: false,
    kitConfig: {
      title: {
        fontSize: 14,
        color: 'rgba(255, 255, 255, 1)',
        height: 32,
      },
      column: {
        width: [100, 80, 80],
        fontSize: [14, 14, 14],
        textAlign: ['left', 'left', 'left'],
        padding: [
          { paddingTop: '', paddingRight: '', paddingBottom: '', paddingLeft: '' },
          { paddingTop: '', paddingRight: '', paddingBottom: '', paddingLeft: '' },
          { paddingTop: '', paddingRight: '', paddingBottom: '', paddingLeft: '' },
        ],
        color: 'rgba(255, 255, 255, 1)',
        height: 32,
        show: [true, true, true],
        isShowUnit: [false, false, false],
      },
    },
    productDataInfos: [],
    tree: [],
    resourceInterfaceId: '',
    displayProductDataInfos: [
      {
        code: '',
        expression: '',
        unit: '',
        displayName: '',
        shortDisplayName: '',
        isShowShort: false,
        upperLimits: '',
        lowerLimits: '',
        isNumber: true,
        isUpperEqual: true,
        isLowerEqual: true,
        normalColor: '',
        greaterThanUpperColor: 'rgba(255, 82, 43, 1)',
        lowerThanLowerColor: 'rgba(254, 197, 45, 1)',
        script: '',
        scriptType: '',
        warningRuleId: '',
      },
    ],
  },
};

export const initProductionControlData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: PRODUCT_CONTROL_KIT,
  positionX: '',
  positionY: '',
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  frameType: FRAMETYPE_ENUM.HAS_BORDER,
  framePlacement: PLACEMEN_ENUM.BOTTOM,
  frameWidth: DASHED_BORDER_MIN_WIDTH + '',
  frameHeight: '',
  framePadding: [14, 14, 14, 14],
  frameBackgroundType: frameBackgroundTypeEnum.MODE1,
  frameBackground: '',
  // tableType: '',
  businessData: {
    isShowIndicator: false,
    kitConfig: {
      title: {
        fontSize: 14,
        color: 'rgba(255, 255, 255, 1)',
        height: 32,
      },
      column: {
        width: [100, 80, 80],
        fontSize: [14, 14, 14],
        textAlign: ['left', 'left', 'left'],
        padding: [
          { paddingTop: '', paddingRight: '', paddingBottom: '', paddingLeft: '' },
          { paddingTop: '', paddingRight: '', paddingBottom: '', paddingLeft: '' },
          { paddingTop: '', paddingRight: '', paddingBottom: '', paddingLeft: '' },
        ],
        color: 'rgba(255, 255, 255, 1)',
        height: 32,
        show: [true, true, false],
        isShowUnit: [false, false, false],
      },
    },
    productDataInfos: [],
    tree: [],
    resourceInterfaceId: '',
    displayProductDataInfos: [
      {
        code: '',
        expression: '',
        unit: '',
        displayName: '',
        shortDisplayName: '',
        isShowShort: false,
        upperLimits: '',
        lowerLimits: '',
        isNumber: true,
        isUpperEqual: true,
        isLowerEqual: true,
        normalColor: '',
        greaterThanUpperColor: 'rgba(255, 82, 43, 1)',
        lowerThanLowerColor: 'rgba(254, 197, 45, 1)',
        script: '',
        scriptType: '',
        warningRuleId: '',
      },
    ],
  },
};

export const initTableData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: TABLE_KIT,
  positionX: '',
  positionY: '',
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  frameType: FRAMETYPE_ENUM.HAS_BORDER,
  framePlacement: PLACEMEN_ENUM.BOTTOM,
  frameWidth: '',
  frameHeight: '',
  framePadding: [14, 14, 14, 14],
  frameBackgroundType: frameBackgroundTypeEnum.MODE1,
  frameBackground: '',
  businessData: {
    dataset: '',
    resourceInterfaceId: '',
    dimensions: [],
    tableConfig: {
      header: [],
      data: [],
      headerColor: '',
      headerBackground: '',
      style: {
        title: {
          fontSize: 14,
        },
        header: {
          fontSize: 12,
        },
        body: {
          fontSize: 12,
        },
      },
    },
    tableDataInfos: [],
    displayTableDataInfos: [
      {
        code: '',
        expression: '',
        unit: '',
        displayName: '',
        shortDisplayName: '',
        isShowShort: false,
        upperLimits: '',
        lowerLimits: '',
        isNumber: true,
        isUpperEqual: true,
        isLowerEqual: true,
        normalColor: '',
        greaterThanUpperColor: 'rgba(255, 82, 43, 1)',
        lowerThanLowerColor: 'rgba(254, 197, 45, 1)',
        script: '',
        scriptType: '',
        warningRuleId: '',
      },
    ],
  },
};

export const initChartData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: CHART_KIT,
  positionX: '',
  positionY: '',
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  frameType: 1,
  frameWidth: 922,
  businessData: {
    // 联动表单类型 DatePicker,Select
    formItemType: '',
    // 数据集
    dataset: '',
    // 接口id
    resourceInterfaceId: '',
    // 指标分组ID
    groupId: '',
    // 指标CODE
    indexCodes: [],
    // 图表宽
    width: `${CHART_WIDTH}`,
    // 图表高
    height: `${CHART_HEIGHT}`,
    // echarts配置
    echarts: {
      option: getLineOption(),
    },
  },
};

export const initLiquidLevelData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: LIQUID_LEVEL_KIT,
  positionX: '',
  positionY: '',
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  frameType: 0,
  framePlacement: '',
  frameWidth: '',
  frameHeight: '',
  businessData: {
    // 液位指标
    indicator: '',
    // 最大值
    max: 1,
    // 最小值
    min: 0,
    // 模型宽度
    modelWidth: 0,
    // 模型高度
    modelHeight: 0,
    // 波浪图
    image: '',
    // 图片id
    imageId: '',
    // 脚本
    script: '',
  },
};

export const initPipelineData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: PIPELINE_KIT,
  positionX: '',
  positionY: '',
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  frameType: 0,
  framePlacement: '',
  frameWidth: '',
  frameHeight: '',
  businessData: {
    // 关联指标
    indicator: '',
    // 关联管线id
    ids: [],
    // gif图
    gif: '',
    // 静态图
    image: '',
    // 脚本
    script: '',
  },
};

export const defaultDisplayProductDataInfo = {
  code: '',
  expression: '',
  unit: '',
  displayName: '',
  shortDisplayName: '',
  isShowShort: false,
  upperLimits: '',
  lowerLimits: '',
  isNumber: true,
  isUpperEqual: true,
  isLowerEqual: true,
  normalColor: '',
  greaterThanUpperColor: 'rgba(255, 82, 43, 1)',
  lowerThanLowerColor: 'rgba(254, 197, 45, 1)',
  script: '',
  scriptType: '',
  warningRuleId: '',
};

export const defaultDisplayProductControlDataInfo = {
  code: '',
  expression: '',
  unit: '',
  displayName: '',
  shortDisplayName: '',
  isShowShort: false,
  upperLimits: '',
  lowerLimits: '',
  isNumber: true,
  isUpperEqual: true,
  isLowerEqual: true,
  normalColor: '',
  greaterThanUpperColor: 'rgba(255, 82, 43, 1)',
  lowerThanLowerColor: 'rgba(254, 197, 45, 1)',
  script: '',
  scriptType: '',
  warningRuleId: '',
  indicatorType: INDICATOR_TYPE_ENUM.NUMBER,
  // wqtodo 下控地址
  controlUrl: DEVICE_CHANGE_SWITCH_API,
};

export const initExpression = {
  expression: '',
  unit: '',
  displayName: '',
  shortDisplayName: '',
  isShowShort: false,
  upperLimits: '',
  lowerLimits: '',
  isNumber: true,
  isUpperEqual: true,
  isLowerEqual: true,
  normalColor: '',
  greaterThanUpperColor: 'rgba(255, 82, 43, 1)',
  lowerThanLowerColor: 'rgba(254, 197, 45, 1)',
  script: '',
  scriptType: '',
  warningRuleId: '',
  code: '',
};

export const initValue = {
  value: '',
  color: '',
};

export const indexOptions = [
  {
    label: 'ph',
    value: 'CS_PH_AI',
  },
  {
    label: 'COD',
    value: 'CS_COD_AI',
  },
];

export const initVideoData = {
  kitDisplayName: '',
  kitTypeName: VIDEO_KIT,
  positionX: 1,
  positionY: 1,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  frameWidth: VIDEO_IMG_WIDTH,
  frameHeight: VIDEO_IMG_HEIGHT,
  framePlacement: PLACEMEN_ENUM.BOTTOM,
  framePadding: [0, 0, 0, 0],
  type: 'SimpleVedioKit',
  videoUniqueId: '',
  playerUrl: '',
  displayName: '',
  businessData: {
    videoUniqueId: '',
    playerUrl: '',
    displayName: '',
  },
};

export const videoUrlOptions = [
  {
    label: '净化厂',
    value: 'http://video1.com',
  },
  {
    label: '污水厂',
    value: 'http://video2.com',
  },
];

export const itemTypeOptions = ITEM_TYPES;

export const initItemData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: 'SmallSemaphore',
  positionX: '',
  positionY: '',
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  framePlacement: 0,
  businessData: {
    // 小元件的宽度
    itemWidth: '',
    // 小元件的高度
    itemHeight: '',
    // 小元件类型 通用-base 鼓风机-blower
    itemType: 'base',
    // 管线ID
    pipelineIds: [],
    // {DataUniqueId,DataValue}
    productDataInfos: [],
    // {ImagePath,ImageName}
    semaphoreImageList: [],
    script: '',
  },
};

export const videoTypeOptions = [
  {
    label: '单个',
    value: 'SimpleVedioKit',
  },
  {
    label: '多个',
    value: 'MutiVedioKit',
  },
];

export const initSimpleVideoData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: 'SimpleVedioKit',
  positionX: 1,
  positionY: 1,
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  framePlacement: PLACEMEN_ENUM.BOTTOM,
  frameWidth: VIDEO_IMG_WIDTH,
  frameHeight: VIDEO_IMG_HEIGHT,
  videoId: '',
  videoList: [],
  displayName: '',
  businessData: {
    videoUniqueId: '',
    playerUrl: '',
    playVedioType: '',
    displayName: '',
  },
};

export const initMultipleVideoData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: 'MutiVedioKit',
  positionX: 1,
  positionY: 1,
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  framePlacement: PLACEMEN_ENUM.BOTTOM,
  frameWidth: VIDEO_IMG_WIDTH,
  frameHeight: VIDEO_IMG_HEIGHT,
  businessData: [
    {
      videoUniqueId: '',
      playerUrl: '',
      playVedioType: '',
      displayName: '',
    },
  ],
};

export const videoColumns = [
  {
    title: '视频名称',
    dataIndex: 'name',
  },
  {
    title: '视频别名',
    dataIndex: 'displayName',
  },
];

export const initCommonData = {
  uniqueId: '',
  kitDisplayName: '',
  kitTypeName: 'CommonKit',
  positionX: '',
  positionY: '',
  zIndex: 0,
  displayMode: DISPLAY_MODE_ENUM.SHOW,
  framePlacement: 0,
  businessData: {
    productDataInfos: [],
    script: '',
  },
};
