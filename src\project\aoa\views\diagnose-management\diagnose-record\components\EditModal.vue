<template>
  <div>
    <BasicModal
      :canFullscreen="false"
      @cancel="handleCancel"
      v-bind="$attrs"
      title="诊断记录详情"
      @register="registerModal"
      width="1200px"
    >
      <div class="container">
        <BasicForm @register="registerForm">
          <!-- 诊断类型 -->
          <template #diagnosis-type-slot="{ model, field }">
            <Select
              v-model:value="model[field]"
              :options="diagnosisTypeOptions"
              disabled
              placeholder="请选择诊断类型"
            />
          </template>
          <!-- 触发类型 -->
          <template #trigger-type-slot="{ model, field }">
            <Select
              v-model:value="model[field]"
              :options="triggerTypeOptions"
              placeholder="请选择触发类型"
              disabled
            />
          </template>
          <!-- 报警规则 -->
          <template #alarm-slot="{ model, field }">
            <div class="rule-contianer">
              <Select
                v-model:value="model[field]"
                :options="alarmRuleOptions"
                :field-names="{
                  label: 'title',
                  value: 'id',
                }"
                showSearch
                optionFilterProp="title"
                disabled
                placeholder="请选择报警规则"
              />
              <Button
                v-if="model[field]"
                type="primary"
                ghost
                @click="handleOpenAlarmRuleDetailModal"
                >查看详情</Button
              >
            </div>
          </template>
          <!-- 参与指标 -->
          <template #indicator-list-slot>
            <div class="indicator-table">
              <BasicTable @register="registeIndicatorTable" />
            </div>
          </template>
          <!-- 报警参与指标 -->
          <template #indicator-alarm-list-slot>
            <div class="indicator-alarm-form">
              <Row :gutter="[0, 12]">
                <Col :span="12" v-for="item in indicatorAlarmList" :key="item.code">
                  <Input v-model:value="item.code" disabled />
                </Col>
              </Row>
              <!-- <BasicTable @register="registeIndicatorTable" /> -->
            </div>
          </template>
          <template #formula-slot="{ model, field }">
            <div class="trigger-condition-container">
              <!-- 一般规则触发条件 -->
              <div class="normal-rule" v-if="rule_type === 1">
                <Form ref="formRef1" :model="dynamicForm">
                  <Row
                    :gutter="[12, 16]"
                    v-for="(item, index) in dynamicForm.list"
                    :key="item.code"
                    :class="index > 0 ? 'my-4' : ''"
                  >
                    <Col :span="12">
                      <FormItem
                        label=""
                        :name="['list', index, 'code']"
                        :rules="{ required: true, message: '请选择指标', trigger: 'blur' }"
                      >
                        <Input v-model:value="item.code" placeholder="请输入指标" disabled />
                      </FormItem>
                    </Col>
                    <Col :span="4">
                      <FormItem
                        label=""
                        :name="['list', index, 'action']"
                        :rules="{ required: true, message: '请选择比较符', trigger: 'blur' }"
                      >
                        <Select v-model:value="item.action" placeholder="请选择比较符" disabled>
                          <SelectOption
                            v-for="i in ruleActionList"
                            :key="i.id"
                            :value="i.id"
                            :data="i"
                          >
                            {{ i.title }}
                          </SelectOption>
                        </Select>
                      </FormItem>
                    </Col>
                    <Col :span="4">
                      <FormItem
                        label=""
                        :name="['list', index, 'minValue']"
                        :rules="{ required: true, message: '请输入判断值', trigger: 'blur' }"
                      >
                        <Input v-model:value="item.minValue" placeholder="请输入判断值" disabled />
                      </FormItem>
                    </Col>
                    <Col :span="4" v-if="item.valueCount === 2">
                      <FormItem
                        label=""
                        :name="['list', index, 'maxValue']"
                        :rules="{ required: true, message: '请输入判断值', trigger: 'blur' }"
                      >
                        <Input v-model:value="item.maxValue" placeholder="请输入判断值" disabled />
                      </FormItem>
                    </Col>
                  </Row>
                </Form>
              </div>
              <!-- 特殊规则触发条件 -->
              <div class="special-rule" v-if="rule_type === 2">
                <!-- 设置规则 -->
                <div class="add-rule" v-if="rule_setting === 2">
                  <div class="content">
                    <Form ref="formRef2" :model="dynamicForm2">
                      <Row
                        :gutter="[12, 16]"
                        v-for="(item, index) in dynamicForm2.list"
                        :key="item.code"
                        :class="index > 0 ? 'my-4' : ''"
                      >
                        <Col :span="12">
                          <FormItem
                            label=""
                            :name="['list', index, 'code']"
                            :rules="{
                              required: true,
                              message: '请选择指标',
                              trigger: 'change',
                            }"
                          >
                            <Input v-model:value="item.code" placeholder="请输入指标" disabled />
                          </FormItem>
                        </Col>
                        <Col :span="4">
                          <FormItem
                            label=""
                            :name="['list', index, 'action']"
                            :rules="{
                              required: true,
                              message: '请选择比较运算符',
                              trigger: 'change',
                            }"
                          >
                            <Select
                              v-model:value="item.action"
                              placeholder="请选择比较运算符"
                              disabled
                            >
                              <SelectOption
                                v-for="i in ruleActionList"
                                :key="i.id"
                                :value="i.id"
                                :data="i"
                              >
                                {{ i.title }}
                              </SelectOption>
                            </Select>
                          </FormItem>
                        </Col>
                        <Col :span="4">
                          <FormItem
                            label=""
                            :name="['list', index, 'minValue']"
                            :rules="{
                              required: true,
                              message: item.type === 1 ? '请输入判断值' : '请选择请指标',
                              trigger: item.type === 1 ? 'blur' : 'change',
                            }"
                          >
                            <Input
                              v-model:value="item.minValue"
                              placeholder="请输入判断值"
                              disabled
                            />
                          </FormItem>
                        </Col>
                        <Col :span="4" v-if="item.valueCount === 2">
                          <FormItem
                            label=""
                            :name="['list', index, 'maxValue']"
                            :rules="{
                              required: true,
                              message: item.type === 1 ? '请输入判断值' : '请选择请指标',
                              trigger: item.type === 1 ? 'blur' : 'change',
                            }"
                          >
                            <Input
                              v-model:value="item.maxValue"
                              placeholder="请输入判断值"
                              disabled
                            />
                          </FormItem>
                        </Col>
                      </Row>
                    </Form>
                  </div>
                </div>
                <!-- 编辑规则 -->
                <div class="json-rule" v-if="rule_setting === 1">
                  <JsonEditor
                    v-model:modelValue="model[field]"
                    style="height: 150px; border: 1px solid #ced4da"
                    disabled
                  />
                </div>
              </div>
            </div>
          </template>

          <template #suggestion-slot="{ model, field }">
            <div class="content-container">
              <Textarea v-model:value="model[field]" disabled />
              <!-- <Button
                :loading="false"
                :icon="h(Icon, { icon: 'icon-park-outline:doc-detail' })"
                type="primary"
                class="mt-2"
                >AI生成模板</Button
              > -->
            </div>
          </template>
          <template #content-slot="{ model, field }">
            <div class="content-container">
              <Textarea v-model:value="model[field]" disabled />
              <!-- <Button
                :loading="false"
                :icon="h(Icon, { icon: 'icon-park-outline:doc-detail' })"
                type="primary"
                class="mt-2"
                >AI生成模板</Button
              > -->
            </div>
          </template>
        </BasicForm>
      </div>
      <template #footer>
        <a-button @click="handleCancel">关闭</a-button>
      </template>
    </BasicModal>
    <AlarmRuleDetailModal @register="registerAlarmRuleDetailModal" />
  </div>
</template>

<script lang="ts" setup name="EditModal">
  import { ref, reactive } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicTable, useTable } from '/@/components/Table';
  import { JsonEditor } from '/@/components/JsonEditor';
  import AlarmRuleDetailModal from '/@aoa/views/alarm-management/rule-configuration/components/EditModal.vue';
  import {
    Form,
    FormItem,
    Select,
    SelectOption,
    Row,
    Col,
    Input,
    Textarea,
    Button,
  } from 'ant-design-vue';
  import { schemas, diagnosisTypeOptions, triggerTypeOptions, indicatorColumns } from '../data';
  import { formulaToArr } from '../../util';
  import { getWarnEventByPage } from '/@aoa/api/event-center';
  import {
    detailDiagnosisRecord,
    getDefaultRuleActionList,
    getListIndicatorAndVar,
  } from '/@aoa/api/diagnose';

  interface FormList {
    code: string;
    name: string;
    action: string;
    valueCount: number;
    minValue: '';
    maxValue: '';
    type: number; // 0 指标  1 数值
  }
  interface action {
    id: string;
    title: string;
    valueCount: number;
  }

  const isEdit = ref(false);
  const rule_type = ref(1);
  const rule_setting = ref(2);
  const indicatorList = ref([]);
  const indicatorAlarmList = ref([]); // 报警参与指标
  const id = ref();
  const formRef1 = ref();
  const formRef2 = ref();
  const dynamicForm = reactive<{ list: FormList[] }>({ list: [] }); // 一般
  const dynamicForm2 = reactive<{ list: FormList[] }>({ list: [] }); // 特殊
  const ruleActionList = ref<action[]>([]);
  const alarmRuleOptions = ref<{ id: string; title: string; targets: any[] }[]>([]);

  const [registerForm, { resetFields, updateSchema, setFieldsValue, getFieldsValue }] = useForm({
    labelWidth: 100,
    schemas: schemas,
    showActionButtonGroup: false,
  });

  const [registeIndicatorTable, { setTableData }] = useTable({
    dataSource: [],
    columns: indicatorColumns,
    pagination: false,
    useSearchForm: false,
    showIndexColumn: true,
    canResize: false,
    indexColumnProps: {
      width: 150,
    },
    showTableSetting: false,
  });

  // 报警规则详情
  const [registerAlarmRuleDetailModal, { openModal: openAlarmRuleDetailModal }] = useModal();
  const handleOpenAlarmRuleDetailModal = () => {
    const alarmId = getFieldsValue()?.alarmId;
    const data = alarmRuleOptions.value.find((item) => item.id === alarmId);
    openAlarmRuleDetailModal(true, {
      isEdit: true,
      record: data,
      disabled: true,
    });
  };

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    isEdit.value = data.isEdit;
    id.value = data.id;
    setModalProps({ loading: true });
    await initFormSelect();
    getDetail(data.id);
    setModalProps({ loading: false });
  });

  // 初始化表单数据
  const initFormSelect = async () => {
    // 获取报警规则
    await getWarnEventByPage({ current: 1, size: 1000 }).then((data) => {
      alarmRuleOptions.value = data.records;
    });
    // 获取所有code
    await getListIndicatorAndVar({}).then((data) => {
      indicatorList.value = data;
    });
    // 比较符
    await getDefaultRuleActionList().then((data) => {
      ruleActionList.value = data;
    });
  };

  // 关闭
  const handleCancel = () => {
    rule_type.value = 1;
    rule_setting.value = 2;
    dynamicForm.list = [];
    dynamicForm2.list = [];
    indicatorAlarmList.value = [];
    setTableData([]);
    updateSchema([
      { field: 'alarmId', ifShow: false },
      { field: 'frequency', ifShow: false },
      { field: 'triggerFrequency', ifShow: false },
      { field: 'indicatorList', show: false },
      { field: 'indicatorAlarmList', show: false },
      { field: 'formula', ifShow: false },
      { field: 'blank2', ifShow: false },
      // { field: 'aiStatus', ifShow: true },
      { field: 'exceptionReason', ifShow: false },
      { field: 'suggestion', ifShow: false },
      { field: 'content', ifShow: false },
    ]);
    resetFields();
    closeModal();
  };

  // 获取详情
  const getDetail = async (id) => {
    const res = await detailDiagnosisRecord(id);
    console.log('详情', res);
    setFieldsValue({ ...res });
    rule_type.value = res.ruleType;
    rule_setting.value = res.isEdit ? 1 : 2;
    if (res.diagnosisType === 2 && res?.triggerType === 1 && res?.ruleType === 2) {
      setFieldsValue({
        triggerFrequency: res.frequency,
      });
    }
    let targets: any[] = [];
    if (res.diagnosisType === 1) {
      // 报警规则,查询报警规则下的参与指标
      const currentTargets =
        alarmRuleOptions.value.find((item) => item.id === res.alarmId)?.targets ?? [];

      console.log('当前参与指标列表', currentTargets);
      targets = currentTargets.map((item) => ({
        code: item.target,
        formula: '',
        name: item.targetAlias,
        val: '',
      }));
      indicatorAlarmList.value = targets;
    }
    updateSchema([
      { field: 'alarmId', ifShow: res.diagnosisType === 1 }, //报警规则
      {
        field: 'frequency',
        ifShow: res.diagnosisType === 2 && res?.triggerType === 2,
      }, // 触发频率
      {
        field: 'triggerFrequency',
        ifShow: res.diagnosisType === 2 && res?.triggerType === 1 && res?.ruleType === 2,
      },
      { field: 'indicatorList', show: res.diagnosisType === 2 && res?.list.length > 0 }, // 参与指标
      { field: 'indicatorAlarmList', show: res.diagnosisType === 1 && targets.length > 0 }, // 报警参与指标
      { field: 'formula', ifShow: res.diagnosisType === 2 && res?.triggerType === 1 }, //触发条件
      { field: 'blank2', ifShow: res.diagnosisType }, // 占位符-内容信息
      // // { field: 'aiStatus', ifShow: true }, //AI状态
      { field: 'exceptionReason', ifShow: res.diagnosisType === 1 }, // 异常原因
      { field: 'suggestion', ifShow: res.diagnosisType === 1 }, // 诊断建议
      { field: 'content', ifShow: res.diagnosisType === 2 }, //诊断内容
    ]);
    // 获取表格列配置
    if (
      (res.diagnosisType === 2 && res.triggerType === 1 && res.ruleType === 1) ||
      (res.diagnosisType === 2 && res.triggerType === 1 && res.ruleType === 2 && !res.isEdit)
    ) {
      const list = formulaToArr(res.formula);
      const dynamicFormList: any[] = [];
      list.map((item, index) => {
        const idx = Math.floor(index / 4);
        const indx = index % 4;
        if (indx === 0) dynamicFormList.push([]);
        dynamicFormList[idx][indx] = item;
      });

      if (res.ruleType === 1) {
        // 一般规则
        dynamicForm.list = dynamicFormList.map((item) => {
          return {
            code: item[0][1],
            name: item[0][2],
            action: item[1][0],
            valueCount: item[2][2],
            minValue: item[2][1],
            maxValue: item[3][1],
            type: item[2][0],
          };
        });
      }
      if (res.ruleType === 2) {
        // 特殊规则
        dynamicForm2.list = dynamicFormList.map((item) => {
          const minName = indicatorList.value.find((i: any) => i.code === String(item[2][1]))?.name;
          const maxName = indicatorList.value.find((i: any) => i.code === String(item[3][1]))?.name;
          return {
            code: item[0][1],
            name: item[0][2],
            action: item[1][0],
            valueCount: item[2][2],
            minValue: item[2][0] == 1 ? item[2][1] : minName,
            maxValue: item[2][0] == 1 ? item[3][1] : maxName,
            type: item[2][0],
          };
        });
      }
      console.log('公式', dynamicFormList);
    }

    setTableData(res.list ? res.list : []);
  };
</script>

<style scoped lang="less">
  .easy-rule,
  .custom-rule {
    :deep(.ant-form-item) {
      margin-bottom: 0;
    }
  }

  .indicator-table {
    :deep(.ant-table-wrapper) {
      margin: 0;
      padding: 0;
      border-left: none;
      border-right: none;
      border-bottom: none;
    }
  }

  .indicator-alarm-form {
    :deep(.ant-row) {
      .ant-col {
        &:nth-child(2n + 1) {
          padding-right: 50px;
        }

        &:nth-child(2n) {
          margin-left: -38px;
          max-width: calc(50% + 38px);
          flex: 0 0 calc(50% + 38px);
        }
      }
    }
  }

  .container {
    .rule-contianer {
      display: flex;
      align-items: flex-start;
      gap: 0 16px;
    }
  }
</style>
