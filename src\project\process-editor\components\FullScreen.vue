<template>
  <div @click="open">
    <IconButton
      :tooltip="getTitle"
      hoverColor="rgba(255,255,255,0.1)"
      :icon="`icon-park-outline:${isFullscreen ? 'overall-reduction' : 'full-screen'}`"
    />
  </div>
</template>
<script lang="ts">
  import { defineComponent, computed, unref, ref, PropType } from 'vue';
  import { useRouter } from 'vue-router';
  import QueryString from 'qs';
  import { useFullscreen, MaybeElementRef } from '@vueuse/core';
  import { IconButton } from '/@/components/Button';
  import { createLocalStorage } from '/@/utils/cache';
  import { getToken } from '/@/utils/auth';
  import { TENANTID_KEY } from '/@/enums/cacheEnum';
  import { openWindow } from '/@/utils';
  import { getFactoryId } from '/@process-editor/utils/index.ts';
  // 此组件在大屏使用
  export default defineComponent({
    name: 'FullScreen',
    components: {
      IconButton,
    },
    props: {
      el: {
        type: Object as PropType<MaybeElementRef>,
        default: () => undefined,
      },
      flowId: {
        type: String,
        default: '',
      },
    },

    setup(props) {
      const showTooltip = ref(false);
      const { toggle, isFullscreen } = useFullscreen(props.el);
      // 重新检查全屏状态
      isFullscreen.value = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );

      const getTitle = computed(() => {
        return unref(isFullscreen) ? '退出全屏' : '全屏';
      });

      const router = useRouter();
      const ls = createLocalStorage();
      const tenantId = ls.get(TENANTID_KEY) || '';

      function open() {
        const routerLocation = router.resolve({
          name: 'process-preview-new',
          path: '/process-preview-new',
          query: {
            flowId: props.flowId,
            factoryId: getFactoryId(),
          },
        });
        const source = routerLocation.href.split('?');
        const url = source[0];
        const query = source[1];

        const params = Object.assign(QueryString.parse(query), {
          token: getToken(),
          tenantId,
          isIframe: true,
        });
        delete params.DEPARTMENT;

        const src = url + QueryString.stringify(params, { addQueryPrefix: true });
        openWindow(src, { target: '_blank' });
      }

      return {
        getTitle,
        isFullscreen,
        toggle,
        showTooltip,
        open,
      };
    },
  });
</script>
