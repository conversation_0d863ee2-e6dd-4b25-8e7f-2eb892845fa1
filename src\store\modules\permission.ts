import type { AppRouteRecordRaw, Menu } from '/@/router/types';

import { defineStore } from 'pinia';
import { store } from '/@/store';
import { useI18n } from '/@/hooks/web/useI18n';
import { useUserStore } from './user';
import { useAppStoreWithOut } from './app';
import { toRaw } from 'vue';
import {
  processRouteList,
  flatMultiLevelRoutes,
  routeRemoveIgnoreFilter,
} from '/@@/router/helper/routeHelper';
import { transformRouteToMenu, findMenuByModuleId } from '/@/router/helper/menuHelper';

import projectSetting from '/@/settings/projectSetting';

import { PermissionModeEnum } from '/@/enums/appEnum';

import { asyncRoutes } from '/@/router/routes';
import { ERROR_LOG_ROUTE } from '/@/router/routes/basic';

import { filter } from '/@/utils/helper/treeHelper';

import { GetCustomPageBtnAuth } from '/@/api/admin/auth';

import { useMessage } from '/@/hooks/web/useMessage';
import { MENUID_KEY } from '/@/enums/cacheEnum';
import { createSessionStorage } from '/@/utils/cache';
import { RootRoute } from '/@@/router/routes';
import { getModuleFirstListApi, getMenuListApi } from '/@/api/admin/menu';
import { merge } from 'lodash-es';
import { router } from '/@/router';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { flattenTree } from '/@/utils';

// import patrol from '/@@/router/routes/modules/patrol';

const ss = createSessionStorage();

interface FirstMenuParams {
  moduleId?: string | null;
  redirect?: string | null;
  title?: string | null;
  icon?: string;
  [key: string]: any;
}

interface PermissionState {
  // Permission code list
  // 权限代码列表
  permCodeList: string[] | number[];
  // Whether the route has been dynamically added
  // 路由是否动态添加
  isDynamicAddedRoute: boolean;
  // To trigger a menu update
  // 触发菜单更新
  lastBuildMenuTime: number;
  // Backstage menu list
  // 后台菜单列表
  backMenuList: Menu[];
  // 菜单列表
  frontMenuList: Menu[];
  // 一级菜单列表
  firstMenuList: Menu[];
  // 一级菜单id和首页地址，便于切换，刷新
  firstMenuParams: Nullable<FirstMenuParams>;
  // iframe路由列表
  framePagesList: AppRouteRecordRaw[];
}

export const usePermissionStore = defineStore({
  id: 'app-permission',
  state: (): PermissionState => ({
    // 权限代码列表
    permCodeList: [],
    // Whether the route has been dynamically added
    // 路由是否动态添加
    isDynamicAddedRoute: false,
    // To trigger a menu update
    // 触发菜单更新
    lastBuildMenuTime: 0,
    // Backstage menu list
    // 后台菜单列表
    backMenuList: [],
    // menu List
    // 菜单列表
    frontMenuList: [],
    // 全部菜单列表
    firstMenuList: [],
    firstMenuParams: {
      moduleId: null,
      redirect: '',
      icon: '',
    },
    framePagesList: [],
  }),
  getters: {
    getPermCodeList(): string[] | number[] {
      return this.permCodeList;
    },
    getBackMenuList(): Menu[] {
      // slider menus
      return this.backMenuList;
      // const backMenuList = this.getFirstMenuList.find(
      //   (item) => this.getFirstMenuParams?.moduleId == item.meta?.moduleId,
      // );

      // return backMenuList?.children || [];
    },
    getFirstMenuList(): Menu[] {
      return this.firstMenuList;
    },
    getFrontMenuList(): Menu[] {
      return this.frontMenuList;
    },
    getLastBuildMenuTime(): number {
      return this.lastBuildMenuTime;
    },
    getIsDynamicAddedRoute(): boolean {
      return this.isDynamicAddedRoute;
    },
    getFirstMenuParams(): FirstMenuParams | null {
      return this.firstMenuParams;
    },
    getFramePagesList(): AppRouteRecordRaw[] {
      return this.framePagesList;
    },
  },
  actions: {
    setPermCodeList(codeList: string[]) {
      this.permCodeList = codeList;
    },

    setBackMenuList(list: Menu[]) {
      this.backMenuList = list.filter((el) => !el.hideMenu && !el.meta?.hideMenu);
      list?.length > 0 && this.setLastBuildMenuTime();
    },

    setFirstMenuList(list: Menu[]) {
      this.firstMenuList = list;
    },

    setFrontMenuList(list: Menu[]) {
      this.frontMenuList = list;
    },

    setLastBuildMenuTime() {
      this.lastBuildMenuTime = new Date().getTime();
    },

    setDynamicAddedRoute(added: boolean) {
      this.isDynamicAddedRoute = added;
    },

    setFirstMenuParams(params: FirstMenuParams | null) {
      this.firstMenuParams = params && { ...this.firstMenuParams, ...params };
      const id = params?.moduleId;
      ss.set(MENUID_KEY, id || null);
    },

    setFamePagesList(list: AppRouteRecordRaw[]) {
      this.framePagesList = list;
    },

    resetState(): void {
      this.isDynamicAddedRoute = false;
      this.permCodeList = [];
      this.backMenuList = [];
      this.framePagesList = [];
      this.firstMenuList = [];

      this.lastBuildMenuTime = 0;
    },
    async changePermissionCode(moduleId) {
      const codeList = (await GetCustomPageBtnAuth(moduleId)) || [];
      this.setPermCodeList(codeList);
    },

    patchHomeAffix() {
      const routes = router.getRoutes() as AppRouteRecordRaw[];

      const userStore = useUserStore();
      let homePath: string = this.getFirstMenuParams?.redirect || userStore.getUserInfo.homePath;

      function patcher(routes: AppRouteRecordRaw[], parentPath = '') {
        if (parentPath) parentPath = parentPath + '/';
        routes.forEach((route: AppRouteRecordRaw) => {
          const { path, children, redirect } = route;
          const currentPath = path.startsWith('/') ? path : parentPath + path;

          if (currentPath === homePath) {
            if (redirect) {
              homePath = route.redirect! as string;
            } else {
              route.meta = Object.assign(route.meta, { affix: true });

              throw new Error('end');
            }
          }
          children && children.length > 0 && patcher(children, currentPath);
        });
      }

      try {
        patcher(routes);
      } catch (e) {
        // 已处理完毕跳出循环
      }
      return;
    },

    // 构建路由 isRefresh 是否是路由刷新
    async buildRoutesAction(
      specialUser = false,
      isRefresh = false,
      singleLogin = false,
    ): Promise<AppRouteRecordRaw[]> {
      const { t } = useI18n();
      const userStore = useUserStore();
      const appStore = useAppStoreWithOut();
      const { getIsPlatformMode } = useMenuSetting();

      let routes: AppRouteRecordRaw[] = [];
      const roleList = toRaw(userStore.getRoleList) || [];
      const { permissionMode = projectSetting.permissionMode } = appStore.getProjectConfig;

      userStore.setSpecialUser(specialUser);

      // 路由过滤器 在 函数filter 作为回调传入遍历使用
      const routeFilter = (route: AppRouteRecordRaw) => {
        const { meta } = route;
        // 抽出角色
        const { roles } = meta || {};
        if (!roles) return true;
        // 进行角色权限判断
        return roleList.some((role) => roles.includes(role));
      };

      switch (permissionMode) {
        // 角色权限
        case PermissionModeEnum.ROLE:
          // 对非一级路由进行过滤
          routes = filter(asyncRoutes, routeFilter);
          // 对一级路由根据角色权限过滤
          routes = routes.filter(routeFilter);
          // Convert multi-level routing to level 2 routing
          // 将多级路由转换为 2 级路由
          routes = flatMultiLevelRoutes(routes);
          break;

        // 路由映射， 默认进入该case
        case PermissionModeEnum.ROUTE_MAPPING:
          // 对非一级路由进行过滤
          routes = filter(asyncRoutes, routeFilter);
          // 对一级路由再次根据角色权限过滤
          routes = routes.filter(routeFilter);
          // 将路由转换成菜单
          const menuList = transformRouteToMenu(routes, true);
          // 移除掉 ignoreRoute: true 的路由 非一级路由
          routes = filter(routes, routeRemoveIgnoreFilter);
          // 移除掉 ignoreRoute: true 的路由 一级路由；
          routes = routes.filter(routeRemoveIgnoreFilter);
          // 对菜单进行排序
          menuList.sort((a, b) => {
            return (a.meta?.orderNo || 0) - (b.meta?.orderNo || 0);
          });

          // 设置菜单列表
          this.setFrontMenuList(menuList);

          // Convert multi-level routing to level 2 routing
          // 将多级路由转换为 2 级路由
          routes = flatMultiLevelRoutes(routes);
          break;

        //  If you are sure that you do not need to do background dynamic permissions, please comment the entire judgment below
        //  如果确定不需要做后台动态权限，请在下方注释整个判断
        case PermissionModeEnum.BACK:
          if (!specialUser && !singleLogin) {
            const { createMessage } = useMessage();

            createMessage.loading({
              content: t('sys.app.menuLoading'),
              duration: 1,
            });
          }

          // !Simulate to obtain permission codes from the background,
          // 模拟从后台获取权限码，
          // this function may only need to be executed once, and the actual project can be put at the right time by itself
          // 这个功能可能只需要执行一次，实际项目可以自己放在合适的时间
          let childrens: AppRouteRecordRaw[][] = [];
          let _redirect: string | undefined = '';

          // if (specialUser) {
          //   const routeList = (await getMenuListApi({
          //     parentId: -1,
          //   })) as AppRouteRecordRaw[];

          //   childrens = [processRouteList(routeList)];
          // } else {}
          try {
            let moduleId = ss.get(MENUID_KEY) ?? this.getFirstMenuParams?.moduleId;

            const res = (await getModuleFirstListApi()) as AppRouteRecordRaw[];
            if (!res.length) return routes;
            const AllMenuList = transformRouteToMenu(res);

            childrens = await Promise.all(
              AllMenuList.map((item) => getMenuListApi({ parentId: item.meta.moduleId as any })),
            );
            //  子菜单的获取
            childrens = childrens.map((item) => processRouteList(item));

            // 合并至主菜单
            merge(
              AllMenuList,
              childrens.map((children) => ({ children: transformRouteToMenu(children) })),
            );
            this.setFirstMenuList(AllMenuList);
            let redirect;
            let title;
            let icon;
            if (!moduleId) {
              this.setBackMenuList(AllMenuList[0].children || []);
              moduleId = AllMenuList[0]?.meta?.moduleId;

              redirect = AllMenuList[0].redirect;
              title = AllMenuList[0].title;
              icon = AllMenuList[0].quickNavigationIcon;
            } else {
              if (getIsPlatformMode.value) {
                // 平台模式 只获取当前模块的菜单
                const menu = findMenuByModuleId(moduleId, AllMenuList);
                if (menu) {
                  this.setBackMenuList(menu.children || []);
                  redirect = isRefresh ? menu.redirect : this.getFirstMenuParams?.redirect;
                  title = isRefresh ? menu.meta?.title : this.getFirstMenuParams?.title;
                }
              } else {
                if (singleLogin) {
                  const ret = this.getTargetMenuList(AllMenuList, moduleId);
                  if (ret) {
                    const { node, menu } = ret;
                    this.setBackMenuList(node.children || []);

                    redirect = menu.path;
                    title = menu.title;
                    icon = menu.quickNavigationIcon;
                  }
                } else {
                  const menuInfo = AllMenuList.filter(
                    (el) => !el.hideMenu && !el.meta?.hideMenu,
                  ).find((el) => el.moduleId === moduleId);

                  if (menuInfo) {
                    this.setBackMenuList(menuInfo.children || []);
                    redirect = isRefresh ? menuInfo.redirect : this.getFirstMenuParams?.redirect;
                    title = isRefresh ? menuInfo.meta?.title : this.getFirstMenuParams?.title;
                    icon = isRefresh
                      ? menuInfo?.quickNavigationIcon
                      : this.getFirstMenuParams?.icon;
                  }
                }
              }
            }

            const flatAllMenuList = flattenTree(AllMenuList[0]?.children || []);
            // 获取重定向

            if (redirect) {
              const __redirect = flatAllMenuList.find((item) => item.path === redirect);
              if (__redirect) {
                _redirect = redirect;
              } else {
                const filterAllMenuList = flatAllMenuList.filter(
                  (item) => item.redirect !== redirect,
                );
                const currentRedirect = filterAllMenuList.find((item) => item.redirect)?.redirect;
                _redirect = currentRedirect;
              }
            }

            const menuParams: FirstMenuParams = {
              moduleId,
              title,
              redirect: _redirect,
              icon,
            };

            this.setFirstMenuParams(menuParams);

            // 无界临时测试菜单
            // childrens[0].push(patrol);
          } catch (error) {
            console.error(error);
          }

          this.setLastBuildMenuTime();

          // 替换根路由
          // RootRoute.redirect = this.getFirstMenuParams?.redirect || RootRoute.redirect;
          RootRoute.redirect = _redirect || RootRoute.redirect;

          // 生成路由
          routes = [...childrens.map((item) => flatMultiLevelRoutes(item)).flat()];

          break;
      }

      routes.push(ERROR_LOG_ROUTE);

      return routes;
    },
    getTargetMenuList(source: Menu[], targetModuleId: string) {
      const list = source.filter((el) => !el.hideMenu && !el.meta?.hideMenu);
      //   this.setBackMenuList(menuInfo.children || []);
      for (const node of list) {
        if (node.children) {
          const menu = node.children.find((child) => {
            if (child.moduleId === targetModuleId) {
              return true;
            }
          });
          if (menu) {
            return { node, menu };
          }
        }
        const result = this.getTargetMenuList(node.children || [], targetModuleId);
        if (result) {
          return result;
        }
      }
    },
  },
});

// Need to be used outside the setup
// 需要在设置之外使用
export function usePermissionStoreWithOut() {
  return usePermissionStore(store);
}
