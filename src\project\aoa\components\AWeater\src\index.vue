<template>
  <div :class="['weather-box', theme]">
    <div class="weather">
      <div class="icon-wrapper" v-if="info.icon">
        <i :class="`qi-${info.icon}`"></i>
      </div>
      <div class="temp-container">
        <div class="temp"> {{ info.temp !== '' ? info.temp : '-' }}°C </div>
        <div class="text">
          {{ info.text }}
        </div>
      </div>
    </div>
    <div class="weather-info">
      <div class="weather-info-container">
        <div class="weather-info-item">
          <div class="weather-info-item-label">
            <img src="./assets/images/icon.png" style="width: 16px" />
            湿度
          </div>
          <div class="weather-info-item-value">{{
            info.humidity !== '' ? `${info.humidity}%` : '-'
          }}</div>
        </div>
        <div class="weather-info-item">
          <div class="weather-info-item-label">
            <Icon icon="icon-park-outline:wind" color="rgba(81, 199, 234, 1)" size="16" />
            风力
          </div>
          <div class="weather-info-item-value">{{
            info.windScale !== '' ? `${info.windScale}级` : '-'
          }}</div>
        </div>
      </div>
      <div class="weather-info-tip">温馨提示：{{ tipMap[weatherType] }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import dayjs from 'dayjs';
  import { Icon } from '/@/components/Icon';
  import { getWeather24HApi, getWeather3DApi } from '/@aoa/api/weather';

  defineProps({
    theme: {
      type: String,
      default: 'light',
    },
  });

  const info = ref({
    text: '',
    icon: '',
    temp: '',
    windScale: '',
    humidity: '',
  });

  const tipMap = {
    [0]: '明日天气未知',
    [1]: '明日天晴，记得关注天气',
    [2]: '明日有雨，记得关注天气',
    [3]: '明日多云，记得关注天气',
    [4]: '明日阴，记得关注天气',
  };

  const weatherType = ref(0);

  const textTransformType = (text) => {
    if (!text) return 0;
    return text.includes('晴')
      ? 1
      : text.includes('雨')
      ? 2
      : text.includes('多云')
      ? 3
      : text.includes('阴')
      ? 4
      : 0;
  };

  const getWeather = async () => {
    try {
      const { data } = await getWeather24HApi();
      const HOUR = 'HH';
      const findItem = data?.hourly?.find(
        (i) => dayjs(i.fxTime).format(HOUR) === dayjs().format(HOUR),
      );

      info.value = {
        text: findItem?.text ?? '',
        icon: findItem?.icon ?? '',
        temp: findItem?.temp ?? '',
        windScale: findItem?.windScale ?? '',
        humidity: findItem?.humidity ?? '',
      };
    } catch (error) {
      throw error;
    }
  };

  const get3DWeather = async () => {
    try {
      const { data } = await getWeather3DApi();
      const DAY = 'DD';
      const findItem = data?.daily?.find(
        (i) => dayjs(i.fxTime).format(DAY) === dayjs().format(DAY),
      );
      weatherType.value = textTransformType(findItem?.textDay);
    } catch (error) {
      throw error;
    }
  };

  onMounted(() => {
    getWeather();
    get3DWeather();
  });
</script>

<style lang="less" scoped>
  @import '/@aoa/assets/css/font.less';
  @import '/@aoa/assets/qweather-icons/qweather-icons.less';

  .weather-box {
    display: flex;
    align-items: center;
    font-family: PingFang SC;
    font-weight: 400;
    .font-size(14);
    color: #333;

    &.dark {
      color: #ffffff;
    }

    .weather {
      display: flex;
      align-items: center;

      .icon-wrapper {
        .font-size(36);
      }

      .temp-container {
        padding-left: 8px;

        .temp {
          font-family: Alimama ShuHeiTi;
          font-weight: 500;
          .font-size(22);
          .height-prop(22, line-height);
          text-shadow: 0px 2px 1px #01120f;
        }

        .text {
          .height-prop(5, padding-top);
          text-shadow: 0px 2px 1px #01120f;
        }
      }
    }

    .weather-info {
      .width-prop(50, padding-left);

      .weather-info-container {
        display: flex;
        align-items: center;
        gap: 0 20px;
        line-height: 16px;

        .weather-info-item {
          display: flex;
          align-items: center;

          .weather-info-item-label {
            display: flex;
            gap: 0 4px;
            text-shadow: 0px 2px 1px #01120f;
          }

          .weather-info-item-value {
            font-family: Alimama ShuHeiTi;
            .font-size(16);
            .width-prop(20, padding-left);
            font-weight: 500;
            text-shadow: 0px 2px 1px #01120f;
          }
        }
      }

      .weather-info-tip {
        .height-prop(10, padding-top);
        text-shadow: 0px 2px 1px #01120f;
      }
    }
  }

  @media screen and (max-width: 1599px) {
    .weather-box {
      .weather {
        .icon-wrapper {
          font-size: 32px;
        }

        .temp-container {
          .temp {
            font-size: 18px;
            line-height: 18px;
          }
        }
      }

      .weather-info {
        padding-left: 24px;
      }
    }
  }
</style>
