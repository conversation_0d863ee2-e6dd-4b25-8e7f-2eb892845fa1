import type { MultiTabsSetting, TypeTabsSetting } from '/#/config';
import { TabsTypeEnum } from '/@/enums/menuEnum';
import { computed, unref } from 'vue';

import { useAppStore } from '/@/store/modules/app';
import { useMenuSetting } from './useMenuSetting';

export function useMultipleTabSetting() {
  const appStore = useAppStore();
  const {
    getIsMixMode,
    getIsMixSidebar,
    getIsSidebarType,
    getIsTopMenu,
    getIsPlatformMode,
    getIsMoreSystemMode,
  } = useMenuSetting();

  const tabsSettingMap = {
    mixTabsSetting: getIsMixMode,
    topMenuTabsSetting: getIsTopMenu,
    sidebarTabsSetting: getIsSidebarType,
    mixSidebarTabsSetting: getIsMixSidebar,
    platformTabsSetting: getIsPlatformMode,
    moreSystemTabsSetting: getIsMoreSystemMode,
  };

  const getTypeTabsSetting = computed(() => {
    for (const key in tabsSettingMap) {
      if (unref(tabsSettingMap[key])) {
        return appStore.getProjectConfig[key] || {};
      }
    }

    return {} as TypeTabsSetting;
  });

  const setTypeTabsSetting = (typeTabsSetting: Partial<TypeTabsSetting>) => {
    for (const key in tabsSettingMap) {
      if (unref(tabsSettingMap[key])) {
        appStore.setProjectConfig({ [key]: typeTabsSetting });
        break;
      }
    }
  };
  const getShowMultipleTab = computed(() => unref(getTypeTabsSetting).show);
  const getTabsType = computed(() => unref(getTypeTabsSetting).tabsType as TabsTypeEnum);
  const getCardTabsType = computed(() =>
    [TabsTypeEnum.CARD, TabsTypeEnum.T_CARD, TabsTypeEnum.RADIUS_CARD, TabsTypeEnum.TAG].includes(
      getTabsType.value,
    ),
  );

  const getShowQuick = computed(() => unref(getTypeTabsSetting).showQuick);

  const getShowRedo = computed(() => unref(getTypeTabsSetting).showRedo);

  const getShowFold = computed(() => unref(getTypeTabsSetting).showFold);

  const getClass = computed(() => appStore.getMultiTabsSetting.className);

  function setMultipleTabSetting(multiTabsSetting: Partial<MultiTabsSetting>) {
    appStore.setProjectConfig({ multiTabsSetting });
  }

  return {
    setMultipleTabSetting,
    getShowMultipleTab,
    getShowQuick,
    getShowRedo,
    getShowFold,
    getClass,
    getTabsType,
    getCardTabsType,
    getTypeTabsSetting,
    setTypeTabsSetting,
  };
}
