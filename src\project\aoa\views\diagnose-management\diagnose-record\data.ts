import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs, { Dayjs } from 'dayjs';
import { h } from 'vue';

// 诊断类型
export const diagnosisTypeOptions = [
  {
    value: 1,
    label: '报警诊断',
  },
  {
    value: 2,
    label: '常规诊断',
  },
];

// 触发类型
export const triggerTypeOptions = [
  {
    value: 1,
    label: '条件触发',
  },
  {
    value: 2,
    label: '定时触发',
  },
];

export const columns: BasicColumn[] = [
  {
    title: '记录编码',
    dataIndex: 'recordCode',
    width: 220,
  },
  {
    title: '诊断规则名称',
    dataIndex: 'ruleName',
  },
  // {
  //   title: 'AI状态',
  //   dataIndex: 'aiStatus',
  // },
  {
    title: '诊断类型',
    dataIndex: 'diagnosisDescription',
  },
  {
    title: '触发类型',
    dataIndex: 'triggerTypeDescription',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    customRender: ({ record }) => {
      return dayjs(Number(record.createTime)).format('YYYY-MM-DD HH:mm:ss');
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'ruleName',
    label: '规则名称',
    labelWidth: 68,
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'diagnosisType',
    label: '诊断类型',
    component: 'Select',
    componentProps: {
      options: diagnosisTypeOptions,
      placeholder: '全部',
      class: 'placeholder-all',
    },
    colProps: { span: 6 },
  },
  {
    field: 'triggerType',
    label: '触发类型',
    component: 'Select',
    componentProps: {
      options: triggerTypeOptions,
      placeholder: '全部',
      class: 'placeholder-all',
    },
    colProps: { span: 6 },
  },
  {
    field: 'time',
    label: '记录日期',
    component: 'RangePicker',
    componentProps: {
      showTime: false,
      format: 'YYYY-MM-DD',
      disabledDate: (current: Dayjs) => current && current > dayjs().endOf('day'),
    },
    // slot: 'time',
    // defaultValue: [dayjs(), dayjs()],
    colProps: { span: 6 },
  },
];

export const schemas: FormSchema[] = [
  {
    field: 'blank',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    render: () => {
      return h(
        'div',
        {
          style: {
            borderBottom: '1px solid #eeeff1',
            paddingBottom: '6px',
            display: 'flex',
            alignItems: 'center',
          },
        },
        [
          h(
            'span',
            {
              style: {
                width: '4px',
                height: '14px',
                background: 'var(--theme-color)',
                display: 'inline-block',
              },
            },
            '',
          ),
          h(
            'span',
            { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
            '基础信息',
          ),
        ],
      );
    },
  },
  {
    field: 'ruleCode',
    component: 'Input',
    label: '诊断编码',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'ruleName',
    component: 'Input',
    label: '规格名称',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'diagnosisType',
    component: 'Select',
    label: '诊断类型',
    componentProps: {
      disabled: true,
    },
    slot: 'diagnosis-type-slot',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'triggerType',
    component: 'Select',
    label: '触发类型',
    componentProps: {
      disabled: true,
    },
    slot: 'trigger-type-slot',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'alarmId',
    component: 'Select',
    label: '报警规则',
    componentProps: {
      disabled: true,
    },
    slot: 'alarm-slot',
    colProps: {
      span: 24,
    },
    ifShow: false,
  },
  {
    field: 'frequency',
    component: 'InputNumber',
    label: '触发频率(min)',
    componentProps: {
      disabled: true,
      min: 1,
      precision: 0,
    },
    colProps: {
      span: 12,
    },
    defaultValue: 60,
    ifShow: false,
  },
  {
    field: 'triggerFrequency',
    component: 'InputNumber',
    label: '触发间隔(min)',
    componentProps: {
      disabled: true,
      min: 1,
      precision: 0,
    },
    colProps: {
      span: 12,
    },
    defaultValue: 60,
    ifShow: false,
  },
  {
    field: 'indicatorAlarmList',
    component: 'Input',
    label: '参与指标', // 报警参与指标
    slot: 'indicator-alarm-list-slot',
    colProps: { span: 24 },
    show: false,
  },
  {
    field: 'indicatorList',
    component: 'Input',
    label: '参与指标', // 常规参与指标
    slot: 'indicator-list-slot',
    colProps: { span: 24 },
    show: false,
  },
  {
    field: 'formula',
    component: 'Input',
    label: '触发条件',
    componentProps: {
      disabled: true,
    },
    slot: 'formula-slot',
    colProps: { span: 24 },
    ifShow: false,
  },
  {
    field: 'blank2',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    render: () => {
      return h(
        'div',
        {
          style: {
            borderBottom: '1px solid #eeeff1',
            paddingBottom: '6px',
            display: 'flex',
            alignItems: 'center',
          },
        },
        [
          h(
            'span',
            {
              style: {
                width: '4px',
                height: '14px',
                background: 'var(--theme-color)',
                display: 'inline-block',
              },
            },
            '',
          ),
          h(
            'span',
            { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
            '内容信息',
          ),
        ],
      );
    },
    ifShow: false,
  },
  {
    field: 'exceptionReason',
    component: 'InputTextArea',
    label: '异常原因',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 24,
    },
    ifShow: false,
  },
  {
    field: 'suggestion',
    component: 'InputTextArea',
    label: '诊断建议',
    slot: 'suggestion-slot',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 24,
    },
    ifShow: false,
  },
  {
    field: 'content',
    component: 'InputTextArea',
    label: '诊断内容',
    slot: 'content-slot',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 24,
    },
    ifShow: false,
  },
];

export const indicatorColumns: BasicColumn[] = [
  {
    title: '指标名称',
    dataIndex: 'name',
  },
  {
    title: '指标标识',
    dataIndex: 'code',
  },
  {
    title: '计算公式',
    dataIndex: 'formula',
    ellipsis: false,
    customRender: ({ record }) => {
      return record.formula ? record.formula : '-';
    },
  },
  {
    title: '记录值',
    dataIndex: 'val',
  },
];
