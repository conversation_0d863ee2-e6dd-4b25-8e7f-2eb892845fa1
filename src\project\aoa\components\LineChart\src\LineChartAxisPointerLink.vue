<template>
  <div
    class="line-chart-axis-pointer-link w-full h-full flex flex-col gap-16px"
    v-loading="loading"
  >
    <div class="w-full h-full flex justify-center items-center" v-if="isEmpty">
      <HEmpty />
    </div>
    <template v-else>
      <div class="chart flex-1" ref="chartRef1"></div>
      <div class="chart flex-1" ref="chartRef2"></div>
      <div class="chart flex-1" ref="chartRef3"></div>
    </template>
  </div>
</template>

<script setup lang="ts">
  // import dayjs from 'dayjs/esm';
  import { computed, onUnmounted } from 'vue';
  import echarts from '/@/utils/lib/echarts';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { ref, Ref, PropType, nextTick, watch } from 'vue';
  import { useChartsData, type Params } from './useChartsData';
  import { uniqBy, values, flatMap, uniqueId } from 'lodash-es';
  import { getMaxValue, getMinValue, getChartsGrid, colorList, mockTime } from '/@aoa/utils';
  // import { TimeTypeMap } from '/@aoa/components/SearchForm';
  import { getValue } from '/@aoa/utils/string';

  const props = defineProps({
    api: {
      type: Function,
      required: true,
    },
    params: {
      type: Object as PropType<Params>,
      required: true,
    },
  });

  const chartRef1 = ref<HTMLDivElement | null>(null);
  const chartRef2 = ref<HTMLDivElement | null>(null);
  const chartRef3 = ref<HTMLDivElement | null>(null);
  const { chartsData, loading } = useChartsData(props);

  const { setOptions: setOptions1, getInstance: getInstance1 } = useECharts(
    chartRef1 as Ref<HTMLDivElement>,
  );
  const { setOptions: setOptions2, getInstance: getInstance2 } = useECharts(
    chartRef2 as Ref<HTMLDivElement>,
  );
  const { setOptions: setOptions3, getInstance: getInstance3 } = useECharts(
    chartRef3 as Ref<HTMLDivElement>,
  );

  const isEmpty = computed(() => {
    if (loading.value) return false;
    const allDatas = flatMap(values(chartsData), 'datas');
    return !allDatas.length;
  });

  async function renderEcharts() {
    if (isEmpty.value) return;
    await nextTick();

    // const { format, format2 } = TimeTypeMap[props.params.timeType];
    const { nowData, sameData, compareData } = chartsData;

    const dataArr = [
      { ...nowData, suffix: '', option: {} },
      { ...compareData, suffix: '-环比', option: {} },
      { ...sameData, suffix: '-同比', option: {} },
    ];

    const firstUniqUnit = uniqBy(nowData.datas, 'unit');

    const allMax = getMaxValue(nowData.datas);
    const { gridLeft, gridRight } = getChartsGrid(firstUniqUnit, allMax);

    const instance1 = getInstance1();
    const instance2 = getInstance2();
    const instance3 = getInstance3();

    dataArr.forEach((item) => {
      const itemUniqUnit = uniqBy(item.datas, 'unit');

      const yAxisList = itemUniqUnit.map((val, key) => {
        // const mergeDatas = item.datas
        //   .filter((i) => i.unit === val.unit)
        //   .map((i) => i.data)
        //   .flat();
        // const othersDatas = dataArr
        //   .map((i) => i.datas)
        //   .flat()
        //   .filter((i) => i.unit === val.unit)
        //   .map((i) => i.data)
        //   .flat();
        const itemDatas = item.datas
          .filter((i) => i.unit === val.unit)
          .map((i) => i.data)
          .flat();

        let max = getMaxValue(itemDatas, true);
        let min = getMinValue(itemDatas, true);

        let offset = Math.floor(key / 2) * 72;

        return {
          offset,
          position: key % 2 == 0 ? 'left' : 'right',
          unit: val.unit,
          type: 'value',
          name: val.unit ? ` 单位(${val.unit})` : '',
          min,
          max,
          splitNumber: 5,
          interval: (max - min) / 5,
          nameTextStyle: {
            color: '#999999',
            align: itemUniqUnit.length > 2 ? (key % 2 == 0 ? 'right' : 'left') : 'center',
            // align:
            //   key % 2 === 0 ? (max > 1000 ? 'right' : 'center') : max > 1000 ? 'left' : 'center',
            padding: max.toString().length > 3 ? [0, 20, 0, 0] : [0, 0, 0, 0],
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: '#333',
            fontSize: '12px',
            formatter: (value: string) => {
              return getValue(value, val.unit);
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#A1D0C4',
              type: [5, 10],
              offset: 5,
            },
          },
        };
      });
      const option: any = {
        tooltip: {
          trigger: 'axis',
          appendToBody: true,
          axisPointer: {
            animation: false,
          },
          showContent: false,
          backgroundColor: '#E1F3F1',
          borderColor: '#02695E',
          // backgroundColor: 'rgba(255, 255, 255, 0.8)',
          formatter: (params: Recordable) => {
            // 当前数据
            const linkedData1: any = instance1?.getOption()?.series || [];
            // 环比
            const linkedData2 = instance2?.getOption()?.series || [];
            // 同比
            const linkedData3 = instance3?.getOption()?.series || [];
            const thead = `
              <table style="text-align: left;">
                <thead>
                  <tr>
                    <th style="color: #333">${params[0]?.axisValue}</th>
                    <th style="min-width: 50px;"></th>
                    <th style="min-width: 100px;color: #333">当前</th>
                    <th style="min-width: 100px;color: #333">环比</th>
                    <th style="min-width: 100px;color: #333">同比</th>
                  </tr>
                </thead>
                <tbody>
              `;
            const tbody = params
              .map((param) => {
                const name = param.seriesName?.includes('-同比')
                  ? param.seriesName?.split('-同比')[0]
                  : param.seriesName?.includes('-环比')
                  ? param.seriesName?.split('-环比')[0]
                  : param.seriesName;
                const unit = dataArr[0]?.datas.find((i) => i.indexTitie === name)?.unit;
                const dataIndex = linkedData1?.findIndex((i) => i.name === name) || 0;
                return `<tr>
                  <td>${param.marker}${linkedData1[dataIndex]?.name}</td>
                  <td style="min-width: 50px;"></td>
                  <td style="min-width: 100px;line-height: 2">
                    <span style="color: #333 ;font-weight: 600">${getValue(
                      linkedData1[dataIndex]?.data[param.dataIndex],
                      unit,
                    )}</span>&nbsp;&nbsp;
                    ${
                      linkedData1[dataIndex]?.data[param.dataIndex] !== null &&
                      linkedData1[dataIndex]?.data[param.dataIndex] !== undefined &&
                      linkedData1[dataIndex]?.data[param.dataIndex] !== ''
                        ? unit
                        : ''
                    }
                  </td>
                  <td style="min-width: 100px;line-height: 2">
                    <span style="color: #333 ;font-weight: 600">${getValue(
                      linkedData2[dataIndex]?.data[param.dataIndex],
                      unit,
                    )}</span>&nbsp;&nbsp;
                    ${
                      linkedData2[dataIndex]?.data[param.dataIndex] !== null &&
                      linkedData2[dataIndex]?.data[param.dataIndex] !== undefined &&
                      linkedData2[dataIndex]?.data[param.dataIndex] !== ''
                        ? unit
                        : ''
                    }
                  </td>
                  <td style="min-width: 100px;line-height: 2">
                    <span style="color: #333 ;font-weight: 600">${getValue(
                      linkedData3[dataIndex]?.data[param.dataIndex],
                      unit,
                    )}</span>&nbsp;&nbsp;
                    ${
                      linkedData3[dataIndex]?.data[param.dataIndex] !== null &&
                      linkedData3[dataIndex]?.data[param.dataIndex] !== undefined &&
                      linkedData3[dataIndex]?.data[param.dataIndex] !== ''
                        ? unit
                        : ''
                    }
                  </td>
                </tr>`;
              })
              .join('');

            return `${thead}${tbody}</tbody></table>`;
          },
        },
        legend: {
          show: true,
          type: 'scroll',
          icon: 'circle',
          left: 0,
          itemWidth: 8,
          itemHeight: 8,
          padding: [
            0,
            gridRight + 80 * Math.floor(yAxisList.length / 2),
            0,
            gridLeft + (yAxisList.length > 2 ? 65 * Math.ceil(yAxisList.length / 2) : 85),
          ],
          textStyle: {
            color: '#999999',
            fontSize: '12px',
          },
          top: 5,
        },
        grid: {
          left: gridLeft + 2,
          right: gridRight + (props.params.timeType == 5 ? 25 : 8),
          // bottom: 24,
          bottom: 5,
          top: 32,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#59B29B',
            },
          },
          axisPointer: {
            show: true,
            type: 'line',
            lineStyle: {
              color: '#119078',
              type: [5, 10],
            },
          },
          axisLabel: {
            show: true,
            color: '#333',
            fontSize: 12,
            padding: [5, 0, 0, 0],
            formatter: (value, index) => {
              if (index === 0) {
                return `{date| ${value}}`;
              }
              return value;
            },
            rich: {
              date: {
                padding: [0, 0, 0, 30],
              },
            },
          },
          data: item.time.length ? item.time : mockTime(),
        },
        yAxis: yAxisList,
        series: item.datas.map((value, key) => ({
          color: colorList[key % colorList.length],
          name: value.indexTitie + item.suffix,
          type: 'line',
          smooth: true,
          symbol: 'none',
          symbolSize: 6,
          data: value.data,
          yAxisIndex:
            yAxisList.findIndex((val) => val.unit == value.unit) < 0
              ? 0
              : yAxisList.findIndex((val) => val.unit == value.unit),
        })),
      };

      item.option = option;
    });

    setOptions1(dataArr[0].option);
    setOptions2(dataArr[1].option);
    setOptions3(dataArr[2].option);

    const groupId = uniqueId('lineChart');
    instance1!.group = instance2!.group = instance3!.group = groupId;
    echarts.connect(groupId);

    chartRef1.value?.addEventListener('mouseenter', () => {
      instance1?.setOption({ tooltip: { showContent: true } });
      instance2?.setOption({ tooltip: { showContent: false } });
      instance3?.setOption({ tooltip: { showContent: false } });
    });
    chartRef2.value?.addEventListener('mouseenter', () => {
      instance1?.setOption({ tooltip: { showContent: false } });
      instance2?.setOption({ tooltip: { showContent: true } });
      instance3?.setOption({ tooltip: { showContent: false } });
    });
    chartRef3.value?.addEventListener('mouseenter', () => {
      instance1?.setOption({ tooltip: { showContent: false } });
      instance2?.setOption({ tooltip: { showContent: false } });
      instance3?.setOption({ tooltip: { showContent: true } });
    });
  }

  watch(chartsData, renderEcharts, {
    deep: true,
  });

  onUnmounted(() => {
    chartRef1.value?.removeEventListener('mouseenter', () => {});
    chartRef2.value?.removeEventListener('mouseenter', () => {});
    chartRef3.value?.removeEventListener('mouseenter', () => {});
  });
</script>

<style scoped lang="less">
  .line-chart-axis-pointer-link {
    padding: 16px;

    .chart {
      overflow: hidden;
    }
  }
</style>
