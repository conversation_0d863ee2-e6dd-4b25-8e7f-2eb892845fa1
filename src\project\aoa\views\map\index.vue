<template>
  <div class="map-page h-full">
    <div class="map h-full w-full" ref="mapRef">
      <div class="map-control h-full w-full" id="tiandituMap"> </div>
      <div class="search-control" ref="searchControlRef">
        <div class="map-search-control">
          <div class="input-control">
            <Input
              allowClear
              v-model:value="keyword"
              placeholder="查找水厂"
              @pressEnter="handleSearch"
              @change="handleChange"
            />
            <div class="search-btn">
              <a-button
                type="primary"
                :icon="h(Icon, { icon: 'icon-park-outline:search', size: 18 })"
                @click="handleSearch"
              />
            </div>
          </div>
        </div>
        <div class="map-result-control">
          <div class="map-result-control-container">
            <div
              class="result-container"
              v-if="resultList.length"
              ref="mapResultControlContainerRef"
            >
              <div class="list">
                <div
                  class="item"
                  v-for="item in resultList"
                  :key="item.id"
                  @click="handleSetCenter(item)"
                  :class="currentSelectItem?.id === item.id ? 'active' : ''"
                >
                  <div class="item-left">
                    <div class="item-title">
                      {{ item.name }}
                    </div>
                    <div class="item-address" v-if="item.address">{{ item.address }}</div>
                    <div class="item-address empty" v-else>暂无地址信息</div>
                  </div>
                  <div
                    class="item-right"
                    v-if="getImg(item.imgUrl)"
                    :style="{ backgroundImage: `url(${getEevReturnDomain(getImg(item.imgUrl))})` }"
                  >
                  </div>
                  <div class="item-right empty" v-else>
                    <img :src="emptyIcon" />
                  </div>
                </div>
              </div>
              <div class="pagetion" v-if="pagination.total > 10">
                <Pagination
                  v-model:current="pagination.current"
                  v-model:pageSize="pagination.size"
                  :showSizeChanger="false"
                  size="small"
                  :total="pagination.total"
                />
              </div>
            </div>
            <div class="result-empty" v-if="!resultList.length && isSearch">
              <Empty :image="emptyImg" description="抱歉，暂无搜索内容" />
            </div>
          </div>
        </div>
      </div>
      <div class="right-control" v-if="!!Map">
        <div
          :class="['top-control', { open: openSetMapType }]"
          @mouseenter="openSetMapType = true"
          @mouseleave="openSetMapType = false"
        >
          <div class="close" @click="openSetMapType = false"></div>
          <div
            v-for="item in mapTypes"
            :key="item.layer"
            :class="['type', item.layer, { active: currentMapType === item.layer }]"
            @click="setMapType(item.layer)"
          >
            <img class="icon" :src="item.icon" />{{ item.title }}</div
          >
        </div>
        <div class="bottom-control">
          <div class="control-item" @click="setFullScreen">
            <Icon
              :icon="
                isFullscreen
                  ? 'icon-park-outline:off-screen-one'
                  : 'icon-park-outline:full-screen-one'
              "
              :size="18"
            />
          </div>
          <div class="control-item" @click="setLocation">
            <Icon icon="icon-park-outline:local" :size="18" />
          </div>
          <div
            class="control-item"
            :class="mapConfig.zoom >= mapConfig.maxZoom ? 'disabled' : ''"
            @click="setZoomIn"
          >
            <Icon icon="icon-park-outline:plus" :size="18" />
          </div>
          <div
            class="control-item"
            :class="mapConfig.zoom <= mapConfig.minZoom ? 'disabled' : ''"
            @click="setZoomOut"
          >
            <Icon icon="icon-park-outline:minus" :size="18" />
          </div>
        </div>
      </div>
      <Loading :loading="loading" absolute />
      <FactoryDetail @register="registerDrawer" />
    </div>
  </div>
</template>

<script lang="ts" name="FactoryMap" setup>
  import { ref, h, reactive, nextTick, onUnmounted, onMounted } from 'vue';
  import { Input, Empty, Pagination } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { useDrawer } from '/@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import emptyImg from '/@/assets/images/table-empty.png';
  import { Loading } from '/@/components/Loading';
  import { useFullscreen } from '@vueuse/core';
  import { key } from './data';
  import { getEevReturnDomain } from '/@/utils/file/url';
  import {
    getAllOnFactory,
    getAllOnFactoryByPage,
    getFactoryAndIndicatorDataById,
  } from '/@aoa/api/factory';
  import markerIcon from './assets/images/marker.png';
  import emptyIcon from './assets/images/empty.png';
  import FactoryDetail from './FactoryDetail.vue';

  defineOptions({
    name: 'Map',
  });

  const T = ref();
  const Map = ref();
  const definedOverlay = ref();

  const mapRef = ref();
  const searchControlRef = ref();
  const mapResultControlContainerRef = ref();

  const keyword = ref('');
  const isSearch = ref(false);
  const resultList = ref<any>([]);
  const allFactoryList = ref<any>([]);
  const markerList = ref<any>([]);
  const labelList = ref<any>([]);
  const currentMarkerOpenInfo = ref();

  const pagination = reactive({
    current: 1,
    size: 10,
    total: 0,
  });

  const mapConfig = ref({
    zoom: 14,
    maxZoom: 18,
    minZoom: 1,
    x: 113.940705,
    y: 22.561867,
  });

  const { createMessage } = useMessage();
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawer();

  const detail = async (id) => {
    const currentFactory = allFactoryList.value.find((item) => item.id === id);
    if (currentFactory) {
      const res = await getFactoryAndIndicatorDataById({ mapFactoryId: id });
      const imgs = res?.imgUrl ? currentFactory.imgUrl.split(',') : [];
      openDrawer(true, { data: { ...res, imgs } });
      Map.value.closeInfoWindow();
      currentMarkerOpenInfo.value = null;
      keyword.value = '';
      isSearch.value = false;
      resultList.value = [];
    }
  };

  const handleSearch = () => {
    if (!keyword.value) return;
    getSearchFactoryList();
  };

  const handleChange = () => {
    if (!keyword.value) {
      isSearch.value = false;
      resultList.value = [];
    }
  };

  const currentSelectItem = ref();
  const handleSetCenter = (info) => {
    currentSelectItem.value = info;
    if (info.x && info.y) {
      Map.value.centerAndZoom(new T.value.LngLat(info.x, info.y), mapConfig.value.zoom);
      const infoWin = setInfoWin(info);
      const currentMarker = markerList.value.find((i) => i.id === info.id);
      if (currentMarker) {
        currentMarker.marker.openInfoWindow(infoWin);
        currentMarkerOpenInfo.value = info.id;
        document
          .querySelector(`.map-page .tdt-infowindow-content-container-${info.id}`)
          ?.addEventListener('click', () => detail(info.id));
      }
    }
    isSearch.value = false;
    resultList.value = [];
    Map.value.enableDrag();
  };

  const getImg = (url) => {
    return url ? url.split(',')[0] : null;
  };

  const getSearchFactoryList = async () => {
    const params = {
      current: pagination.current,
      size: pagination.size,
      name: keyword.value,
    };
    const res = await getAllOnFactoryByPage(params);
    resultList.value = res.records;
    pagination.total = res.total;
    isSearch.value = true;
    if (mapResultControlContainerRef.value) {
      mapResultControlContainerRef.value.style.height = '';
      await nextTick(() => {
        mapResultControlContainerRef.value.style.height =
          searchControlRef.value?.offsetHeight - 48 + 'px';
      });
    }
  };

  // 创建信息窗口
  const setInfoWin = (item) => {
    const img = getImg(item.imgUrl);
    const imgUrl = img ? getEevReturnDomain(img) : emptyIcon;
    const sContent = `
      <div class="tdt-infowindow-content-container tdt-infowindow-content-container-${item.id}">
        <div class="tdt-infowindow-content-top">
          <div class="title">${item.name}</div>
          <div class="detail">详情 </div>
        </div>
        <div class="tdt-infowindow-content-content">
          <div class="desc ${item.introduction ? '' : 'empty'}">${
      item.introduction ? item.introduction : '该水厂暂无简介'
    }</div>
          <div class="img ${img ? '' : 'empty'}" style="background-image: url(${imgUrl});"></div>
        </div>
      </div>
      `;
    const infoWin = new T.value.InfoWindow(sContent, {
      closeButton: false,
      closeOnClick: true,
    });
    return infoWin;
  };

  // 初始化文字标注
  const setLabel = (data) => {
    const currentMarker = markerList.value.find((i) => i.id === data.id);
    if (currentMarker) {
      const label = new T.value.Label({
        text: data.name,
        position: currentMarker.position,
        offset: new T.value.Point(-32, -48),
      });
      labelList.value.push({
        id: data.id,
        label,
      });
      Map.value.addOverLay(label);
    }
  };

  // 打开信息窗口
  const handleMarkerOpenInfo = (info) => {
    keyword.value = '';
    isSearch.value = false;
    resultList.value = [];
    Map.value.centerAndZoom(info.position, mapConfig.value.zoom);
    const infoWin = setInfoWin(info.data);

    const currentMarker = markerList.value.find((i) => i.id === info.id);
    if (currentMarker) {
      currentMarker.marker.openInfoWindow(infoWin);
      currentMarkerOpenInfo.value = info.id;
      handleMarkerCloseLabel(info);
      closeDrawer();
      document
        .querySelector(`.map-page .tdt-infowindow-content-container-${info.id}`)
        ?.addEventListener('click', () => detail(info.id));
    }
  };

  // 打开label
  const handleMarkerOpenLabel = (info) => {
    // 判断是否打开信息窗口
    currentMarkerOpenInfo.value;
    if (currentMarkerOpenInfo.value !== info.id) {
      setLabel(info.data);
    }
  };
  const handleMarkerCloseLabel = (info) => {
    const label = labelList.value.find((i) => i.id === info.id);
    if (label) {
      Map.value.removeOverLay(label.label);
      labelList.value = labelList.value.filter((i) => i.id !== info.id);
    }
  };

  const getAllFactoryList = async () => {
    allFactoryList.value = await getAllOnFactory();
    // 向地图添加所有水厂标注
    // 创建自定义图片
    const icon = new T.value.Icon({
      iconUrl: markerIcon,
      iconSize: new T.value.Point(32, 32),
      iconAnchor: new T.value.Point(16, 16),
    });

    let points: any = [];

    allFactoryList.value.map((i) => {
      if (i.x && i.y) {
        const position = new T.value.LngLat(i.x, i.y);
        points.push(position);
        const marker = new T.value.Marker(position, { icon });
        const info = {
          id: i.id,
          position,
          data: i,
          marker,
        };
        markerList.value.push(info);
        //注册标注的点击事件
        marker.addEventListener('click', () => handleMarkerOpenInfo(info));
        marker.addEventListener('mouseover', () => handleMarkerOpenLabel(info));
        marker.addEventListener('mouseout', () => handleMarkerCloseLabel(info));
      }
    });

    if (points.length) {
      // 设置最佳视野
      Map.value.setViewport(points);
      const viewInfo = Map.value.getViewport(points);
      mapConfig.value.zoom = viewInfo.zoom;
    } else {
      Map.value.centerAndZoom(
        new T.value.LngLat(mapConfig.value.x, mapConfig.value.y),
        mapConfig.value.zoom,
      );
    }

    // 添加标注
    markerList.value.forEach((item) => {
      var pdefinedOverlay = new definedOverlay.value(item.position, item, {});
      Map.value.addOverLay(pdefinedOverlay);
      Map.value.addOverLay(item.marker);
    });
  };

  // 设置不可拖动
  const setMapIsDrag = () => {
    if (Map.value) {
      searchControlRef.value?.addEventListener('mouseenter', () => {
        Map.value?.disableDrag();
      });
      searchControlRef.value?.addEventListener('mouseleave', () => {
        Map.value?.enableDrag();
      });
    }
  };

  // 设置全屏
  const { isFullscreen, enter, exit } = useFullscreen(mapRef);

  const setFullScreen = () => {
    isFullscreen.value ? exit() : enter();
  };

  // 定位
  const loading = ref(false);
  const setLocation = () => {
    if (!navigator.geolocation) {
      createMessage.warning('浏览器不支持定位服务!');
      return;
    }
    loading.value = true;
    const lo = new T.value.Geolocation();
    const fn = function (e) {
      const status = lo.getStatus();
      switch (status) {
        case 0:
          Map.value.setViewport([e.lnglat]);
          const viewInfo = Map.value.getViewport([e.lnglat]);
          mapConfig.value.zoom = viewInfo.zoom;
          Map.value.centerAndZoom(e.lnglat, mapConfig.value.zoom);
          break;
        case 1:
          Map.value.centerAndZoom(e.lnglat, e.level);
          mapConfig.value.zoom = e.level;
          break;
        case 2:
          createMessage.warning('当前位置不可用!');
          break;
        case 3:
          createMessage.warning('用户拒绝提供地理位置!');
          break;
        case 4:
          createMessage.warning('获取位置信息超时!');
          break;
        case 5:
          createMessage.warning('未知错误!');
          break;
      }
      loading.value = false;
    };

    lo.getCurrentPosition(fn, { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 });
  };
  // 放大
  const setZoomOut = () => {
    Map.value.zoomOut();
  };

  // 缩小
  const setZoomIn = () => {
    Map.value.zoomIn();
  };

  // 地图放大缩小
  const mapZoom = () => {
    const zoom = Map.value.getZoom();
    mapConfig.value.zoom = zoom;
  };

  // 地图点击
  const handleClickMap = () => {
    Map.value.closeInfoWindow();
    currentMarkerOpenInfo.value = null;
    closeDrawer();
    keyword.value = '';
    isSearch.value = false;
    resultList.value = [];
  };

  // 加载天地图js
  const loadTiandituScript = async () => {
    if ((window.T && window.T.Map) || document.getElementById('tianditu-script')) {
      return Promise.resolve();
    }
    return new Promise(async (resolve, reject) => {
      const script = document.createElement('script');
      script.id = 'tianditu-script';
      script.src = `http://api.tianditu.gov.cn/api?v=4.0&tk=${key}`;
      script.async = true;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  };

  // 初始化地图
  const initMap = () => {
    if (window.T) {
      T.value = window.T;
      Map.value = new T.value.Map('tiandituMap', {
        maxZoom: mapConfig.value.maxZoom,
        minZoom: mapConfig.value.minZoom,
      });
      definedOverlay.value = T.value.Overlay.extend({
        initialize: function (lnglat, info, options) {
          this.lnglat = lnglat;
          this.info = info;
          this.setOptions(options);
        },

        onAdd: function (map) {
          this.map = map;
          var div = (this._div = document.createElement('div'));
          div.className = 'custome-marker-point';
          div.style.position = 'absolute';
          div.style.height = '30px';
          div.style.width = '30px';
          div.style.userSelect = 'none';
          Map.value.getPanes()._w.appendChild(this._div);
          this.update(this.lnglat);
        },

        onRemove: function () {},

        /**
         * 更新位置
         */
        update: function () {
          var pos = this.map.lngLatToLayerPoint(this.lnglat);
          this._div.style.top = pos.y - 15 + 'px';
          this._div.style.left = pos.x - 15 + 'px';
        },
      });

      Map.value?.addEventListener('zoomend', mapZoom);
      Map.value?.addEventListener('click', handleClickMap);
      setMapIsDrag();
      getAllFactoryList();
    }
  };

  // 切换地图类型
  const openSetMapType = ref(false);
  const currentMapType = ref('TMAP_NORMAL_MAP');
  const mapTypes = [
    {
      title: '地图',
      icon: 'http://api.tianditu.gov.cn/v4.0/image/map/maptype/vector.png',
      layer: 'TMAP_NORMAL_MAP',
    },
    {
      title: '卫星',
      icon: ' http://api.tianditu.gov.cn/v4.0/image/map/maptype/satellite.png',
      layer: 'TMAP_SATELLITE_MAP',
    },
    {
      title: '卫星(路网)',
      icon: 'http://api.tianditu.gov.cn/v4.0/image/map/maptype/satellitepoi.png',
      layer: 'TMAP_HYBRID_MAP',
    },
  ];
  const setMapType = (type) => {
    Map.value?.setMapType(window[type]);
    currentMapType.value = type;
    openSetMapType.value = false;
  };

  onMounted(async () => {
    try {
      await loadTiandituScript();
      await initMap();
    } catch (_) {
      const script = document.getElementById('tianditu-script');
      if (script && script.parentNode) {
        script.parentNode.removeChild(script);
      }
      console.error('天地图脚本加载失败，请刷新页面重新加载!');
    }
  });

  onUnmounted(() => {
    Map.value?.removeEventListener('zoomend', mapZoom);
    Map.value?.removeEventListener('click', handleClickMap);
  });
</script>

<style lang="less" scoped>
  :deep(.vben-basic-drawer) {
    &.aoa3 {
      box-shadow: -4px 0px 12px 0px #96beb9;
    }
  }

  .map-page {
    padding: 0 16px 16px;

    :deep(.full-loading) {
      z-index: 1000;
    }

    .map {
      position: relative;
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.48);
      border-radius: 4px 4px 4px 4px;
      overflow: hidden;

      .search-control {
        position: absolute;
        left: 24px;
        top: 24px;
        width: 400px;
        z-index: 999;
        max-height: calc(100% - 48px);
      }

      .map-search-control {
        .input-control {
          position: relative;

          :deep(.ant-input-affix-wrapper) {
            height: 40px;
            width: 400px;

            .ant-input-suffix {
              margin-inline-end: 39px;
            }
          }

          .search-btn {
            position: absolute;
            right: 4px;
            top: 4px;
            z-index: 10;

            :deep(.ant-btn) {
              padding: 0 9px;

              .anticon + span {
                display: none;
              }
            }
          }
        }
      }

      .map-result-control {
        height: calc(100% - 48px);
        width: 400px;
        cursor: default;

        .map-result-control-container {
          height: 100%;
          margin-top: 8px;
          // background-color: #fff;
          // box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.16);
          border-radius: 4px 4px 4px 4px;

          box-shadow: 0px 1px 8px 0px #577572;
          background: @aoa3-join-from-bg;
        }

        .result-container {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;

          .list {
            margin: 4px 0 0;
            padding: 0 0 4px 4px;
            overflow-y: auto;
            flex: 1;
            width: 100%;

            .item {
              font-family: 'PingFang SC, PingFang SC';
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              width: calc(100% - 4px);
              gap: 0 14px;
              padding: 9px 8px 9px 12px;
              height: 108px;
              border-radius: 4px;
              overflow: hidden;
              cursor: pointer;

              &:hover {
                // background: #f0f0f0;
                background: @theme-aoa3-color-16p;
              }

              &.active {
                // background: #f0f0f0;
                background: @theme-aoa3-color-16p;
              }

              .item-left {
                flex: 1;
                padding-left: 24px;
                background: url('./assets/images/marker.png') left 3px no-repeat;
                background-size: 16px 16px;

                .item-title {
                  font-weight: 500;
                  font-size: 14px;
                  color: var(--theme-color);
                  line-height: 21px;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2; /* 设置显示的行数 */
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .item-address {
                  margin-top: 9px;
                  font-weight: 400;
                  font-size: 12px;
                  color: #666666;
                  line-height: 18px;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2; /* 设置显示的行数 */
                  overflow: hidden;
                  text-overflow: ellipsis;

                  &.empty {
                    color: #999;
                  }
                }
              }

              .item-right {
                margin-top: 3px;
                width: 120px;
                height: 84px;
                border-radius: 4px 4px 4px 4px;
                overflow: hidden;
                align-items: center;
                background-size: cover;
                background-repeat: no-repeat;
                background-position: center center;

                &.empty {
                  // background: #f0f0f0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  background: #fff;

                  img {
                    width: 24px;
                    height: 24px;
                  }
                }

                // img {
                //   object-fit: cover;
                // }
              }
            }
          }

          .pagetion {
            margin: 12px auto;

            :deep(.ant-pagination) {
              margin-top: 0 !important;

              .ant-pagination-prev,
              .ant-pagination-item,
              .ant-pagination-next,
              .ant-pagination-jump-prev,
              .ant-pagination-jump-next {
                border: none !important;
                min-width: 24px !important;
                height: 24px !important;
              }

              .ant-pagination-prev.ant-pagination-item-active {
                background-color: var(--theme-color-8p);

                a {
                  color: var(--theme-color) !important;
                }
              }

              .ant-pagination-item.ant-pagination-item-active {
                background-color: var(--theme-color-8p);

                a {
                  color: var(--theme-color) !important;
                }
              }

              .ant-pagination-next.ant-pagination-item-active {
                background-color: var(--theme-color-8p);

                a {
                  color: var(--theme-color) !important;
                }
              }
            }
          }
        }

        .result-empty {
          height: 368px;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;

          :deep(.ant-empty) {
            .ant-empty-image {
              margin-bottom: 12px;
              height: 88px;
            }

            .ant-empty-description {
              font-size: 14px;
              color: #999999;
              line-height: 14px;
            }
          }
        }
      }

      .right-control {
        .top-control {
          position: absolute;
          right: 24px;
          top: 24px;
          z-index: 999;
          transition: width 0.2s;
          border-radius: 22px;
          width: 54px;
          height: 44px;
          display: flex;
          align-items: center;

          &:before {
            position: absolute;
            content: '';
            width: 54px;
            height: 44px;
            top: 0;
            right: 0;
            background: url('./assets/images/map_type_bg.png') center center no-repeat;
            background-size: 54px 44px;
            filter: drop-shadow(0px 0px 4px rgba(40, 43, 47, 0.24));
            cursor: pointer;
          }

          &:after {
            position: absolute;
            content: '';
            width: 54px;
            height: 44px;
            top: 0;
            right: 0;
            background-image: url('./assets/images/map_type.png');
            background-position: calc(100% - 4px) center;
            background-repeat: no-repeat;
            background-size: 36px 36px;
            z-index: 1;
          }

          .close {
            position: absolute;
            background: url('./assets/images/map_type_close.png') center center no-repeat;
            background-size: 30px 30px;
            left: 7px;
            top: 7px;
            width: 30px;
            height: 30px;
            overflow: hidden;
            cursor: pointer;
            z-index: 1;
            pointer-events: all;
            visibility: hidden;
            opacity: 0;
          }

          .type {
            position: absolute;
            padding: 0 12px;
            top: 4px;
            width: 98px;
            height: 36px;
            border-radius: 8px 8px 8px 8px;
            font-weight: 400;
            font-size: 16px;
            color: #1f2329;
            line-height: 16px;
            cursor: pointer;
            visibility: hidden;
            opacity: 0;
            display: flex;
            align-items: center;

            &:hover {
              background-color: var(--theme-color-16p);
            }

            &.active {
              background: var(--theme-color-16p) url('./assets/images/map_type_checked.png') right
                bottom no-repeat;
              background-size: 14px 14px;
            }

            .icon {
              margin-right: 8px;
              width: 24px;
              height: 24px;
              border-radius: 50%;
            }

            &.TMAP_HYBRID_MAP {
              right: 274px;
              width: 134px;
            }

            &.TMAP_SATELLITE_MAP {
              right: 164px;
            }

            &.TMAP_NORMAL_MAP {
              right: 54px;
            }
          }

          &.open {
            width: 457px;
            background-color: #fff;
            box-shadow: 0px 0px 4px 0px rgba(40, 43, 47, 0.24);

            &:before {
              width: 0;
              height: 0;
            }

            .close,
            .type {
              opacity: 1;
              visibility: visible;
            }
          }
        }

        .bottom-control {
          position: absolute;
          right: 24px;
          bottom: 24px;
          z-index: 999;

          .control-item {
            margin-top: 16px;
            width: 40px;
            height: 40px;
            background: #ffffff;
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.24);
            border-radius: 4px 4px 4px 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &:hover {
              color: var(--theme-color);
            }

            &.disabled {
              cursor: no-drop;
              color: #bbb;
            }
          }
        }
      }
    }

    :deep(.tdt-map-pane) {
      @keyframes pulsate {
        0% {
          transform: scale(0.1, 0.1);
          opacity: 0;
          filter: opacity = 0;
        }

        50% {
          opacity: 1;
          -ms-filter: none;
          filter: none;
        }

        100% {
          transform: scale(2, 2);
          opacity: 0;
          filter: opacity = 0;
        }
      }

      .tdt-_w-pane {
        .custome-marker-point {
          border-radius: 100%;
          cursor: pointer;

          &:after {
            content: '';
            height: 100%;
            width: 100%;
            border-radius: 100%;
            position: absolute;
            margin: 0;
            box-shadow: -1px -1px 4px 1px rgba(46, 182, 255, 1),
              1px 1px 4px 1px rgba(46, 123, 255, 1);
            animation: pulsate 1s ease-out;
            animation-iteration-count: infinite;
            animation-delay: 1.1s;
          }
        }
      }

      .tdt-overlay-pane {
        z-index: 700;
      }

      .tdt-label {
        padding: 0 16px;
        // border: none;
        border-radius: 4px;
        cursor: pointer;
        // border: 1px solid #cecece;
        font-family: 'PingFang SC, PingFang SC';
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 32px;
        background: @aoa3-join-from-bg;
        box-shadow: 0px 1px 8px 0px #577572;
        border: 1px solid #94dcd5;

        &:after {
          position: absolute;
          content: '';
          width: 12px;
          height: 12px;
          left: 14px;
          top: 32px;
          // border-left: 6px solid #fff;
          transform: rotateZ(90deg);
          border-left: 6px solid @aoa3-join-from-bg;
        }
      }

      .tdt-infoWindow-pane {
        cursor: default;

        .tdt-infowindow {
          bottom: -10px !important;
        }

        .tdt-infowindow-close-button {
          display: none;
        }

        .tdt-infowindow-content-wrapper {
          border-radius: 4px 4px 4px 4px;
          // border: 1px solid #cecece;
          padding: 0;
          background: @aoa3-join-from-bg;
          box-shadow: 0px 1px 8px 0px #577572;
          border: 1px solid #94dcd5;

          .tdt-infowindow-content {
            margin: 10px 16px;
          }
        }

        .tdt-infowindow-tip-container {
          transform: translateY(-2px);

          .tdt-infowindow-tip {
            width: 12px;
            height: 12px;
            margin: -7px auto 0;
            background: @aoa3-join-from-bg;
            box-shadow: 0px 1px 8px 0px #577572;
            border: 1px solid #94dcd5;
          }
        }

        .tdt-infowindow-content {
          .tdt-infowindow-content-container {
            font-family: 'PingFang SC, PingFang SC';

            .tdt-infowindow-content-top {
              display: flex;
              justify-content: space-between;
              align-items: center;
              gap: 0 16px;

              .title {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-weight: 500;
                font-size: 14px;
                color: #333333;
                line-height: 14px;
              }

              .detail {
                position: relative;
                color: rgba(10, 96, 79, 1);
                cursor: pointer;
                padding-right: 9px;
                font-size: 14px;
                line-height: 14px;

                &:after {
                  content: '';
                  position: absolute;
                  width: 6px;
                  height: 6px;
                  border: 1px solid rgba(10, 96, 79, 1);
                  border-left: none;
                  border-bottom: none;
                  right: 0;
                  top: 0;
                  transform: translate(0, 5px) rotate(45deg);
                }
              }
            }

            .tdt-infowindow-content-content {
              margin-top: 12px;
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              gap: 0 16px;

              .desc {
                width: 165px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 4; /* 设置显示的行数 */
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: 14px;
                color: #666666;
                line-height: 21px;
              }

              .img {
                width: 120px;
                height: 84px;
                border-radius: 4px;
                overflow: hidden;
                background-size: cover;
                background-repeat: no-repeat;
                background-position: center center;

                &.empty {
                  // background-color: #f0f0f0;
                  background-size: 24px 24px;
                  background-color: #fff;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
