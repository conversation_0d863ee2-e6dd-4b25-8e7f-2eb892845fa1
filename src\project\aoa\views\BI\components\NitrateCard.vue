<template>
  <BiCardBox title="硝氮">
    <template #title-slot>
      <AiCard
        :questionTemplate="question"
        questionTitle="硝氮"
        type="thickness"
        :deepThinking="deepThinking"
      />
    </template>
    <template #header-right>
      <SelectSlot :options="data?.options" :value="data.value" @update:value="handleChange" />
    </template>
    <template #content>
      <div class="nitrate-content" v-if="data?.data?.length">
        <div class="icon">
          <img :src="icon7" />
          <div class="name">硝氮</div>
        </div>
        <div class="list">
          <div class="item" v-for="(item, index) in data.data" :key="index">
            <ProcessBar :data="item" :type="index % 2" :max="maxValue" :groupCode="groupCode" />
          </div>
        </div>
      </div>
      <div class="empty-container w-full h-full" v-else>
        <Empty />
      </div>
    </template>
  </BiCardBox>
</template>

<script lang="ts" setup>
  import { PropType, computed } from 'vue';
  import { BiCardBox, SelectSlot, ProcessBar, Empty } from './BiCard';
  import { AiCard } from '/@aoa/components/AiCard';
  import type { DataListSelectData } from '../type';
  import icon7 from '/@aoa/views/BI/assets/images/icon_7.png';

  const emits = defineEmits(['update:value']);

  const props = defineProps({
    data: {
      type: Object as PropType<DataListSelectData>,
      default: () => ({
        data: [],
        value: '',
        options: [],
      }),
    },
    groupCode: String,
    deepThinking: {
      type: Boolean,
      default: false,
    },
    aiQuestionTemplate: {
      type: String,
      default: '',
    },
  });

  const maxValue = computed(() => {
    if (!props.data.data || !props.data.data.length) {
      return 0;
    }
    const valueList = props.data.data.map((item) => item.value);
    if (valueList.every((item) => item === null || item === '')) {
      return 0;
    }
    const max = Math.max(...props.data.data.map((item) => Number(item.value)));
    return max;
  });

  const handleChange = (value) => {
    emits('update:value', value);
  };

  const getQuestion = (data) => {
    if (!props.aiQuestionTemplate || !props.aiQuestionTemplate.includes('${data}')) {
      return props.aiQuestionTemplate;
    }
    const str = data
      .map((item) => {
        return `${item.name}${
          item.value !== null && item.value !== undefined
            ? Number(item.value).toFixed(item.digit ?? 2)
            : '-'
        }${item.unit}`;
      })
      .join('、');
    return props.aiQuestionTemplate.replace('${data}', str);
  };

  const question = computed(() => {
    const data = props.data.data;
    return getQuestion(data);
  });
</script>
<style lang="less" scoped>
  .nitrate-content {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 166px;
    padding: 0 8px;
    gap: 0 16px;

    .icon {
      position: relative;
      width: 110px;
      height: 110px;

      img {
        width: 100%;
      }

      .name {
        position: absolute;
        width: 100%;
        top: 22px;
        text-align: center;
        font-weight: 600;
        font-size: 14px;
        line-height: 1;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.88), -2px -1px 2px rgba(255, 255, 255, 0.4);
      }
    }

    .list {
      height: calc(100% - 32px);
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 17px 0;
      flex: 1;

      // @media screen and (max-width: 1601px) {
      //   width: 152px;
      // }
    }
  }

  @media screen and (min-width: 1800px) {
    .nitrate-content {
      .px2vw(25);
      padding: 0 @vw;
      gap: 0 @vw;
    }
  }
  @media screen and (min-width: 2000px) {
    .nitrate-content {
      .icon {
        position: relative;
        .px2vw(130);
        .width-prop(130);
        height: @vw;

        .name {
          .height-prop(27,top);
          .font-size(16);
        }
      }

      .list {
        .px2vw(25);
        gap: @vw 0;
      }
    }
  }
</style>
