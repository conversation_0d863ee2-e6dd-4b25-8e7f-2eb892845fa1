import type { Router, RouteRecordRaw } from 'vue-router';

import { usePermissionStoreWithOut } from '/@/store/modules/permission';

import { PageEnum } from '/@/enums/pageEnum';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { useFlowStoreWithout } from '/@/store/modules/flow';

import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';

// import { RootRoute } from '/@/router/routes';
import { useDomain } from '/@/locales/useDomain';

import { RootRoute } from '/@@/router/routes';

import { whiteListArr } from '/@@/router/guard/whitelist';
import { getParamKeyApi } from '/@/api/admin/param';
import { ParamsKeyEnum } from '/@/enums/appEnum';
import { parseJson } from '/@/utils';
import { SINGLE_LOGIN } from '/@/enums/cacheEnum';
import { createLocalStorage } from '/@/utils/cache';
import { checkUserToken } from '/@/api/sys/user';
import { useMailStoreWithOut } from '/@hlxb-mail/store/mail';
import { getCurrentUserFactoryList } from '/@zhcz/api/patrol';

const LOGIN_PATH = PageEnum.BASE_LOGIN;

const ROOT_PATH = RootRoute.path;

const whitePathList: PageEnum[] = [LOGIN_PATH, ...(whiteListArr as PageEnum[])];

export function createPermissionGuard(router: Router) {
  const userStore = useUserStoreWithOut();
  const mailStore = useMailStoreWithOut();

  const permissionStore = usePermissionStoreWithOut();
  const flowStore = useFlowStoreWithout();
  const ls = createLocalStorage();

  const { getDomain, changeTenantId } = useDomain();

  router.beforeEach(async (to, from, next) => {
    const firstMenuParams = permissionStore.getFirstMenuParams;
    const factoryId = userStore.getCurrentFactoryId;
    const moduleId = permissionStore.getFirstMenuParams?.moduleId;
    if (moduleId) {
      const res = await getCurrentUserFactoryList({ moduleId });
      if (res?.bindSourceUniqueId) {
        // 匹配是否启用
        let setId = '-1';
        const findIndex = res.factoryInfoList.findIndex((item) => item.moduleId === moduleId);
        // const factoryInfoList = res.factoryInfoList.filter(
        //   (item) => item.factoryId === res.bindSourceUniqueId,
        // );
        setId = findIndex !== -1 ? res.bindSourceUniqueId : factoryId || setId;
        userStore.setFactoryId(setId);
        userStore.setUserInfo({
          ...userStore.getUserInfo,
          factoryInfoList: res.factoryInfoList,
          bindSourceUniqueId: setId,
        });
      } else {
        userStore.setFactoryId('-1');
        userStore.setGLobalSource({ factoryId: '-1' });
      }
    }

    //页面嵌入到外部系统
    const isSingleLogin = ls.get(SINGLE_LOGIN);

    if (to.query.tenantId && to.query.token) {
      if (permissionStore.getIsDynamicAddedRoute || to.query.isIframe) {
        next();
        return;
      }
      userStore.setToken(to.query.token as string);
      changeTenantId(to.query.tenantId as string);
      // const specialUser = to.query.noSpecialUser !== '0';

      await gatherRoutes(true, next);
    }

    if (to.query.isSingleLogin || isSingleLogin) {
      to.query.tenantId && changeTenantId(to.query.tenantId as string);
      const flag = await checkUserToken(userStore.getToken);
      if (flag) {
        if (permissionStore.getIsDynamicAddedRoute) return next();
        await gatherRoutes(false, next);
        return next();
      }
      const ret = await getParamKeyApi(ParamsKeyEnum.TOKEN_REQ);

      if (ret) {
        userStore.setToken(undefined);
        const account = parseJson(ret);

        // 是否已加载过页面-》登录则直接过 ； 没有=》则重新加载

        const res = to?.path.split('/');

        to.meta.moduleId = res[res.length - 1];

        await userStore.login({
          username: account.userName,
          password: account.passWord,
          goHome: false,
          singleLogin: true,
          toPageInfo: to,
        });
        if (to.query.isSingleLogin) {
          return next({ path: to.fullPath, replace: true, query: to.query });
        }
        return next();
      }
    }

    if (
      from.path === ROOT_PATH &&
      to.path === firstMenuParams?.redirect &&
      userStore.getUserInfo.homePath &&
      userStore.getUserInfo.homePath !== firstMenuParams?.redirect
    ) {
      next(userStore.getUserInfo.homePath);
      return;
    }

    const token = userStore.getToken;

    // Whitelist can be directly entered
    if (whitePathList.includes(to.path as PageEnum)) {
      if (to.path === LOGIN_PATH && token) {
        const isSessionTimeout = userStore.getSessionTimeout;
        try {
          await userStore.afterLoginAction();
          if (!isSessionTimeout) {
            next((to.query?.redirect as string) || '/');
            return;
          }
        } catch {}
      }
      next();
      return;
    }
    // token or user does not exist
    if (!token) {
      // You can access without permission. You need to set the routing meta.ignoreAuth to true
      if (to.meta.ignoreAuth) {
        next();
        return;
      }

      // redirect login page
      const redirectData: { path: string; replace: boolean; query?: Recordable<string> } = {
        path: LOGIN_PATH,
        replace: true,
      };
      if (to.path) {
        redirectData.query = {
          ...redirectData.query,
          domain: getDomain.value,
          redirect: to.path,
        };
      }
      next(redirectData);
      return;
    }

    // Jump to the 404 page after processing the login
    if (
      from.path === LOGIN_PATH &&
      to.name === PAGE_NOT_FOUND_ROUTE.name &&
      to.fullPath !== (userStore.getUserInfo.homePath || firstMenuParams?.redirect)
    ) {
      const path =
        userStore.getUserInfo.homePath ||
        firstMenuParams?.redirect ||
        (RootRoute.redirect! as string);

      if (path === '/home') {
        next();
        return;
      }
      next(path);
      return;
    }

    // get userinfo while last fetch time is empty
    if (userStore.getLastUpdateTime === 0) {
      try {
        await userStore.getUserInfoAction();
      } catch (err) {
        next();
        return;
      }
    }

    if (permissionStore.getIsDynamicAddedRoute) {
      mailStore.setCount();
      const path = firstMenuParams?.redirect || (RootRoute.redirect as string);

      if (from.fullPath === ROOT_PATH && to.fullPath === '/home') {
        if (path === '/home') {
          next();
          return;
        }
        next(path);
      } else {
        next();
      }

      const moduleId = to.meta.moduleId || to.query.moduleId;

      if (moduleId) {
        await permissionStore.changePermissionCode(moduleId);
      }
      return;
    }

    async function gatherRoutes(specialUser, next) {
      const routes = await permissionStore.buildRoutesAction(specialUser, true, isSingleLogin);

      routes.forEach((route) => {
        router.addRoute(route as unknown as RouteRecordRaw);
      });

      router.addRoute(PAGE_NOT_FOUND_ROUTE as unknown as RouteRecordRaw);

      permissionStore.setDynamicAddedRoute(true);
      permissionStore.patchHomeAffix();

      if (to.name === PAGE_NOT_FOUND_ROUTE.name) {
        // 动态添加路由后，此处应当重定向到fullPath，否则会加载404页面内容

        next({ path: to.fullPath, replace: true, query: to.query });
      } else {
        const redirectPath = (from.query.redirect || to.path) as string;
        const redirect = decodeURIComponent(redirectPath);
        const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect };
        next(nextData);
      }

      if (flowStore.getFlowReleaseParams && !flowStore.isUnlock) {
        await flowStore.releaseFlowProcess(flowStore.getFlowReleaseParams);
      }
      const arr =
        flowStore.getFlowAttachFiles?.filter((i) => i.new)?.map((i) => i.realfileName) ?? [];
      if (arr.length) {
        await flowStore.delAttachFilesRoute(arr, userStore.getUserInfo.userId as string);
      }
    }

    await gatherRoutes(false, next);
  });
}
