<template>
  <div class="water-in">
    <div class="icon" @click="showPanel = !showPanel"><img :src="icon11" /></div>
    <div class="water-card" v-if="showPanel">
      <div class="water-card-header">
        <div class="title">
          <div class="title-text">进出水水质</div>
          <div class="title-slot">
            <AiCard
              :questionTemplate="question"
              questionTitle="进出水水质"
              :deepThinking="deepThinking"
            />
          </div>
        </div>
        <div class="close" @click="showPanel = false">
          <Icon icon="icon-park-outline:close" :size="getScaleValByClientWidth(16)" />
        </div>
      </div>
      <div class="water-card-content">
        <div class="table-header">
          <div class="header-item" v-for="(item, index) in indexNameList" :key="index">
            {{ item }}
          </div>
        </div>
        <div class="table-body" v-if="dataList.length">
          <div class="item" v-for="item in dataList" :key="item.indexCode">
            <div class="name">
              <EllipsisTooltip :text="item.name" placement="topLeft" />
            </div>
            <div class="value" @click="openModal(item.indexCode, groupCode)">
              <EllipsisTooltip :text="item.value" placement="topLeft" />
            </div>
            <div class="value1" @click="openModal(item.indexCode, groupCode)">
              <EllipsisTooltip :text="item.value1" placement="topLeft" />
            </div>
            <div class="unit">
              <EllipsisTooltip :text="item.unit || '-'" placement="topLeft" />
            </div>
          </div>
        </div>
        <div class="empty-container" v-else>
          <Empty />
        </div>
      </div>
    </div>
  </div>
  <IndicatorModal
    v-model:open="indicatorOpen"
    :width="isIPCRef ? '100%' : '1272px'"
    :bodyStyle="modalBodyStyle"
    :destroyOnClose="true"
    :groupInfo="groupInfo"
    :themeColor="themeColor"
    :echartsConfig="echartsConfig"
    :multiple="multiple"
    :factoryId="factoryId"
    title="指标详情"
    :requestHeader="requestHeader"
    :footer="modalFooter"
    :base-url="baseUrl"
    wrapClassName="aoa-ipc-modal-curve"
  />
</template>

<script lang="ts" setup>
  import { PropType, computed, ref } from 'vue';
  import dayjs from 'dayjs';
  import { Empty } from './CockpitCard';
  import icon11 from '/@aoa/views/cockpit/assets/images/icon_11.png';
  import { Icon } from '/@/components/Icon';
  import type { ChartData } from '../type';
  import { AiCard } from '/@aoa/components/AiCard';
  import { getScaleValByClientWidth } from '../data';
  import { EllipsisTooltip } from '/@/components/EllipsisTooltip';

  import { IndicatorModal } from 'hlxb-business-ui';
  import { getProcessEditorTheme } from '/@process-editor/assets/theme';
  import { isIPCBreakPoint } from '/@/hooks/event/useBreakpoint';
  import { useWindowSize } from '@vueuse/core';
  import { getToken } from '/@/utils/auth';
  import { useDomain } from '/@/locales/useDomain';
  import { getFactoryId } from '/@aoa/utils/factory';
  import { getAppEnvConfig } from '/@/utils/env';

  const props = defineProps({
    data: {
      type: Array as PropType<ChartData[]>,
      default: () => [],
    },
    groupCode: String,
    deepThinking: {
      type: Boolean,
      default: false,
    },
    aiQuestionTemplate: {
      type: String,
      default: '1111',
    },
  });

  const indexNameList = ['指标名称', '进水', '出水', '单位'];
  const dataList = [
    { name: 'COD', indexCode: 'COD', value: 348.22, value1: 45.17, unit: 'mg/L' },
    { name: '氨氮', indexCode: '氨氮', value: 33.7, value1: 3.3, unit: 'mg/L' },
    { name: 'TP', indexCode: 'TP', value: 5.74, value1: 0.56, unit: 'mg/L' },
    { name: 'TN', indexCode: 'TN', value: 44.86, value1: 11.78, unit: 'mg/L' },
    { name: 'pH', indexCode: 'pH', value: 7.58, value1: 7.3, unit: '' },
    { name: 'SS', indexCode: 'SS', value: 155.73, value1: 23.85, unit: 'mg/L' },
    { name: '温度', indexCode: '温度', value: 33.45, value1: 34.84, unit: 'mg/L' },
  ];

  const showPanel = ref(false);

  const getQuestion = (data) => {
    if (!props.aiQuestionTemplate || !props.aiQuestionTemplate.includes('${data}')) {
      return props.aiQuestionTemplate;
    }
    if (!data.length) {
      return '';
    }
    const str = data[0]?.data
      .map((item) => {
        return `${Number(dayjs(item.collectDateTime).format('MM'))}月${
          item.value !== null && item.value !== undefined
            ? Number(item.value).toFixed(data[0].digit ?? 2)
            : '-'
        }${data[0].unit}`;
      })
      .join('、');
    return props.aiQuestionTemplate.replace('${data}', str);
  };

  const question = computed(() => {
    const data = props.data;
    return getQuestion(data);
  });

  const { themeColor, echartsConfig } = getProcessEditorTheme();
  const factoryId = getFactoryId();
  const { isIPCRef } = isIPCBreakPoint();
  const { height } = useWindowSize();
  const modalBodyStyle = computed(() => {
    return isIPCRef.value
      ? {
          height: `${height.value - 64}px`,
        }
      : { height: '716px' };
  });
  const modalFooter = computed(() => {
    return isIPCRef.value ? null : ' ';
  });

  const baseUrl = computed(() => getAppEnvConfig().VITE_GLOB_API_URL);

  const { getTenantId } = useDomain();
  const requestHeader: any = {
    Authorization: getToken(),
    'Tenant-Id': getTenantId.value,
  };

  const indicatorOpen = ref(false);
  const groupInfo = ref({
    groupCode: '',
    resourceInterfaceId: '3',
    jsConvert: false,
    indexCodes: '',
  });
  const multiple = ref(false);

  const openModal = (code, groupCode) => {
    groupInfo.value.indexCodes = code;
    groupInfo.value.groupCode = groupCode;
    indicatorOpen.value = true;
  };
</script>
<style lang="less" scoped>
  .water-in {
    position: relative;

    .icon {
      .width-prop(45);
      .width-prop(45, height);
      cursor: pointer;

      img {
        width: 100%;
      }
    }

    .water-card {
      position: absolute;
      .width-prop(53, left);
      top: 0;
      .width-prop(448);
      background: linear-gradient(180deg, #015848 0%, #025849 100%);
      box-shadow: 0px 4px 6px 0px #1d2e1e;
      border-radius: 4px;
      border: 1px solid #9ec5bf;
      .width-prop(18, padding-top);
      .width-prop(24, padding-left);
      .width-prop(24, padding-right);
      .width-prop(24, padding-bottom);

      .water-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
          display: flex;
          align-items: center;

          .title-text {
            font-weight: 600;
            .font-size(18);
            line-height: 1;
          }
        }

        .close {
          cursor: pointer;
        }
      }

      .water-card-content {
        .width-prop(18, padding-top);

        .table-header {
          display: flex;
          align-items: center;
          background-color: rgba(6, 202, 166, 0.4);
          border-radius: 4px;
          .width-prop(44, height);
          .px2vw(24);
          padding: 0 @vw;
          .font-size(16);
          line-height: 1;

          .header-item {
            &:nth-child(1) {
              width: 36%;
            }

            &:nth-child(2) {
              width: 26%;
            }

            &:nth-child(3) {
              width: 26%;
            }

            &:nth-child(4) {
              width: 14%;
            }
          }
        }

        .table-body {
          .item {
            background-color: rgba(0, 43, 33, 0.56);
            border: 1px solid rgba(255, 255, 255, 0.16);
            border-radius: 4px;
            .width-prop(44, height);
            .px2vw(24);
            padding: 0 @vw;
            .font-size(16);
            line-height: 1;
            display: flex;
            align-items: center;
            .width-prop(8, margin-top);

            .name {
              width: 34%;
              .width-prop(8, padding-right);
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }

            .value {
              width: 26%;
              .width-prop(8, padding-right);
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              cursor: pointer;
            }

            .value1 {
              width: 26%;
              .width-prop(8, padding-right);
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              cursor: pointer;
            }

            .unit {
              width: 14%;
              .width-prop(8, padding-right);
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
          }
        }

        .empty-container {
          .width-prop(18, padding-top);
        }
      }
    }
  }
</style>
