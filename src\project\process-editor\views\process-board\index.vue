<template>
  <BasicWrapper>
    <currentCom style="margin: 0; border-width: 0" />
  </BasicWrapper>
</template>

<script setup lang="ts">
  import { shallowRef } from 'vue';
  import { useRoute } from 'vue-router';
  import BasicWrapper from './components/BasicWrapper.vue';

  import { defineAsyncComponent } from 'vue';

  let controlRoomCom = defineAsyncComponent(() => import('../control-room/index.vue'));
  let previewInnerCom = defineAsyncComponent(() => import('../previewInner/index.vue'));

  let ComsMap = {
    ControlRoom: controlRoomCom,
    PreviewInner: previewInnerCom,
  };
  let currentCom = shallowRef(null);

  const route = useRoute();

  // 匹配动态组件
  function matchCom() {
    let componentName = route.meta.component;
    if (!componentName) return;
    currentCom.value = ComsMap[componentName];
  }

  matchCom();
</script>

<style lang="scss" scoped></style>
