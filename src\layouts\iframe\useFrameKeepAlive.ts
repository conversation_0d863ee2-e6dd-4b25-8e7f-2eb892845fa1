import type { AppRouteRecordRaw } from '/@/router/types';

import { computed, toRaw, unref } from 'vue';

// import { useMultipleTabStore } from '/@/store/modules/multipleTab';

import { uniqBy } from 'lodash-es';

import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';

import { useRouter } from 'vue-router';

import { usePermissionStore } from '/@/store/modules/permission';

export function useFrameKeepAlive() {
  const router = useRouter();
  const { currentRoute } = router;
  const { getShowMultipleTab } = useMultipleTabSetting();
  // const tabStore = useMultipleTabStore();
  const getFramePages = computed(() => {
    const permissionStore = usePermissionStore();
    let ret: AppRouteRecordRaw[] = [];

    try {
      if (permissionStore.getFramePagesList.length) {
        ret = permissionStore.getFramePagesList;
      } else {
        ret = getAllFramePages(toRaw(router.getRoutes()) as unknown as AppRouteRecordRaw[]) || [];
      }
    } catch (error) {
      console.log('error0', error);
      throw error;
    }
    return ret;
  });

  // const getOpenTabList = computed((): string[] => {
  //   return tabStore.getTabList.reduce((prev: string[], next) => {
  //     if (next.meta && Reflect.has(next.meta, 'frameSrc') && !next.meta.refreshIframePage) {
  //       prev.push(next.name as string);
  //     }
  //     return prev;
  //   }, []);
  // });

  function getAllFramePages(routes: AppRouteRecordRaw[]): AppRouteRecordRaw[] {
    let res: AppRouteRecordRaw[] = [];
    for (const route of routes) {
      const { meta: { frameSrc, isBlank } = {}, children } = route;
      if (frameSrc && !isBlank) {
        res.push(route);
      }
      if (children && children.length) {
        res.push(...getAllFramePages(children));
      }
    }
    res = uniqBy(res, 'name');
    return res;
  }

  function showIframe(item: AppRouteRecordRaw) {
    const flag = item.name === unref(currentRoute).name;
    // console.log(
    //   '%cflag===>63： ',
    //   'background: rgb(25, 197, 237,.6); color: #ff5025; font-size:18px;font-weight:700',
    //   flag,
    //   item.meta,
    // );

    // if (item.meta.modelClassify == 4 || item.meta.modelClassify == 5) {
    //   const command = flag ? 'start' : 'pause';
    //   window.postMessage(
    //     {
    //       type: 'canvasRefreshControl',
    //       command,
    //     },
    //     '*',
    //   );
    // }
    return flag;
  }

  function hasRenderFrame(item: AppRouteRecordRaw) {
    if (
      !unref(getShowMultipleTab) &&
      item.meta?.isFullScreen &&
      currentRoute.value.meta.isFullScreen
    ) {
      return router.currentRoute.value.name === item.name;
    }
    // return unref(getOpenTabList).includes(item.name);
    return router.currentRoute.value.name === item.name;
  }

  return { hasRenderFrame, getFramePages, showIframe, getAllFramePages, getShowMultipleTab };
}
