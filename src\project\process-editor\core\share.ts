import { cloneDeep } from 'lodash-es';
import { type Pen, Meta2d } from 'hlxb-meta2d-core';
import { colorIsDark } from '../utils/color';
import { addResourcePrefix } from '/@process-editor/utils/index';
import { ImgPen } from './diagrams/common';

/**
 * 获取图片的大小
 * @param {string} url 图片地址
 * @returns {Promise} 图片的宽高
 */
export function getImgSize(url: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve) => {
    const img = new Image();
    img.src = url;
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
  });
}

/**
 *  data:[
 * {name: '猫科动物, list: [{img: '狮子.png', width: 0, height: 0}, {{img: '老虎.png', width: 0, height: 0}}]},
 * {name: '犬科动物, list: [{img: '狗.png', width: 0, height: 0}, {{img: '狐狸.png', width: 0, height: 0}}]}
 * ]
 *  获取图片组大小
 */
export async function getImgListSize(data: Recordable[]) {
  const result = cloneDeep(data);

  for (let i = 0; i < result.length; i++) {
    const list = result[i].list;
    for (let j = 0; j < list.length; j++) {
      const { img } = list[j];
      const { width, height } = await getImgSize(img);
      list[j].width = width;
      list[j].height = height;
    }
  }
  return result;
}

/**
 * 是否是旧的图元
 * @param activePen 当前图元
 * @param pens 所有图元
 * @returns {boolean} 是否是旧的图元
 */
export function isOldPen(activePen: Pen, pens: Pen[]) {
  const isOld = pens.find((i) => i.id === activePen?.id);

  return Boolean(isOld);
}

// 将rgba颜色转换为16进制
export function rgbToHex(color: string) {
  const reg = /^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+))?\)$/;
  const result = color.match(reg);
  if (!result) {
    return color;
  }
  const r = parseInt(result[1]);
  const g = parseInt(result[2]);
  const b = parseInt(result[3]);
  return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
}

/**
 * 判断颜色是否是light颜色
 * @param color 颜色
 */
export function getTheme(color: string) {
  const hexColor = rgbToHex(color);

  return colorIsDark(hexColor) ? 'dark' : 'light';
}

export function isNumeric(val: string) {
  return typeof +val === 'number';
}

const imgTypes = ['gif', 'image', '图片'];
export function isImgPen(pen: Pen) {
  if (!pen) return false;
  // @ts-ignore
  return imgTypes.includes(pen.name || '') || (pen._isImg as unknown as boolean);
}

export function linkImgPenFunc(oldPen: ImgPen, metaIns: Meta2d) {
  const pen = metaIns.findOne(oldPen!.id as string) as unknown as ImgPen;
  if (!pen) return;

  const pipelineForm = pen.pipelineForm;

  if (!metaIns) return;
  const fileList = pipelineForm?.fileList;

  const pens = metaIns.data().pens;

  let gifUrl = '';
  if (fileList?.length) gifUrl = addResourcePrefix(fileList[0].url);

  pen._playback = !pen._playback;

  let image = addResourcePrefix(pipelineForm?.image);

  if (pen.image && isImgPen(pen)) {
    if (!pen._image) pen._image = pen.image;
    image = pen._image;
  }

  const __imgStatus = pen._imgStatus || 0;
  if (__imgStatus == 1) {
    image = gifUrl;
  }

  const isGif = (image || '').endsWith('.gif');
  const name = isGif ? 'gif' : 'image';

  const obj: Indexable = {
    id: pen.id,
  };
  if (fileList?.length) {
    obj.image = image;
    obj.name = name;
  }
  metaIns.setValue(obj);
  const ids = pipelineForm?.ids;
  if (!ids || !ids.length) return;
  const lines = pens.filter((i) => ids.includes(i.id));

  lines.forEach((p) => {
    const autoPlay = pen._playback;

    metaIns?.setValue({
      id: p.id,
      autoPlay,
      keepAnimateState: pen._playback,
    });

    if (pen._playback) {
      metaIns?.startAnimate(p.id);
    } else {
      metaIns?.stopAnimate(p.id);
    }
  });
}

// export function linkImgPenFunc(pen: ImgPen, metaIns?: Meta2d) {
//   const pipelineForm = pen.pipelineForm;

//   const ids = pipelineForm?.ids;
//   if (!metaIns || !pipelineForm?.ids.length) return;
//   const fileList = pipelineForm?.fileList;

//   const pens = metaIns.data().pens;
//   const lines = pens.filter((i) => ids.includes(i.id));

//   let gifUrl = '';
//   if (fileList?.length) gifUrl = addResourcePrefix(fileList[0].url);

//   pen._playback = !pen._playback;

//   let image = pipelineForm?.image;

//   if (pen.image && isImgPen(pen)) {
//     if (!pen._image) pen._image = pen.image;
//     image = pen._image;
//   }

//   const __lineStatus = pen._lineStatus || 0;

//   if (__lineStatus == 0) {
//     image = pen._playback ? gifUrl : image;
//   } else if (__lineStatus == 1) {
//     image = pen._playback ? image : gifUrl;
//   }

//   const isGif = (image || '').endsWith('.gif');
//   const name = isGif ? 'gif' : 'image';

//   const obj: Indexable = {
//     id: pen.id,
//   };
//   if (fileList?.length) {
//     obj.image = image;
//     obj.name = name;
//   }
//   metaIns.setValue(obj);

//   lines.forEach((p) => {
//     let autoPlay = pen._playback ? !p.autoPlay : p.autoPlay;
//     if (__lineStatus == 1) {
//       autoPlay = pen._playback ? p.autoPlay : !p.autoPlay;
//     }

//     metaIns?.setValue({
//       id: p.id,
//       autoPlay,
//       keepAnimateState: pen._playback,
//     });

//     if (pen._playback) {
//       metaIns?.startAnimate(p.id);
//     } else {
//       metaIns?.stopAnimate(p.id);
//     }
//   });
// }
