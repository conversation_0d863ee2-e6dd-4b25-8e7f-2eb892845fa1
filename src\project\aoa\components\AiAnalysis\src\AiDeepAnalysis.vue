<template>
  <div class="ai-box h-full" ref="aiBoxRef">
    <div :class="['ai-content', { thickness: modalType }]">
      <div class="ai-question-box flex flex-row-reverse" v-if="questionTitle && step >= 1">
        <div class="ai-question p-3">{{ questionTitle }}</div>
      </div>
      <div class="ai-answer flex mt-6" v-if="step >= 2">
        <div class="logo-box flex items-center justify-center">
          <img :src="deepseekLogo" alt="" />
        </div>
        <div class="ai-answer-right flex-1 pl-3">
          <div class="depth-des flex items-center">
            <img class="mr-1" :src="deeplyLogo" alt="" />
            <span>{{ thinkDes }}</span>
            <!-- <span v-if="!loading">（用时{{ duration }}s）</span> -->
            <Icon
              v-if="aiData.deepAnalysis.length"
              color="#fff"
              :icon="isShow ? 'icon-park-outline:up' : 'icon-park-outline:down'"
              style="cursor: pointer; margin-left: 0.2em"
              @click="isShow = !isShow"
            />
          </div>
          <template v-if="aiData.deepAnalysis.length">
            <div
              :class="['deep-analysis', 'pl-2.5', 'markdown', { hidden: !isShow }]"
              v-html="renderMdText(aiData.deepAnalysis)"
            ></div>
          </template>
          <template v-if="aiData.analysisContent.length">
            <div class="explain markdown" v-html="renderMdText(aiData.analysisContent)"></div>
          </template>
          <div class="relative mt-6" style="width: 24px" v-loading="loading"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import deepseekLogo from '/@/assets/images/deepseek-logo_white.png';
  import deeplyLogo from '/@/assets/images/deeply-logo_white.png';
  import { setTimer } from './helper/index';
  import MarkdownIt from 'markdown-it';
  import { Icon } from '/@/components/Icon';

  const emits = defineEmits(['setScroll']);

  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    speed: {
      type: Number,
      default: 1,
    },
    aiQuestion: {
      type: String,
      default: '',
    },
    questionTitle: {
      type: String,
      default: '',
    },
    aiData: {
      type: Object,
      default: () => {
        return {
          deepAnalysis: '',
          analysisContent: '',
        };
      },
    },
    duration: {
      type: Number,
      default: 0,
    },
    modalType: {
      type: String,
      default: '',
    },
  });

  const aiBoxRef = ref<HTMLElement>();
  const step = ref(0); // 0: init, 1: deep analysis, 2: result
  // const time = ref(0);
  const finallyed = ref(false);
  const hasScroll = ref(false);

  const markdownRender = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
  });

  const thinkDes = computed(() => {
    return props.loading ? '思考中...' : '已深度思考';
  });
  const isShow = ref(true);

  const initFun = async () => {
    await setTimer(500 * props.speed, null);
    step.value++;
    await setTimer(300 * props.speed, null);
    step.value++;
    finallyed.value = true;
  };

  function renderMdText(text) {
    return markdownRender.render(text);
  }

  watch(
    () => props.aiData,
    () => {
      if (!hasScroll.value) {
        const pH = document.querySelector('.ai-analysis-modal .warp-box')?.clientHeight ?? 0;
        const cH = aiBoxRef.value?.scrollHeight ?? 0;
        if (cH > pH) {
          hasScroll.value = true;
          emits('setScroll');
        }
      }
    },
    { deep: true },
  );

  onMounted(() => {
    initFun();
  });
</script>
<style lang="less" scoped>
  .ai-content {
    color: #fff;
    padding-bottom: 50px;
    text-align: justify;

    .logo-box {
      .px2vw(40);
      width: @vw;
      height: @vw;
      border: 1px solid rgba(158, 197, 191, 0.72);
      border-radius: 50%;

      img {
        .px2vw(24);
        width: @vw;
        height: @vw;
      }
    }

    .ai-question-box {
      .ai-question {
        .px2vw(40);
        width: calc(100% - @vw - 0.75rem);
        .font-size(16);
        .width-prop(24, line-height);
        border-radius: 4px;
        text-align: justify;
        background: rgba(0, 43, 25, 0.56);
        border: 1px solid rgba(255, 255, 255, 0.16);
        color: #fff;
      }
    }

    .ai-answer-right {
      :deep(.full-loading) {
        background-color: transparent !important;
      }

      .depth-des {
        width: fit-content;
        .width-prop(36, height);
        .px2vw(12);
        margin: 2px 0;
        padding: 0 @vw;
        border-radius: 4px;
        background: rgba(0, 43, 25, 0.56);
        border: 1px solid rgba(255, 255, 255, 0.16);
        .font-size(15);
        color: #fff;

        img {
          .px2vw(16);
          width: @vw;
          height: @vw;
        }
      }

      .deep-analysis {
        .px2vw(10);
        color: rgba(255, 255, 255, 0.88);
        text-align: justify;
        .width-prop(22, line-height);
        margin: @vw 0;
        .font-size(15);
        border-left: 2px solid rgba(255, 255, 255, 0.4);

        &.hidden {
          height: 0;
          transition: height 0.1s ease-in-out;
          overflow: hidden;
        }
      }

      .explain {
        margin-top: 0.5em;
        .font-size(15);
        .width-prop(22, line-height);
        color: rgba(255, 255, 255, 1);
      }

      :deep(.markdown p) {
        .px2vw(10);
        margin: @vw 0 !important;
        .width-prop(22, line-height) !important;
      }

      :deep(.markdown li) {
        .width-prop(24, line-height) !important;
      }

      :deep(.markdown hr) {
        margin-top: 0.5em;
      }
    }

    &.thickness {
      .logo-box {
        background: #015848;
        box-shadow: inset 2px 2px 2px 0px #05231d, inset -1px -1px 2px 0px #8de7d6;
        border: 1px solid #dafff8;
      }

      .ai-question-box {
        .ai-question {
          background: rgba(6, 134, 107, 0.4);
          box-shadow: inset 1px 2px 4px 0px #000e0b, inset 0px -1px 4px 0px #b2fff1;
          border-radius: 4px;
          border: none;
        }
      }

      .ai-answer-right {
        .depth-des {
          background: rgba(6, 134, 107, 0.4);
          box-shadow: inset 1px 2px 4px 0px #000e0b, inset 0px -1px 4px 0px #b2fff1;
          border-radius: 4px;
          border: none;
        }
      }
    }
  }
</style>
