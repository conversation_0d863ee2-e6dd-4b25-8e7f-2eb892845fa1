node_modules
dist
# public
*.zip
.DS_Store
.npmrc
.cache

tests/server/static
tests/server/static/upload

.local
# local env files
.env.local
.env.*.local
.eslintcache

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
# .vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

package-lock.json
pnpm-lock.yaml

.history
.env
baoqibg.png
facility-static-bg.png