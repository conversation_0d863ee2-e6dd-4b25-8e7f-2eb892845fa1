<template>
  <div>
    <BasicModal
      @register="registerModal"
      :title="title"
      :width="800"
      :bodyStyle="{ padding: 0 }"
      @cancel="emits('cancel')"
    >
      <div v-if="createTemplateBtnShow" class="text-right pb-3">
        <span v-if="createTemplateHelp" style="margin-right: 8px">
          <BasicHelp placement="top" :text="createTemplateHelp" />
        </span>
        <a-button type="second" @click="emits('createTemplate')"> 生成模版 </a-button>
      </div>
      <JsonEditor
        @change="emits('change', $event)"
        :modelValue="value"
        style="height: 400px"
        ref="editorRef"
        :mode="mode"
      />
      <template #footer>
        <a-button @click="emits('cancel')"> 取消 </a-button>
        <a-button @click="emits('confirm')" type="primary"> 确定 </a-button>
      </template>
    </BasicModal>
  </div>
</template>
<script lang="ts" setup>
  import { BasicHelp } from '/@/components/Basic';
  import JsonEditor from './JsonEditor.vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { ref, watch } from 'vue';

  const emits = defineEmits(['cancel', 'confirm', 'change', 'createTemplate']);
  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: 'javascript',
    },
    activePen: {
      type: Object,
      default: () => {},
    },
    value: {
      type: String,
      default: '',
    },
    mode: {
      type: String,
      default: 'javascript',
    },
    createTemplateBtnShow: {
      type: Boolean,
      default: true,
    },
    createTemplateHelp: {
      type: [String, Array],
    },
  });

  const [registerModal, { openModal, closeModal }] = useModal();
  watch(
    () => props.show,
    (v) => {
      if (v) openModal();
      else closeModal();
    },
  );

  const editorRef = ref(null);
  const setValue = (v) => {
    editorRef.value?.setValue(v);
    editorRef.value?.formatDocument();
  };
  defineExpose({
    setValue,
  });
</script>
<style lang="less" scoped></style>
