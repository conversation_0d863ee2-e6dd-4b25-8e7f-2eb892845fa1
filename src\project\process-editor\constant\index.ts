export const PRODUCT_DATA_KIT = 'ProductDataKit'; // 生产套件
export const PRODUCT_CONTROL_KIT = 'ProductControlKit'; // 生产下控套件
export const SIMPLE_VEDIO_KIT = 'SimpleVedioKit'; // 单视频套件
export const MUTI_VEDIO_KIT = 'MutiVedioKit'; // 多视频套件
export const VIDEO_KIT = 'VideoKit'; // 视频套件
export const COMMON_KIT = 'CommonKit'; // 数据套件
export const SMALL_SEMAPHORE = 'SmallSemaphore'; // 信号量小元件
export const PROCESS_KIT = 'ProcessKit'; // 工艺套件
export const TABLE_KIT = 'TableKit'; // table套件
export const CHART_KIT = 'ChartKit'; // Chart套件
export const LIQUID_LEVEL_KIT = 'liquidLevelKit'; // 液位套件
export const PIPELINE_KIT = 'PipeLineKit'; // 管线套件

export const KIT_ENUM = {
  PRODUCT_DATA_KIT,
  PRODUCT_CONTROL_KIT,
  SIMPLE_VEDIO_KIT,
  MUTI_VEDIO_KIT,
  VIDEO_KIT,
  COMMON_KIT,
  SMALL_SEMAPHORE,
  PROCESS_KIT,
  TABLE_KIT,
  CHART_KIT,
  LIQUID_LEVEL_KIT,
  PIPELINE_KIT,
};
export const INDICATOR_TYPE_ENUM = {
  NUMBER: 'number',
  TEXT: 'text',
  SWITCH: 'switch',
} as const;

export const DISPLAY_MODE_ENUM = {
  SHOW: 0,
  HIDDEN: 2,
} as const;

export const indicatorTypeOptions = [
  { label: '数值', value: INDICATOR_TYPE_ENUM.NUMBER },
  { label: '文本', value: INDICATOR_TYPE_ENUM.TEXT },
  { label: '开关', value: INDICATOR_TYPE_ENUM.SWITCH },
];

export enum EXPRESSION_TYPE_ENUM {
  REGULAR = 0,
  INDICATOR = 10,
  RULE = 20,
}
export const INDICATOR_NORMAL_VALUE = 'normal';

export const CONTROL_SWITCH_DEFAULT_SETTING = {
  onClick: () => {},
  name: 'switch',
  onColor: '#01a132',
  offColor: '#FFFFFF47',
  width: 40,
  height: 20,
};
export const VIDEO_IMG_WIDTH = 30;
export const VIDEO_IMG_HEIGHT = 42;
export const DEVICE_CHANGE_SWITCH_API = '/data/data/sync/change-switch';

// 权限码
export const PERMISSION_CODES = {
  PRODUCT_CONTROL: 'PRODUCT-CONTROL', // 下控权限
};

export const SYSTEM_PARAMS = {
  smsCode: 'smsCode', // 通用手机验证码，立马通过
};

const echartsBaseColor = [
  '#37B9FF',
  '#2CFFF1',
  '#D1F9FF',
  '#8CFC9B',
  '#F6EE60',
  '#E551FF',
  '#EBA4FF',
  '#877CFF',
];
export const echartsColor = [...echartsBaseColor, ...echartsBaseColor, ...echartsBaseColor];
