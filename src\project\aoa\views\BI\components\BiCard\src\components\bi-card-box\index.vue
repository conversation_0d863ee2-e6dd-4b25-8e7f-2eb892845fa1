<template>
  <div :class="['box-container', `direction-${direction}`]">
    <div class="container-header" :style="`background-image: url(${icon})`">
      <div class="container-header-left">
        <div class="title-text">
          {{ title }}
          <slot name="title-slot"></slot>
        </div>
      </div>
      <slot name="header-right"></slot>
    </div>
    <div class="container-content">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import icon from '/@aoa/views/BI/assets/images/card_title_icon.png';

  defineProps({
    title: String,
    direction: {
      type: String,
      default: 'left',
    },
  });
</script>

<style lang="less" scoped>
  .box-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: inset 4px -3px 4px 0px rgba(3, 33, 25, 1),
      inset -2px 2px 4px 0px rgba(162, 255, 239, 1);
    background: linear-gradient(to bottom, rgba(1, 117, 96, 0.26), rgba(1, 90, 74, 0.64));

    &.direction-right {
      box-shadow: inset 2px 2px 4px 0px rgba(162, 255, 239, 1),
        inset -3px -4px 4px 0px rgba(3, 33, 25, 1);
    }

    .container-header {
      padding-right: 11px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      background-repeat: no-repeat;
      background-position: left bottom;
      background-size: 100% 100%;

      .container-header-left {
        .title-text {
          font-family: Alimama ShuHeiTi;
          color: #fff;
          padding-left: 42px;
          font-weight: 500;
          // font-size: 16px;
          .font-size(16);
          line-height: 1;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          display: flex;
          align-items: center;
        }
      }
    }

    .container-content {
      height: calc(100% - 40px);
      overflow-y: overlay;
      overflow-x: hidden;
    }
  }

  @media screen and (min-width: 1800px) {
    .box-container {
      .container-header {
        padding-right: 16px;
      }
    }
  }

  @media screen and (min-width: 2000px) {
    .box-container {
      .container-header {
        .height-prop(48, height);

        .container-header-left {
          .title-text {
            .width-prop(44, padding-left);
            .font-size(18);
          }
        }
      }

      .container-content {
        .px2vh(48);
        height: calc(100% - @vh);
      }
    }
  }
</style>
